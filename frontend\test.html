<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Politica Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .hero {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(to right, #1e3a8a, #1d4ed8);
            color: white;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .hero h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            border: none;
            cursor: pointer;
            margin: 0 0.5rem;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            padding: 2rem 0;
        }
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #111827;
        }
        .feature-card p {
            color: #6b7280;
            margin-bottom: 1rem;
        }
        .nav {
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .nav-links a {
            text-decoration: none;
            color: #374151;
            font-weight: 500;
        }
        .nav-links a:hover {
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <h1 style="margin: 0; color: #1e3a8a;">Politica</h1>
                <ul class="nav-links">
                    <li><a href="#knowledge">Knowledge Base</a></li>
                    <li><a href="#discussions">Discussions</a></li>
                    <li><a href="#debates">Live Debates</a></li>
                    <li><a href="#research">Research</a></li>
                    <li><a href="#login" class="btn btn-primary">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="hero">
            <h1>Welcome to Politica</h1>
            <p>Your comprehensive platform for political education, discussion, and research</p>
            <div>
                <a href="#knowledge" class="btn btn-primary">Explore Knowledge Base</a>
                <a href="#discussions" class="btn btn-secondary">Join Discussions</a>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>📚 Knowledge Base</h3>
                <p>Multi-level political education content from basic to advanced concepts.</p>
                <a href="#knowledge" style="color: #3b82f6; font-weight: 500;">Learn More →</a>
            </div>

            <div class="feature-card">
                <h3>💬 Discussion Forums</h3>
                <p>Engage in thoughtful political discussions with moderated threads.</p>
                <a href="#discussions" style="color: #3b82f6; font-weight: 500;">Join Discussion →</a>
            </div>

            <div class="feature-card">
                <h3>🎙️ Live Debates</h3>
                <p>Participate in scheduled live video debates on current political topics.</p>
                <a href="#debates" style="color: #3b82f6; font-weight: 500;">Watch Debates →</a>
            </div>

            <div class="feature-card">
                <h3>📄 Research Repository</h3>
                <p>Access and submit peer-reviewed political research papers.</p>
                <a href="#research" style="color: #3b82f6; font-weight: 500;">Browse Research →</a>
            </div>
        </div>

        <div style="text-align: center; padding: 4rem 0; background-color: #374151; color: white; margin: 2rem -1rem 0; border-radius: 8px;">
            <h2>Ready to Get Started?</h2>
            <p>Join our community of informed citizens and contribute to meaningful political discourse.</p>
            <a href="#register" class="btn btn-primary">Create Account</a>
        </div>
    </div>

    <script>
        // Simple navigation simulation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Politica Test Page Loaded Successfully!');
            
            // Add click handlers for navigation
            document.querySelectorAll('a[href^="#"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = this.getAttribute('href').substring(1);
                    alert(`Navigation to ${target} would happen here in the React app.`);
                });
            });
        });
    </script>
</body>
</html>
