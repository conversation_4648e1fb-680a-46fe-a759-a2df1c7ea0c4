require('dotenv').config();
const bcrypt = require('bcrypt');
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'POLITICA_AUG',
  password: process.env.DB_PASSWORD || 'Rayvical',
  port: process.env.DB_PORT || 5432,
});

async function addTestData() {
  try {
    console.log('🔄 Adding test data...');
    
    // Hash the password
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // Check if user exists and update or insert
    let userResult;
    try {
      userResult = await pool.query(`
        INSERT INTO users (username, email, password_hash, role, verified_status, created_at)
        VALUES ($1, $2, $3, $4, $5, NOW())
        RETURNING id, username, email
      `, ['testuser', '<EMAIL>', passwordHash, 'verified', true]);
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        // User exists, update it
        userResult = await pool.query(`
          UPDATE users SET
            password_hash = $1,
            verified_status = $2,
            role = $3
          WHERE email = $4 OR username = $5
          RETURNING id, username, email
        `, [passwordHash, true, 'verified', '<EMAIL>', 'testuser']);
      } else {
        throw error;
      }
    }
    
    console.log('✅ Test user created/updated:', userResult.rows[0]);
    
    const userId = userResult.rows[0].id;
    
    // Insert test debates
    const debate1Result = await pool.query(`
      INSERT INTO live_debates (title, description, topic, scheduled_at, status, moderator_id, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW())
      ON CONFLICT DO NOTHING
      RETURNING id, title, status
    `, [
      'Climate Change Policy Debate',
      'A comprehensive discussion on climate change policies and their economic impact',
      'Environmental Policy',
      new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
      'live',
      userId
    ]);
    
    if (debate1Result.rows.length > 0) {
      console.log('✅ Live debate created:', debate1Result.rows[0]);
    } else {
      console.log('ℹ️ Live debate already exists');
    }
    
    const debate2Result = await pool.query(`
      INSERT INTO live_debates (title, description, topic, scheduled_at, status, moderator_id, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW())
      ON CONFLICT DO NOTHING
      RETURNING id, title, status
    `, [
      'Healthcare Reform Discussion',
      'Exploring different approaches to healthcare reform and universal coverage',
      'Healthcare Policy',
      new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
      'scheduled',
      userId
    ]);
    
    if (debate2Result.rows.length > 0) {
      console.log('✅ Scheduled debate created:', debate2Result.rows[0]);
    } else {
      console.log('ℹ️ Scheduled debate already exists');
    }
    
    // Check total debates
    const totalDebates = await pool.query('SELECT COUNT(*) as count FROM live_debates');
    console.log(`📊 Total debates in database: ${totalDebates.rows[0].count}`);
    
    console.log('🎉 Test data setup complete!');
    console.log('📝 You can now login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
  } catch (error) {
    console.error('❌ Error adding test data:', error);
  } finally {
    await pool.end();
  }
}

addTestData();
