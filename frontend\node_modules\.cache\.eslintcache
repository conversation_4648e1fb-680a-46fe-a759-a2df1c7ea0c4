[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js": "14", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js": "15", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js": "16", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js": "17", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js": "18", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js": "19", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\SocialFeed.js": "20", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CreatePost.js": "21", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\PostCard.js": "22", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\TrendingSidebar.js": "23", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CommentsSection.js": "24", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\AdvancedFilters.js": "25", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\ContentScheduler.js": "26", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\ProfessionalHeader.js": "27", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\AnalyticsDashboard.js": "28"}, {"size": 535, "mtime": 1748846816117, "results": "29", "hashOfConfig": "30"}, {"size": 1780, "mtime": 1748944525018, "results": "31", "hashOfConfig": "30"}, {"size": 362, "mtime": 1748846816119, "results": "32", "hashOfConfig": "30"}, {"size": 7700, "mtime": 1748944582225, "results": "33", "hashOfConfig": "30"}, {"size": 15489, "mtime": 1748932719354, "results": "34", "hashOfConfig": "30"}, {"size": 2338, "mtime": 1748936947307, "results": "35", "hashOfConfig": "30"}, {"size": 7733, "mtime": 1748851053411, "results": "36", "hashOfConfig": "30"}, {"size": 8341, "mtime": 1748847125043, "results": "37", "hashOfConfig": "30"}, {"size": 10301, "mtime": 1748847718439, "results": "38", "hashOfConfig": "30"}, {"size": 19221, "mtime": 1748932654063, "results": "39", "hashOfConfig": "30"}, {"size": 16560, "mtime": 1748932590469, "results": "40", "hashOfConfig": "30"}, {"size": 21941, "mtime": 1748940660902, "results": "41", "hashOfConfig": "30"}, {"size": 505, "mtime": 1748846957692, "results": "42", "hashOfConfig": "30"}, {"size": 3371, "mtime": 1748850865139, "results": "43", "hashOfConfig": "30"}, {"size": 4129, "mtime": 1748850838248, "results": "44", "hashOfConfig": "30"}, {"size": 8482, "mtime": 1748937880129, "results": "45", "hashOfConfig": "30"}, {"size": 9165, "mtime": 1748937971196, "results": "46", "hashOfConfig": "30"}, {"size": 8404, "mtime": 1748938115338, "results": "47", "hashOfConfig": "30"}, {"size": 17624, "mtime": 1748940270799, "results": "48", "hashOfConfig": "30"}, {"size": 14603, "mtime": 1748946447613, "results": "49", "hashOfConfig": "30"}, {"size": 11876, "mtime": 1748944154469, "results": "50", "hashOfConfig": "30"}, {"size": 9903, "mtime": 1748944099390, "results": "51", "hashOfConfig": "30"}, {"size": 10517, "mtime": 1748944213691, "results": "52", "hashOfConfig": "30"}, {"size": 11818, "mtime": 1748944281853, "results": "53", "hashOfConfig": "30"}, {"size": 12291, "mtime": 1748946185479, "results": "54", "hashOfConfig": "30"}, {"size": 15407, "mtime": 1748946252224, "results": "55", "hashOfConfig": "30"}, {"size": 11833, "mtime": 1748946130960, "results": "56", "hashOfConfig": "30"}, {"size": 15437, "mtime": 1748946317082, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", ["142"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", ["143", "144"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", ["145", "146", "147", "148", "149"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js", ["150"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js", ["151", "152", "153", "154"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\SocialFeed.js", ["155"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CreatePost.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\PostCard.js", ["156"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\TrendingSidebar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CommentsSection.js", ["157", "158"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\AdvancedFilters.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\ContentScheduler.js", ["159"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\ProfessionalHeader.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\AnalyticsDashboard.js", [], [], {"ruleId": "160", "severity": 1, "message": "161", "line": 18, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 18, "endColumn": 15}, {"ruleId": "160", "severity": 1, "message": "164", "line": 10, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 10, "endColumn": 23}, {"ruleId": "160", "severity": 1, "message": "161", "line": 19, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 19, "endColumn": 15}, {"ruleId": "160", "severity": 1, "message": "165", "line": 12, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 12, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "161", "line": 23, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 23, "endColumn": 15}, {"ruleId": "160", "severity": 1, "message": "166", "line": 25, "column": 5, "nodeType": "162", "messageId": "163", "endLine": 25, "endColumn": 16}, {"ruleId": "160", "severity": 1, "message": "167", "line": 26, "column": 5, "nodeType": "162", "messageId": "163", "endLine": 26, "endColumn": 18}, {"ruleId": "160", "severity": 1, "message": "168", "line": 29, "column": 5, "nodeType": "162", "messageId": "163", "endLine": 29, "endColumn": 22}, {"ruleId": "169", "severity": 1, "message": "170", "line": 188, "column": 6, "nodeType": "171", "endLine": 188, "endColumn": 19, "suggestions": "172"}, {"ruleId": "160", "severity": 1, "message": "166", "line": 23, "column": 19, "nodeType": "162", "messageId": "163", "endLine": 23, "endColumn": 30}, {"ruleId": "169", "severity": 1, "message": "173", "line": 36, "column": 30, "nodeType": "162", "endLine": 36, "endColumn": 37}, {"ruleId": "169", "severity": 1, "message": "174", "line": 40, "column": 6, "nodeType": "171", "endLine": 40, "endColumn": 17, "suggestions": "175"}, {"ruleId": "169", "severity": 1, "message": "176", "line": 72, "column": 6, "nodeType": "171", "endLine": 72, "endColumn": 25, "suggestions": "177"}, {"ruleId": "169", "severity": 1, "message": "178", "line": 67, "column": 6, "nodeType": "171", "endLine": 67, "endColumn": 14, "suggestions": "179"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 8, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 8, "endColumn": 15}, {"ruleId": "160", "severity": 1, "message": "161", "line": 13, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 13, "endColumn": 15}, {"ruleId": "169", "severity": 1, "message": "180", "line": 17, "column": 6, "nodeType": "171", "endLine": 17, "endColumn": 22, "suggestions": "181"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 15, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 15, "endColumn": 15}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'selectedPaper' is assigned a value but never used.", "'selectedDebate' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "'activeDebates' is assigned a value but never used.", "'sendDebateMessage' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["182"], "The ref value 'peersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'peersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'initializeMedia' and 'localStream'. Either include them or remove the dependency array.", ["183"], "React Hook useEffect has missing dependencies: 'handleUserJoinedVideo', 'handleUserLeftVideo', and 'handleVideoSignal'. Either include them or remove the dependency array.", ["184"], "React Hook useEffect has a missing dependency: 'fetchPosts'. Either include it or remove the dependency array.", ["185"], "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["186"], {"desc": "187", "fix": "188"}, {"desc": "189", "fix": "190"}, {"desc": "191", "fix": "192"}, {"desc": "193", "fix": "194"}, {"desc": "195", "fix": "196"}, "Update the dependencies array to be: [user, token, addNotification]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [initializeMedia, isVisible, localStream]", {"range": "199", "text": "200"}, "Update the dependencies array to be: [socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]", {"range": "201", "text": "202"}, "Update the dependencies array to be: [fetchPosts, filter]", {"range": "203", "text": "204"}, "Update the dependencies array to be: [fetchComments, postId, sortBy]", {"range": "205", "text": "206"}, [5831, 5844], "[user, token, addNotification]", [1469, 1480], "[initializeMedia, isVisible, localStream]", [2526, 2545], "[socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]", [2386, 2394], "[fetchPosts, filter]", [565, 581], "[fetchComments, postId, sortBy]"]