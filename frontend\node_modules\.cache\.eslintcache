[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js": "14", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js": "15", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js": "16", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js": "17", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js": "18", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js": "19"}, {"size": 535, "mtime": 1748846816117, "results": "20", "hashOfConfig": "21"}, {"size": 1669, "mtime": 1748937924117, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1748846816119, "results": "23", "hashOfConfig": "21"}, {"size": 7207, "mtime": 1748938017026, "results": "24", "hashOfConfig": "21"}, {"size": 15489, "mtime": 1748932719354, "results": "25", "hashOfConfig": "21"}, {"size": 2338, "mtime": 1748936947307, "results": "26", "hashOfConfig": "21"}, {"size": 7733, "mtime": 1748851053411, "results": "27", "hashOfConfig": "21"}, {"size": 8341, "mtime": 1748847125043, "results": "28", "hashOfConfig": "21"}, {"size": 10301, "mtime": 1748847718439, "results": "29", "hashOfConfig": "21"}, {"size": 19221, "mtime": 1748932654063, "results": "30", "hashOfConfig": "21"}, {"size": 16560, "mtime": 1748932590469, "results": "31", "hashOfConfig": "21"}, {"size": 21941, "mtime": 1748940660902, "results": "32", "hashOfConfig": "21"}, {"size": 505, "mtime": 1748846957692, "results": "33", "hashOfConfig": "21"}, {"size": 3371, "mtime": 1748850865139, "results": "34", "hashOfConfig": "21"}, {"size": 4129, "mtime": 1748850838248, "results": "35", "hashOfConfig": "21"}, {"size": 8482, "mtime": 1748937880129, "results": "36", "hashOfConfig": "21"}, {"size": 9165, "mtime": 1748937971196, "results": "37", "hashOfConfig": "21"}, {"size": 8404, "mtime": 1748938115338, "results": "38", "hashOfConfig": "21"}, {"size": 17624, "mtime": 1748940270799, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", ["97"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", ["98", "99"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", ["100", "101", "102", "103", "104"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js", ["105"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js", ["106", "107", "108", "109"], [], {"ruleId": "110", "severity": 1, "message": "111", "line": 18, "column": 11, "nodeType": "112", "messageId": "113", "endLine": 18, "endColumn": 15}, {"ruleId": "110", "severity": 1, "message": "114", "line": 10, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 23}, {"ruleId": "110", "severity": 1, "message": "111", "line": 19, "column": 11, "nodeType": "112", "messageId": "113", "endLine": 19, "endColumn": 15}, {"ruleId": "110", "severity": 1, "message": "115", "line": 12, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 12, "endColumn": 24}, {"ruleId": "110", "severity": 1, "message": "111", "line": 23, "column": 11, "nodeType": "112", "messageId": "113", "endLine": 23, "endColumn": 15}, {"ruleId": "110", "severity": 1, "message": "116", "line": 25, "column": 5, "nodeType": "112", "messageId": "113", "endLine": 25, "endColumn": 16}, {"ruleId": "110", "severity": 1, "message": "117", "line": 26, "column": 5, "nodeType": "112", "messageId": "113", "endLine": 26, "endColumn": 18}, {"ruleId": "110", "severity": 1, "message": "118", "line": 29, "column": 5, "nodeType": "112", "messageId": "113", "endLine": 29, "endColumn": 22}, {"ruleId": "119", "severity": 1, "message": "120", "line": 188, "column": 6, "nodeType": "121", "endLine": 188, "endColumn": 19, "suggestions": "122"}, {"ruleId": "110", "severity": 1, "message": "116", "line": 23, "column": 19, "nodeType": "112", "messageId": "113", "endLine": 23, "endColumn": 30}, {"ruleId": "119", "severity": 1, "message": "123", "line": 36, "column": 30, "nodeType": "112", "endLine": 36, "endColumn": 37}, {"ruleId": "119", "severity": 1, "message": "124", "line": 40, "column": 6, "nodeType": "121", "endLine": 40, "endColumn": 17, "suggestions": "125"}, {"ruleId": "119", "severity": 1, "message": "126", "line": 72, "column": 6, "nodeType": "121", "endLine": 72, "endColumn": 25, "suggestions": "127"}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'selectedPaper' is assigned a value but never used.", "'selectedDebate' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "'activeDebates' is assigned a value but never used.", "'sendDebateMessage' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["128"], "The ref value 'peersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'peersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'initializeMedia' and 'localStream'. Either include them or remove the dependency array.", ["129"], "React Hook useEffect has missing dependencies: 'handleUserJoinedVideo', 'handleUserLeftVideo', and 'handleVideoSignal'. Either include them or remove the dependency array.", ["130"], {"desc": "131", "fix": "132"}, {"desc": "133", "fix": "134"}, {"desc": "135", "fix": "136"}, "Update the dependencies array to be: [user, token, addNotification]", {"range": "137", "text": "138"}, "Update the dependencies array to be: [initializeMedia, isVisible, localStream]", {"range": "139", "text": "140"}, "Update the dependencies array to be: [socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]", {"range": "141", "text": "142"}, [5831, 5844], "[user, token, addNotification]", [1469, 1480], "[initializeMedia, isVisible, localStream]", [2526, 2545], "[socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]"]