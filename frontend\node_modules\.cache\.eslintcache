[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js": "14", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js": "15", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js": "16", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js": "17", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js": "18"}, {"size": 535, "mtime": 1748846816117, "results": "19", "hashOfConfig": "20"}, {"size": 1669, "mtime": 1748937924117, "results": "21", "hashOfConfig": "20"}, {"size": 362, "mtime": 1748846816119, "results": "22", "hashOfConfig": "20"}, {"size": 7207, "mtime": 1748938017026, "results": "23", "hashOfConfig": "20"}, {"size": 15489, "mtime": 1748932719354, "results": "24", "hashOfConfig": "20"}, {"size": 2338, "mtime": 1748936947307, "results": "25", "hashOfConfig": "20"}, {"size": 7733, "mtime": 1748851053411, "results": "26", "hashOfConfig": "20"}, {"size": 8341, "mtime": 1748847125043, "results": "27", "hashOfConfig": "20"}, {"size": 10301, "mtime": 1748847718439, "results": "28", "hashOfConfig": "20"}, {"size": 19221, "mtime": 1748932654063, "results": "29", "hashOfConfig": "20"}, {"size": 16560, "mtime": 1748932590469, "results": "30", "hashOfConfig": "20"}, {"size": 19660, "mtime": 1748938249440, "results": "31", "hashOfConfig": "20"}, {"size": 505, "mtime": 1748846957692, "results": "32", "hashOfConfig": "20"}, {"size": 3371, "mtime": 1748850865139, "results": "33", "hashOfConfig": "20"}, {"size": 4129, "mtime": 1748850838248, "results": "34", "hashOfConfig": "20"}, {"size": 8482, "mtime": 1748937880129, "results": "35", "hashOfConfig": "20"}, {"size": 9165, "mtime": 1748937971196, "results": "36", "hashOfConfig": "20"}, {"size": 8404, "mtime": 1748938115338, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", ["92"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", ["93", "94"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", ["95", "96", "97", "98", "99"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js", ["100"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js", [], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 18, "column": 11, "nodeType": "103", "messageId": "104", "endLine": 18, "endColumn": 15}, {"ruleId": "101", "severity": 1, "message": "105", "line": 10, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 10, "endColumn": 23}, {"ruleId": "101", "severity": 1, "message": "102", "line": 19, "column": 11, "nodeType": "103", "messageId": "104", "endLine": 19, "endColumn": 15}, {"ruleId": "101", "severity": 1, "message": "106", "line": 11, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 11, "endColumn": 24}, {"ruleId": "101", "severity": 1, "message": "102", "line": 20, "column": 11, "nodeType": "103", "messageId": "104", "endLine": 20, "endColumn": 15}, {"ruleId": "101", "severity": 1, "message": "107", "line": 22, "column": 5, "nodeType": "103", "messageId": "104", "endLine": 22, "endColumn": 16}, {"ruleId": "101", "severity": 1, "message": "108", "line": 23, "column": 5, "nodeType": "103", "messageId": "104", "endLine": 23, "endColumn": 18}, {"ruleId": "101", "severity": 1, "message": "109", "line": 26, "column": 5, "nodeType": "103", "messageId": "104", "endLine": 26, "endColumn": 22}, {"ruleId": "110", "severity": 1, "message": "111", "line": 188, "column": 6, "nodeType": "112", "endLine": 188, "endColumn": 19, "suggestions": "113"}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'selectedPaper' is assigned a value but never used.", "'selectedDebate' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "'activeDebates' is assigned a value but never used.", "'sendDebateMessage' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], {"desc": "115", "fix": "116"}, "Update the dependencies array to be: [user, token, addNotification]", {"range": "117", "text": "118"}, [5831, 5844], "[user, token, addNotification]"]