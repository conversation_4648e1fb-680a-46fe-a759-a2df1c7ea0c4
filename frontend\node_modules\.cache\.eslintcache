[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js": "14", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js": "15"}, {"size": 535, "mtime": 1748846816117, "results": "16", "hashOfConfig": "17"}, {"size": 1513, "mtime": 1748850906027, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1748846816119, "results": "19", "hashOfConfig": "17"}, {"size": 7113, "mtime": 1748851008674, "results": "20", "hashOfConfig": "17"}, {"size": 15489, "mtime": 1748932719354, "results": "21", "hashOfConfig": "17"}, {"size": 6927, "mtime": 1748846994544, "results": "22", "hashOfConfig": "17"}, {"size": 7733, "mtime": 1748851053411, "results": "23", "hashOfConfig": "17"}, {"size": 8341, "mtime": 1748847125043, "results": "24", "hashOfConfig": "17"}, {"size": 10301, "mtime": 1748847718439, "results": "25", "hashOfConfig": "17"}, {"size": 19221, "mtime": 1748932654063, "results": "26", "hashOfConfig": "17"}, {"size": 16560, "mtime": 1748932590469, "results": "27", "hashOfConfig": "17"}, {"size": 17308, "mtime": 1748932620696, "results": "28", "hashOfConfig": "17"}, {"size": 505, "mtime": 1748846957692, "results": "29", "hashOfConfig": "17"}, {"size": 3371, "mtime": 1748850865139, "results": "30", "hashOfConfig": "17"}, {"size": 4129, "mtime": 1748850838248, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", ["77"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", ["78", "79"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", ["80", "81"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js", [], [], {"ruleId": "82", "severity": 1, "message": "83", "line": 18, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 18, "endColumn": 15}, {"ruleId": "82", "severity": 1, "message": "86", "line": 10, "column": 10, "nodeType": "84", "messageId": "85", "endLine": 10, "endColumn": 23}, {"ruleId": "82", "severity": 1, "message": "83", "line": 19, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 19, "endColumn": 15}, {"ruleId": "82", "severity": 1, "message": "87", "line": 9, "column": 10, "nodeType": "84", "messageId": "85", "endLine": 9, "endColumn": 24}, {"ruleId": "82", "severity": 1, "message": "83", "line": 17, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 17, "endColumn": 15}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'selectedPaper' is assigned a value but never used.", "'selectedDebate' is assigned a value but never used."]