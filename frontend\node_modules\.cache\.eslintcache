[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js": "14", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js": "15", "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js": "16", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js": "17", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js": "18", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js": "19", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\SocialFeed.js": "20", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CreatePost.js": "21", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\PostCard.js": "22", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\TrendingSidebar.js": "23", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CommentsSection.js": "24"}, {"size": 535, "mtime": 1748846816117, "results": "25", "hashOfConfig": "26"}, {"size": 1780, "mtime": 1748944525018, "results": "27", "hashOfConfig": "26"}, {"size": 362, "mtime": 1748846816119, "results": "28", "hashOfConfig": "26"}, {"size": 7700, "mtime": 1748944582225, "results": "29", "hashOfConfig": "26"}, {"size": 15489, "mtime": 1748932719354, "results": "30", "hashOfConfig": "26"}, {"size": 2338, "mtime": 1748936947307, "results": "31", "hashOfConfig": "26"}, {"size": 7733, "mtime": 1748851053411, "results": "32", "hashOfConfig": "26"}, {"size": 8341, "mtime": 1748847125043, "results": "33", "hashOfConfig": "26"}, {"size": 10301, "mtime": 1748847718439, "results": "34", "hashOfConfig": "26"}, {"size": 19221, "mtime": 1748932654063, "results": "35", "hashOfConfig": "26"}, {"size": 16560, "mtime": 1748932590469, "results": "36", "hashOfConfig": "26"}, {"size": 21941, "mtime": 1748940660902, "results": "37", "hashOfConfig": "26"}, {"size": 505, "mtime": 1748846957692, "results": "38", "hashOfConfig": "26"}, {"size": 3371, "mtime": 1748850865139, "results": "39", "hashOfConfig": "26"}, {"size": 4129, "mtime": 1748850838248, "results": "40", "hashOfConfig": "26"}, {"size": 8482, "mtime": 1748937880129, "results": "41", "hashOfConfig": "26"}, {"size": 9165, "mtime": 1748937971196, "results": "42", "hashOfConfig": "26"}, {"size": 8404, "mtime": 1748938115338, "results": "43", "hashOfConfig": "26"}, {"size": 17624, "mtime": 1748940270799, "results": "44", "hashOfConfig": "26"}, {"size": 11122, "mtime": 1748944032843, "results": "45", "hashOfConfig": "26"}, {"size": 11876, "mtime": 1748944154469, "results": "46", "hashOfConfig": "26"}, {"size": 9903, "mtime": 1748944099390, "results": "47", "hashOfConfig": "26"}, {"size": 10517, "mtime": 1748944213691, "results": "48", "hashOfConfig": "26"}, {"size": 11818, "mtime": 1748944281853, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", ["122"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", ["123", "124"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", ["125", "126", "127", "128", "129"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\ApiContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\AuthContext.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\contexts\\WebSocketContext.js", ["130"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\NotificationCenter.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\DebateChat.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\VideoDebate.js", ["131", "132", "133", "134"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\SocialFeed.js", ["135", "136"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CreatePost.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\PostCard.js", ["137"], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\TrendingSidebar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\CommentsSection.js", ["138", "139"], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 18, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 18, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "144", "line": 10, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 10, "endColumn": 23}, {"ruleId": "140", "severity": 1, "message": "141", "line": 19, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 19, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "145", "line": 12, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 12, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "141", "line": 23, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 23, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "146", "line": 25, "column": 5, "nodeType": "142", "messageId": "143", "endLine": 25, "endColumn": 16}, {"ruleId": "140", "severity": 1, "message": "147", "line": 26, "column": 5, "nodeType": "142", "messageId": "143", "endLine": 26, "endColumn": 18}, {"ruleId": "140", "severity": 1, "message": "148", "line": 29, "column": 5, "nodeType": "142", "messageId": "143", "endLine": 29, "endColumn": 22}, {"ruleId": "149", "severity": 1, "message": "150", "line": 188, "column": 6, "nodeType": "151", "endLine": 188, "endColumn": 19, "suggestions": "152"}, {"ruleId": "140", "severity": 1, "message": "146", "line": 23, "column": 19, "nodeType": "142", "messageId": "143", "endLine": 23, "endColumn": 30}, {"ruleId": "149", "severity": 1, "message": "153", "line": 36, "column": 30, "nodeType": "142", "endLine": 36, "endColumn": 37}, {"ruleId": "149", "severity": 1, "message": "154", "line": 40, "column": 6, "nodeType": "151", "endLine": 40, "endColumn": 17, "suggestions": "155"}, {"ruleId": "149", "severity": 1, "message": "156", "line": 72, "column": 6, "nodeType": "151", "endLine": 72, "endColumn": 25, "suggestions": "157"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 18, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 18, "endColumn": 15}, {"ruleId": "149", "severity": 1, "message": "158", "line": 57, "column": 6, "nodeType": "151", "endLine": 57, "endColumn": 14, "suggestions": "159"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 8, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 8, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "141", "line": 13, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 13, "endColumn": 15}, {"ruleId": "149", "severity": 1, "message": "160", "line": 17, "column": 6, "nodeType": "151", "endLine": 17, "endColumn": 22, "suggestions": "161"}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'selectedPaper' is assigned a value but never used.", "'selectedDebate' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "'activeDebates' is assigned a value but never used.", "'sendDebateMessage' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["162"], "The ref value 'peersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'peersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'initializeMedia' and 'localStream'. Either include them or remove the dependency array.", ["163"], "React Hook useEffect has missing dependencies: 'handleUserJoinedVideo', 'handleUserLeftVideo', and 'handleVideoSignal'. Either include them or remove the dependency array.", ["164"], "React Hook useEffect has a missing dependency: 'fetchPosts'. Either include it or remove the dependency array.", ["165"], "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["166"], {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"desc": "173", "fix": "174"}, {"desc": "175", "fix": "176"}, "Update the dependencies array to be: [user, token, addNotification]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [initializeMedia, isVisible, localStream]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [fetchPosts, filter]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [fetchComments, postId, sortBy]", {"range": "185", "text": "186"}, [5831, 5844], "[user, token, addNotification]", [1469, 1480], "[initializeMedia, isVisible, localStream]", [2526, 2545], "[socket, isVisible, handleVideoSignal, handleUserJoinedVideo, handleUserLeftVideo]", [1729, 1737], "[fetchPosts, filter]", [565, 581], "[fetchComments, postId, sortBy]"]