{"ast": null, "code": "/*! queue-microtask. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nlet promise;\nmodule.exports = typeof queueMicrotask === 'function' ? queueMicrotask.bind(typeof window !== 'undefined' ? window : global)\n// reuse resolved promise, and allocate it lazily\n: cb => (promise || (promise = Promise.resolve())).then(cb).catch(err => setTimeout(() => {\n  throw err;\n}, 0));", "map": {"version": 3, "names": ["promise", "module", "exports", "queueMicrotask", "bind", "window", "global", "cb", "Promise", "resolve", "then", "catch", "err", "setTimeout"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/queue-microtask/index.js"], "sourcesContent": ["/*! queue-microtask. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nlet promise\n\nmodule.exports = typeof queueMicrotask === 'function'\n  ? queueMicrotask.bind(typeof window !== 'undefined' ? window : global)\n  // reuse resolved promise, and allocate it lazily\n  : cb => (promise || (promise = Promise.resolve()))\n    .then(cb)\n    .catch(err => setTimeout(() => { throw err }, 0))\n"], "mappings": "AAAA;AACA,IAAIA,OAAO;AAEXC,MAAM,CAACC,OAAO,GAAG,OAAOC,cAAc,KAAK,UAAU,GACjDA,cAAc,CAACC,IAAI,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGC,MAAM;AACrE;AAAA,EACEC,EAAE,IAAI,CAACP,OAAO,KAAKA,OAAO,GAAGQ,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,EAC9CC,IAAI,CAACH,EAAE,CAAC,CACRI,KAAK,CAACC,GAAG,IAAIC,UAAU,CAAC,MAAM;EAAE,MAAMD,GAAG;AAAC,CAAC,EAAE,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}