import React, { useState, useEffect, useRef } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { useAuth } from '../contexts/AuthContext';

const DebateChat = ({ debateId, isVisible, onClose }) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const chatInputRef = useRef(null);
  
  const { 
    sendDebateMessage, 
    getDebateMessages, 
    getDebateParticipantCount,
    isConnected 
  } = useWebSocket();
  const { user, isAuthenticated } = useAuth();

  const messages = getDebateMessages(debateId);
  const participantCount = getDebateParticipantCount(debateId);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isVisible && chatInputRef.current) {
      chatInputRef.current.focus();
    }
  }, [isVisible]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!message.trim() || !isAuthenticated || !isConnected) return;

    sendDebateMessage(debateId, message.trim());
    setMessage('');
    setIsTyping(false);
  };

  const handleInputChange = (e) => {
    setMessage(e.target.value);
    
    if (!isTyping && e.target.value.length > 0) {
      setIsTyping(true);
    } else if (isTyping && e.target.value.length === 0) {
      setIsTyping(false);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (!isVisible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '400px',
        height: '500px',
        backgroundColor: 'white',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: '1rem',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <div>
          <h3 style={{ margin: 0, fontSize: '1rem', fontWeight: '600' }}>
            Debate Chat
          </h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginTop: '0.25rem' }}>
            <span
              style={{
                fontSize: '0.75rem',
                color: isConnected ? '#10b981' : '#ef4444',
                fontWeight: '500'
              }}
            >
              {isConnected ? '🟢 Live' : '🔴 Offline'}
            </span>
            <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              {participantCount} participant{participantCount !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            color: '#6b7280',
            cursor: 'pointer',
            fontSize: '1.25rem',
            padding: '0.25rem',
            borderRadius: '4px'
          }}
          title="Close chat"
        >
          ×
        </button>
      </div>

      {/* Messages */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '1rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '0.75rem'
        }}
      >
        {messages.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              color: '#6b7280',
              padding: '2rem 0'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💬</div>
            <p style={{ margin: 0, fontSize: '0.875rem' }}>
              No messages yet. Start the conversation!
            </p>
          </div>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '0.25rem'
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span
                  style={{
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: msg.userId === user?.id ? '#3b82f6' : '#374151'
                  }}
                >
                  {msg.username}
                  {msg.userId === user?.id && ' (You)'}
                </span>
                <span
                  style={{
                    fontSize: '0.75rem',
                    color: '#9ca3af'
                  }}
                >
                  {formatTimestamp(msg.timestamp)}
                </span>
              </div>
              <div
                style={{
                  backgroundColor: msg.userId === user?.id ? '#dbeafe' : '#f3f4f6',
                  padding: '0.5rem 0.75rem',
                  borderRadius: '8px',
                  fontSize: '0.875rem',
                  lineHeight: '1.4',
                  wordWrap: 'break-word'
                }}
              >
                {msg.message}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div
        style={{
          padding: '1rem',
          borderTop: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}
      >
        {!isAuthenticated ? (
          <div
            style={{
              textAlign: 'center',
              color: '#6b7280',
              fontSize: '0.875rem'
            }}
          >
            <a href="/login" style={{ color: '#3b82f6', textDecoration: 'underline' }}>
              Login
            </a>{' '}
            to participate in chat
          </div>
        ) : !isConnected ? (
          <div
            style={{
              textAlign: 'center',
              color: '#ef4444',
              fontSize: '0.875rem'
            }}
          >
            🔴 Disconnected - Reconnecting...
          </div>
        ) : (
          <form onSubmit={handleSendMessage}>
            <div
              style={{
                display: 'flex',
                gap: '0.5rem',
                alignItems: 'flex-end'
              }}
            >
              <input
                ref={chatInputRef}
                type="text"
                value={message}
                onChange={handleInputChange}
                placeholder="Type your message..."
                style={{
                  flex: 1,
                  padding: '0.5rem 0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  outline: 'none',
                  resize: 'none'
                }}
                maxLength={500}
                disabled={!isConnected}
              />
              <button
                type="submit"
                disabled={!message.trim() || !isConnected}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: message.trim() && isConnected ? '#3b82f6' : '#9ca3af',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  cursor: message.trim() && isConnected ? 'pointer' : 'not-allowed',
                  fontWeight: '500'
                }}
              >
                Send
              </button>
            </div>
            <div
              style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                marginTop: '0.25rem',
                textAlign: 'right'
              }}
            >
              {message.length}/500
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default DebateChat;
