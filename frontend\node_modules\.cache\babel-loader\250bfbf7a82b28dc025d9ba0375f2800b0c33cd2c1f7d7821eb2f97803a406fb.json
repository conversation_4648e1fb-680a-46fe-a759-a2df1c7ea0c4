{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\components\\\\AnalyticsDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalyticsDashboard = () => {\n  _s();\n  var _analyticsData$chartD;\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('engagement');\n  const [analyticsData, setAnalyticsData] = useState(null);\n\n  // Mock analytics data\n  useEffect(() => {\n    setAnalyticsData({\n      overview: {\n        totalPosts: 47,\n        totalViews: 125430,\n        totalEngagement: 8920,\n        newFollowers: 234,\n        engagementRate: 7.1,\n        reachGrowth: 15.3\n      },\n      chartData: {\n        engagement: [{\n          date: '2024-05-28',\n          value: 1200\n        }, {\n          date: '2024-05-29',\n          value: 1450\n        }, {\n          date: '2024-05-30',\n          value: 1100\n        }, {\n          date: '2024-05-31',\n          value: 1800\n        }, {\n          date: '2024-06-01',\n          value: 1650\n        }, {\n          date: '2024-06-02',\n          value: 2100\n        }, {\n          date: '2024-06-03',\n          value: 1920\n        }],\n        reach: [{\n          date: '2024-05-28',\n          value: 15200\n        }, {\n          date: '2024-05-29',\n          value: 18450\n        }, {\n          date: '2024-05-30',\n          value: 16100\n        }, {\n          date: '2024-05-31',\n          value: 21800\n        }, {\n          date: '2024-06-01',\n          value: 19650\n        }, {\n          date: '2024-06-02',\n          value: 24100\n        }, {\n          date: '2024-06-03',\n          value: 22920\n        }]\n      },\n      topPosts: [{\n        id: 1,\n        content: \"Climate change policy discussion: The urgency of renewable energy transition cannot be overstated...\",\n        views: 15420,\n        engagement: 892,\n        shares: 156,\n        date: '2024-06-01'\n      }, {\n        id: 2,\n        content: \"Healthcare reform update: Universal healthcare coverage analysis and implementation strategies...\",\n        views: 12350,\n        engagement: 734,\n        shares: 98,\n        date: '2024-05-30'\n      }, {\n        id: 3,\n        content: \"Economic policy insights: Inflation impact on working families and proposed solutions...\",\n        views: 9870,\n        engagement: 567,\n        shares: 87,\n        date: '2024-05-29'\n      }],\n      demographics: {\n        ageGroups: [{\n          range: '18-24',\n          percentage: 15\n        }, {\n          range: '25-34',\n          percentage: 28\n        }, {\n          range: '35-44',\n          percentage: 25\n        }, {\n          range: '45-54',\n          percentage: 20\n        }, {\n          range: '55+',\n          percentage: 12\n        }],\n        locations: [{\n          state: 'California',\n          percentage: 18\n        }, {\n          state: 'New York',\n          percentage: 14\n        }, {\n          state: 'Texas',\n          percentage: 12\n        }, {\n          state: 'Florida',\n          percentage: 10\n        }, {\n          state: 'Illinois',\n          percentage: 8\n        }]\n      }\n    });\n  }, [timeRange]);\n  const timeRanges = [{\n    value: '24h',\n    label: 'Last 24 Hours'\n  }, {\n    value: '7d',\n    label: 'Last 7 Days'\n  }, {\n    value: '30d',\n    label: 'Last 30 Days'\n  }, {\n    value: '90d',\n    label: 'Last 3 Months'\n  }, {\n    value: '1y',\n    label: 'Last Year'\n  }];\n  const metrics = [{\n    key: 'engagement',\n    label: 'Engagement',\n    icon: '💬',\n    color: 'text-primary-600'\n  }, {\n    key: 'reach',\n    label: 'Reach',\n    icon: '📊',\n    color: 'text-success-600'\n  }, {\n    key: 'followers',\n    label: 'Followers',\n    icon: '👥',\n    color: 'text-purple-600'\n  }, {\n    key: 'impressions',\n    label: 'Impressions',\n    icon: '👁️',\n    color: 'text-orange-600'\n  }];\n  if (!analyticsData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-6 animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-secondary-200 rounded w-1/4 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-32 bg-secondary-200 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-secondary-900 flex items-center gap-2\",\n            children: \"\\uD83D\\uDCCA Analytics Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-secondary-600 mt-1\",\n            children: \"Track your political content performance and audience insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: timeRange,\n            onChange: e => setTimeRange(e.target.value),\n            className: \"form-select\",\n            children: timeRanges.map(range => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: range.value,\n              children: range.label\n            }, range.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline\",\n            children: \"\\uD83D\\uDCE5 Export Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-secondary-600\",\n              children: \"Total Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-secondary-900\",\n              children: analyticsData.overview.totalPosts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-success-600\",\n            children: \"\\u2197\\uFE0F +12% from last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-secondary-600\",\n              children: \"Total Views\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-secondary-900\",\n              children: analyticsData.overview.totalViews.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-success-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\uD83D\\uDC41\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-success-600\",\n            children: [\"\\u2197\\uFE0F +\", analyticsData.overview.reachGrowth, \"% reach growth\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-secondary-600\",\n              children: \"Engagement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-secondary-900\",\n              children: analyticsData.overview.totalEngagement.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-success-600\",\n            children: [\"\\u2197\\uFE0F \", analyticsData.overview.engagementRate, \"% rate\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-secondary-600\",\n              children: \"New Followers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-secondary-900\",\n              children: analyticsData.overview.newFollowers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-success-600\",\n            children: \"\\u2197\\uFE0F +18% growth rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-secondary-900\",\n          children: \"Performance Trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: metrics.map(metric => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedMetric(metric.key),\n            className: `btn btn-sm ${selectedMetric === metric.key ? 'btn-primary' : 'btn-ghost'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: metric.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline ml-1\",\n              children: metric.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, metric.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-64 flex items-end justify-between gap-2\",\n        children: (_analyticsData$chartD = analyticsData.chartData[selectedMetric]) === null || _analyticsData$chartD === void 0 ? void 0 : _analyticsData$chartD.map((point, index) => {\n          const maxValue = Math.max(...analyticsData.chartData[selectedMetric].map(p => p.value));\n          const height = point.value / maxValue * 100;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-primary-500 rounded-t transition-all duration-500 hover:bg-primary-600\",\n              style: {\n                height: `${height}%`\n              },\n              title: `${point.date}: ${point.value.toLocaleString()}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-secondary-500 mt-2 transform -rotate-45 origin-left\",\n              children: new Date(point.date).toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-secondary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-secondary-900\",\n            children: \"Top Performing Posts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divide-y divide-secondary-200\",\n          children: analyticsData.topPosts.map((post, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm font-bold text-primary-600\",\n                children: [\"#\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-secondary-900 mb-3 line-clamp-2\",\n                  children: post.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-secondary-500\",\n                      children: \"Views\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-secondary-900\",\n                      children: post.views.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-secondary-500\",\n                      children: \"Engagement\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-secondary-900\",\n                      children: post.engagement\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-secondary-500\",\n                      children: \"Shares\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-secondary-900\",\n                      children: post.shares\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-secondary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-secondary-900\",\n            children: \"Audience Demographics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-secondary-900 mb-3\",\n              children: \"Age Distribution\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: analyticsData.demographics.ageGroups.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-secondary-600\",\n                  children: group.range\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 flex-1 ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 bg-secondary-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-primary-500 h-2 rounded-full transition-all duration-500\",\n                      style: {\n                        width: `${group.percentage}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-secondary-900 w-8\",\n                    children: [group.percentage, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, group.range, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-secondary-900 mb-3\",\n              children: \"Top Locations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: analyticsData.demographics.locations.map(location => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-secondary-600\",\n                  children: location.state\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 flex-1 ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 bg-secondary-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-success-500 h-2 rounded-full transition-all duration-500\",\n                      style: {\n                        width: `${location.percentage}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-secondary-900 w-8\",\n                    children: [location.percentage, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, location.state, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-secondary-900 mb-4\",\n        children: \"AI-Powered Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-primary-50 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-primary-600 text-xl\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-primary-900\",\n                children: \"Optimal Posting Times\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-primary-700 mt-1\",\n                children: \"Your audience is most active on weekdays between 9-11 AM and 7-9 PM EST. Consider scheduling more content during these peak hours.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-success-50 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-success-600 text-xl\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-success-900\",\n                children: \"Content Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-success-700 mt-1\",\n                children: \"Posts about climate policy generate 40% more engagement than average. Consider creating more content on environmental topics.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalyticsDashboard, \"aPneZYMmf27mTbayRO7trc7w/+Q=\");\n_c = AnalyticsDashboard;\nexport default AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AnalyticsDashboard", "_s", "_analyticsData$chartD", "timeRange", "setTimeRange", "selectedMetric", "setSelectedMetric", "analyticsData", "setAnalyticsData", "overview", "totalPosts", "totalViews", "totalEngagement", "newFollowers", "engagementRate", "reachGrowth", "chartData", "engagement", "date", "value", "reach", "topPosts", "id", "content", "views", "shares", "demographics", "ageGroups", "range", "percentage", "locations", "state", "timeRanges", "label", "metrics", "key", "icon", "color", "className", "children", "Array", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "toLocaleString", "metric", "onClick", "point", "index", "maxValue", "Math", "max", "p", "height", "style", "title", "Date", "toLocaleDateString", "month", "day", "post", "group", "width", "location", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/components/AnalyticsDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst AnalyticsDashboard = () => {\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('engagement');\n  const [analyticsData, setAnalyticsData] = useState(null);\n\n  // Mock analytics data\n  useEffect(() => {\n    setAnalyticsData({\n      overview: {\n        totalPosts: 47,\n        totalViews: 125430,\n        totalEngagement: 8920,\n        newFollowers: 234,\n        engagementRate: 7.1,\n        reachGrowth: 15.3\n      },\n      chartData: {\n        engagement: [\n          { date: '2024-05-28', value: 1200 },\n          { date: '2024-05-29', value: 1450 },\n          { date: '2024-05-30', value: 1100 },\n          { date: '2024-05-31', value: 1800 },\n          { date: '2024-06-01', value: 1650 },\n          { date: '2024-06-02', value: 2100 },\n          { date: '2024-06-03', value: 1920 }\n        ],\n        reach: [\n          { date: '2024-05-28', value: 15200 },\n          { date: '2024-05-29', value: 18450 },\n          { date: '2024-05-30', value: 16100 },\n          { date: '2024-05-31', value: 21800 },\n          { date: '2024-06-01', value: 19650 },\n          { date: '2024-06-02', value: 24100 },\n          { date: '2024-06-03', value: 22920 }\n        ]\n      },\n      topPosts: [\n        {\n          id: 1,\n          content: \"Climate change policy discussion: The urgency of renewable energy transition cannot be overstated...\",\n          views: 15420,\n          engagement: 892,\n          shares: 156,\n          date: '2024-06-01'\n        },\n        {\n          id: 2,\n          content: \"Healthcare reform update: Universal healthcare coverage analysis and implementation strategies...\",\n          views: 12350,\n          engagement: 734,\n          shares: 98,\n          date: '2024-05-30'\n        },\n        {\n          id: 3,\n          content: \"Economic policy insights: Inflation impact on working families and proposed solutions...\",\n          views: 9870,\n          engagement: 567,\n          shares: 87,\n          date: '2024-05-29'\n        }\n      ],\n      demographics: {\n        ageGroups: [\n          { range: '18-24', percentage: 15 },\n          { range: '25-34', percentage: 28 },\n          { range: '35-44', percentage: 25 },\n          { range: '45-54', percentage: 20 },\n          { range: '55+', percentage: 12 }\n        ],\n        locations: [\n          { state: 'California', percentage: 18 },\n          { state: 'New York', percentage: 14 },\n          { state: 'Texas', percentage: 12 },\n          { state: 'Florida', percentage: 10 },\n          { state: 'Illinois', percentage: 8 }\n        ]\n      }\n    });\n  }, [timeRange]);\n\n  const timeRanges = [\n    { value: '24h', label: 'Last 24 Hours' },\n    { value: '7d', label: 'Last 7 Days' },\n    { value: '30d', label: 'Last 30 Days' },\n    { value: '90d', label: 'Last 3 Months' },\n    { value: '1y', label: 'Last Year' }\n  ];\n\n  const metrics = [\n    { key: 'engagement', label: 'Engagement', icon: '💬', color: 'text-primary-600' },\n    { key: 'reach', label: 'Reach', icon: '📊', color: 'text-success-600' },\n    { key: 'followers', label: 'Followers', icon: '👥', color: 'text-purple-600' },\n    { key: 'impressions', label: 'Impressions', icon: '👁️', color: 'text-orange-600' }\n  ];\n\n  if (!analyticsData) {\n    return (\n      <div className=\"space-y-6\">\n        {[...Array(6)].map((_, i) => (\n          <div key={i} className=\"card p-6 animate-pulse\">\n            <div className=\"h-6 bg-secondary-200 rounded w-1/4 mb-4\"></div>\n            <div className=\"h-32 bg-secondary-200 rounded\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      \n      {/* Header */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-secondary-900 flex items-center gap-2\">\n              📊 Analytics Dashboard\n            </h2>\n            <p className=\"text-secondary-600 mt-1\">\n              Track your political content performance and audience insights\n            </p>\n          </div>\n          \n          <div className=\"flex items-center gap-3\">\n            <select\n              value={timeRange}\n              onChange={(e) => setTimeRange(e.target.value)}\n              className=\"form-select\"\n            >\n              {timeRanges.map(range => (\n                <option key={range.value} value={range.value}>\n                  {range.label}\n                </option>\n              ))}\n            </select>\n            <button className=\"btn btn-outline\">\n              📥 Export Report\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-secondary-600\">Total Posts</p>\n              <p className=\"text-2xl font-bold text-secondary-900\">{analyticsData.overview.totalPosts}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-xl\">📝</span>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm text-success-600\">↗️ +12% from last period</span>\n          </div>\n        </div>\n\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-secondary-600\">Total Views</p>\n              <p className=\"text-2xl font-bold text-secondary-900\">{analyticsData.overview.totalViews.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-success-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-xl\">👁️</span>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm text-success-600\">↗️ +{analyticsData.overview.reachGrowth}% reach growth</span>\n          </div>\n        </div>\n\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-secondary-600\">Engagement</p>\n              <p className=\"text-2xl font-bold text-secondary-900\">{analyticsData.overview.totalEngagement.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-xl\">💬</span>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm text-success-600\">↗️ {analyticsData.overview.engagementRate}% rate</span>\n          </div>\n        </div>\n\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-secondary-600\">New Followers</p>\n              <p className=\"text-2xl font-bold text-secondary-900\">{analyticsData.overview.newFollowers}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-xl\">👥</span>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm text-success-600\">↗️ +18% growth rate</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Chart */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-secondary-900\">Performance Trends</h3>\n          <div className=\"flex items-center gap-2\">\n            {metrics.map(metric => (\n              <button\n                key={metric.key}\n                onClick={() => setSelectedMetric(metric.key)}\n                className={`btn btn-sm ${\n                  selectedMetric === metric.key ? 'btn-primary' : 'btn-ghost'\n                }`}\n              >\n                <span>{metric.icon}</span>\n                <span className=\"hidden sm:inline ml-1\">{metric.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Simple Chart Visualization */}\n        <div className=\"h-64 flex items-end justify-between gap-2\">\n          {analyticsData.chartData[selectedMetric]?.map((point, index) => {\n            const maxValue = Math.max(...analyticsData.chartData[selectedMetric].map(p => p.value));\n            const height = (point.value / maxValue) * 100;\n            \n            return (\n              <div key={index} className=\"flex-1 flex flex-col items-center\">\n                <div\n                  className=\"w-full bg-primary-500 rounded-t transition-all duration-500 hover:bg-primary-600\"\n                  style={{ height: `${height}%` }}\n                  title={`${point.date}: ${point.value.toLocaleString()}`}\n                ></div>\n                <div className=\"text-xs text-secondary-500 mt-2 transform -rotate-45 origin-left\">\n                  {new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        \n        {/* Top Performing Posts */}\n        <div className=\"card\">\n          <div className=\"p-6 border-b border-secondary-200\">\n            <h3 className=\"text-lg font-semibold text-secondary-900\">Top Performing Posts</h3>\n          </div>\n          <div className=\"divide-y divide-secondary-200\">\n            {analyticsData.topPosts.map((post, index) => (\n              <div key={post.id} className=\"p-6\">\n                <div className=\"flex items-start gap-4\">\n                  <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm font-bold text-primary-600\">\n                    #{index + 1}\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-secondary-900 mb-3 line-clamp-2\">\n                      {post.content}\n                    </p>\n                    <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-secondary-500\">Views</span>\n                        <div className=\"font-semibold text-secondary-900\">{post.views.toLocaleString()}</div>\n                      </div>\n                      <div>\n                        <span className=\"text-secondary-500\">Engagement</span>\n                        <div className=\"font-semibold text-secondary-900\">{post.engagement}</div>\n                      </div>\n                      <div>\n                        <span className=\"text-secondary-500\">Shares</span>\n                        <div className=\"font-semibold text-secondary-900\">{post.shares}</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Audience Demographics */}\n        <div className=\"card\">\n          <div className=\"p-6 border-b border-secondary-200\">\n            <h3 className=\"text-lg font-semibold text-secondary-900\">Audience Demographics</h3>\n          </div>\n          <div className=\"p-6 space-y-6\">\n            \n            {/* Age Groups */}\n            <div>\n              <h4 className=\"font-medium text-secondary-900 mb-3\">Age Distribution</h4>\n              <div className=\"space-y-3\">\n                {analyticsData.demographics.ageGroups.map(group => (\n                  <div key={group.range} className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-secondary-600\">{group.range}</span>\n                    <div className=\"flex items-center gap-3 flex-1 ml-4\">\n                      <div className=\"flex-1 bg-secondary-200 rounded-full h-2\">\n                        <div\n                          className=\"bg-primary-500 h-2 rounded-full transition-all duration-500\"\n                          style={{ width: `${group.percentage}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"text-sm font-medium text-secondary-900 w-8\">\n                        {group.percentage}%\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Top Locations */}\n            <div>\n              <h4 className=\"font-medium text-secondary-900 mb-3\">Top Locations</h4>\n              <div className=\"space-y-3\">\n                {analyticsData.demographics.locations.map(location => (\n                  <div key={location.state} className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-secondary-600\">{location.state}</span>\n                    <div className=\"flex items-center gap-3 flex-1 ml-4\">\n                      <div className=\"flex-1 bg-secondary-200 rounded-full h-2\">\n                        <div\n                          className=\"bg-success-500 h-2 rounded-full transition-all duration-500\"\n                          style={{ width: `${location.percentage}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"text-sm font-medium text-secondary-900 w-8\">\n                        {location.percentage}%\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Insights and Recommendations */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-secondary-900 mb-4\">AI-Powered Insights</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"bg-primary-50 rounded-lg p-4\">\n            <div className=\"flex items-start gap-3\">\n              <span className=\"text-primary-600 text-xl\">🎯</span>\n              <div>\n                <h4 className=\"font-medium text-primary-900\">Optimal Posting Times</h4>\n                <p className=\"text-sm text-primary-700 mt-1\">\n                  Your audience is most active on weekdays between 9-11 AM and 7-9 PM EST. \n                  Consider scheduling more content during these peak hours.\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-success-50 rounded-lg p-4\">\n            <div className=\"flex items-start gap-3\">\n              <span className=\"text-success-600 text-xl\">📈</span>\n              <div>\n                <h4 className=\"font-medium text-success-900\">Content Performance</h4>\n                <p className=\"text-sm text-success-700 mt-1\">\n                  Posts about climate policy generate 40% more engagement than average. \n                  Consider creating more content on environmental topics.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnalyticsDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,YAAY,CAAC;EAClE,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACdW,gBAAgB,CAAC;MACfC,QAAQ,EAAE;QACRC,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,MAAM;QAClBC,eAAe,EAAE,IAAI;QACrBC,YAAY,EAAE,GAAG;QACjBC,cAAc,EAAE,GAAG;QACnBC,WAAW,EAAE;MACf,CAAC;MACDC,SAAS,EAAE;QACTC,UAAU,EAAE,CACV;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,EACnC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAK,CAAC,CACpC;QACDC,KAAK,EAAE,CACL;UAAEF,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC,EACpC;UAAED,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAM,CAAC;MAExC,CAAC;MACDE,QAAQ,EAAE,CACR;QACEC,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,sGAAsG;QAC/GC,KAAK,EAAE,KAAK;QACZP,UAAU,EAAE,GAAG;QACfQ,MAAM,EAAE,GAAG;QACXP,IAAI,EAAE;MACR,CAAC,EACD;QACEI,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,mGAAmG;QAC5GC,KAAK,EAAE,KAAK;QACZP,UAAU,EAAE,GAAG;QACfQ,MAAM,EAAE,EAAE;QACVP,IAAI,EAAE;MACR,CAAC,EACD;QACEI,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,0FAA0F;QACnGC,KAAK,EAAE,IAAI;QACXP,UAAU,EAAE,GAAG;QACfQ,MAAM,EAAE,EAAE;QACVP,IAAI,EAAE;MACR,CAAC,CACF;MACDQ,YAAY,EAAE;QACZC,SAAS,EAAE,CACT;UAAEC,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAG,CAAC,EAClC;UAAED,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAG,CAAC,EAClC;UAAED,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAG,CAAC,EAClC;UAAED,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAG,CAAC,EAClC;UAAED,KAAK,EAAE,KAAK;UAAEC,UAAU,EAAE;QAAG,CAAC,CACjC;QACDC,SAAS,EAAE,CACT;UAAEC,KAAK,EAAE,YAAY;UAAEF,UAAU,EAAE;QAAG,CAAC,EACvC;UAAEE,KAAK,EAAE,UAAU;UAAEF,UAAU,EAAE;QAAG,CAAC,EACrC;UAAEE,KAAK,EAAE,OAAO;UAAEF,UAAU,EAAE;QAAG,CAAC,EAClC;UAAEE,KAAK,EAAE,SAAS;UAAEF,UAAU,EAAE;QAAG,CAAC,EACpC;UAAEE,KAAK,EAAE,UAAU;UAAEF,UAAU,EAAE;QAAE,CAAC;MAExC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EAEf,MAAM6B,UAAU,GAAG,CACjB;IAAEb,KAAK,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAgB,CAAC,EACxC;IAAEd,KAAK,EAAE,IAAI;IAAEc,KAAK,EAAE;EAAc,CAAC,EACrC;IAAEd,KAAK,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAe,CAAC,EACvC;IAAEd,KAAK,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAgB,CAAC,EACxC;IAAEd,KAAK,EAAE,IAAI;IAAEc,KAAK,EAAE;EAAY,CAAC,CACpC;EAED,MAAMC,OAAO,GAAG,CACd;IAAEC,GAAG,EAAE,YAAY;IAAEF,KAAK,EAAE,YAAY;IAAEG,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACjF;IAAEF,GAAG,EAAE,OAAO;IAAEF,KAAK,EAAE,OAAO;IAAEG,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACvE;IAAEF,GAAG,EAAE,WAAW;IAAEF,KAAK,EAAE,WAAW;IAAEG,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC9E;IAAEF,GAAG,EAAE,aAAa;IAAEF,KAAK,EAAE,aAAa;IAAEG,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACpF;EAED,IAAI,CAAC9B,aAAa,EAAE;IAClB,oBACER,OAAA;MAAKuC,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB5C,OAAA;QAAauC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAC7CxC,OAAA;UAAKuC,SAAS,EAAC;QAAyC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/DhD,OAAA;UAAKuC,SAAS,EAAC;QAA+B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAF7CJ,CAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEhD,OAAA;IAAKuC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAGxBxC,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAAC,QAAA,eACvBxC,OAAA;QAAKuC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAIuC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE9E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhD,OAAA;YAAGuC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhD,OAAA;UAAKuC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCxC,OAAA;YACEoB,KAAK,EAAEhB,SAAU;YACjB6C,QAAQ,EAAGC,CAAC,IAAK7C,YAAY,CAAC6C,CAAC,CAACC,MAAM,CAAC/B,KAAK,CAAE;YAC9CmB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAEtBP,UAAU,CAACS,GAAG,CAACb,KAAK,iBACnB7B,OAAA;cAA0BoB,KAAK,EAAES,KAAK,CAACT,KAAM;cAAAoB,QAAA,EAC1CX,KAAK,CAACK;YAAK,GADDL,KAAK,CAACT,KAAK;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACThD,OAAA;YAAQuC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEpC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAKuC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnExC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAGuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEhD,OAAA;cAAGuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEhC,aAAa,CAACE,QAAQ,CAACC;YAAU;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFxC,OAAA;cAAMuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCxC,OAAA;YAAMuC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAwB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAGuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEhD,OAAA;cAAGuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEhC,aAAa,CAACE,QAAQ,CAACE,UAAU,CAACwC,cAAc,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFxC,OAAA;cAAMuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCxC,OAAA;YAAMuC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,gBAAI,EAAChC,aAAa,CAACE,QAAQ,CAACM,WAAW,EAAC,gBAAc;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAGuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEhD,OAAA;cAAGuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEhC,aAAa,CAACE,QAAQ,CAACG,eAAe,CAACuC,cAAc,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,uEAAuE;YAAAC,QAAA,eACpFxC,OAAA;cAAMuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCxC,OAAA;YAAMuC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,eAAG,EAAChC,aAAa,CAACE,QAAQ,CAACK,cAAc,EAAC,QAAM;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAGuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvEhD,OAAA;cAAGuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEhC,aAAa,CAACE,QAAQ,CAACI;YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,uEAAuE;YAAAC,QAAA,eACpFxC,OAAA;cAAMuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCxC,OAAA;YAAMuC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBxC,OAAA;QAAKuC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxC,OAAA;UAAIuC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFhD,OAAA;UAAKuC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrCL,OAAO,CAACO,GAAG,CAACW,MAAM,iBACjBrD,OAAA;YAEEsD,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC8C,MAAM,CAACjB,GAAG,CAAE;YAC7CG,SAAS,EAAE,cACTjC,cAAc,KAAK+C,MAAM,CAACjB,GAAG,GAAG,aAAa,GAAG,WAAW,EAC1D;YAAAI,QAAA,gBAEHxC,OAAA;cAAAwC,QAAA,EAAOa,MAAM,CAAChB;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BhD,OAAA;cAAMuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEa,MAAM,CAACnB;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAPxDK,MAAM,CAACjB,GAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQT,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAKuC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,GAAArC,qBAAA,GACvDK,aAAa,CAACS,SAAS,CAACX,cAAc,CAAC,cAAAH,qBAAA,uBAAvCA,qBAAA,CAAyCuC,GAAG,CAAC,CAACa,KAAK,EAAEC,KAAK,KAAK;UAC9D,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGnD,aAAa,CAACS,SAAS,CAACX,cAAc,CAAC,CAACoC,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACxC,KAAK,CAAC,CAAC;UACvF,MAAMyC,MAAM,GAAIN,KAAK,CAACnC,KAAK,GAAGqC,QAAQ,GAAI,GAAG;UAE7C,oBACEzD,OAAA;YAAiBuC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC5DxC,OAAA;cACEuC,SAAS,EAAC,kFAAkF;cAC5FuB,KAAK,EAAE;gBAAED,MAAM,EAAE,GAAGA,MAAM;cAAI,CAAE;cAChCE,KAAK,EAAE,GAAGR,KAAK,CAACpC,IAAI,KAAKoC,KAAK,CAACnC,KAAK,CAACgC,cAAc,CAAC,CAAC;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACPhD,OAAA;cAAKuC,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC9E,IAAIwB,IAAI,CAACT,KAAK,CAACpC,IAAI,CAAC,CAAC8C,kBAAkB,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,GAAG,EAAE;cAAU,CAAC;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA,GAREQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhD,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAGpDxC,OAAA;QAAKuC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDxC,OAAA;YAAIuC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAC3ChC,aAAa,CAACc,QAAQ,CAACoB,GAAG,CAAC,CAAC0B,IAAI,EAAEZ,KAAK,kBACtCxD,OAAA;YAAmBuC,SAAS,EAAC,KAAK;YAAAC,QAAA,eAChCxC,OAAA;cAAKuC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxC,OAAA;gBAAKuC,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,GAAC,GACtH,EAACgB,KAAK,GAAG,CAAC;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACNhD,OAAA;gBAAKuC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxC,OAAA;kBAAGuC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAChD4B,IAAI,CAAC5C;gBAAO;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACJhD,OAAA;kBAAKuC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CxC,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAMuC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjDhD,OAAA;sBAAKuC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE4B,IAAI,CAAC3C,KAAK,CAAC2B,cAAc,CAAC;oBAAC;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNhD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAMuC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtDhD,OAAA;sBAAKuC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE4B,IAAI,CAAClD;oBAAU;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACNhD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAMuC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClDhD,OAAA;sBAAKuC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE4B,IAAI,CAAC1C;oBAAM;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAxBEoB,IAAI,CAAC7C,EAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAKuC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDxC,OAAA;YAAIuC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAqB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAG5BxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAIuC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEhD,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhC,aAAa,CAACmB,YAAY,CAACC,SAAS,CAACc,GAAG,CAAC2B,KAAK,iBAC7CrE,OAAA;gBAAuBuC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAClExC,OAAA;kBAAMuC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE6B,KAAK,CAACxC;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjEhD,OAAA;kBAAKuC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDxC,OAAA;oBAAKuC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eACvDxC,OAAA;sBACEuC,SAAS,EAAC,6DAA6D;sBACvEuB,KAAK,EAAE;wBAAEQ,KAAK,EAAE,GAAGD,KAAK,CAACvC,UAAU;sBAAI;oBAAE;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhD,OAAA;oBAAMuC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,GACzD6B,KAAK,CAACvC,UAAU,EAAC,GACpB;kBAAA;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAZEqB,KAAK,CAACxC,KAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAahB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhD,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAIuC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEhD,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhC,aAAa,CAACmB,YAAY,CAACI,SAAS,CAACW,GAAG,CAAC6B,QAAQ,iBAChDvE,OAAA;gBAA0BuC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACrExC,OAAA;kBAAMuC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE+B,QAAQ,CAACvC;gBAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpEhD,OAAA;kBAAKuC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDxC,OAAA;oBAAKuC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eACvDxC,OAAA;sBACEuC,SAAS,EAAC,6DAA6D;sBACvEuB,KAAK,EAAE;wBAAEQ,KAAK,EAAE,GAAGC,QAAQ,CAACzC,UAAU;sBAAI;oBAAE;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhD,OAAA;oBAAMuC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,GACzD+B,QAAQ,CAACzC,UAAU,EAAC,GACvB;kBAAA;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAZEuB,QAAQ,CAACvC,KAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAanB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBxC,OAAA;QAAIuC,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtFhD,OAAA;QAAKuC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDxC,OAAA;UAAKuC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CxC,OAAA;YAAKuC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCxC,OAAA;cAAMuC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDhD,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAIuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEhD,OAAA;gBAAGuC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAG7C;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA;UAAKuC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CxC,OAAA;YAAKuC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCxC,OAAA;cAAMuC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDhD,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAIuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEhD,OAAA;gBAAGuC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAG7C;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAxXID,kBAAkB;AAAAuE,EAAA,GAAlBvE,kBAAkB;AA0XxB,eAAeA,kBAAkB;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}