{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('politica_token'));\n  const [loading, setLoading] = useState(true);\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await fetch(`${API_URL}/api/auth/profile`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          if (response.ok) {\n            const data = await response.json();\n            setUser(data.user);\n          } else {\n            // Token is invalid, remove it\n            localStorage.removeItem('politica_token');\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          localStorage.removeItem('politica_token');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token, API_URL]);\n  const login = async (email, password) => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setUser(data.user);\n        setToken(data.token);\n        localStorage.setItem('politica_token', data.token);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          error: data.error\n        };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        error: 'Network error occurred'\n      };\n    }\n  };\n  const register = async (username, email, password) => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          username,\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setUser(data.user);\n        setToken(data.token);\n        localStorage.setItem('politica_token', data.token);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          error: data.error\n        };\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      return {\n        success: false,\n        error: 'Network error occurred'\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('politica_token');\n  };\n  const updateProfile = async updates => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updates)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setUser(data.user);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          error: data.error\n        };\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      return {\n        success: false,\n        error: 'Network error occurred'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAuthenticated: !!user,\n    isVerified: (user === null || user === void 0 ? void 0 : user.verified_status) || false,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"ilzSo37BiC2LvtcNrPdzmRAex2Y=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "API_URL", "process", "env", "REACT_APP_API_URL", "checkAuth", "response", "fetch", "headers", "ok", "data", "json", "removeItem", "error", "console", "login", "email", "password", "method", "body", "JSON", "stringify", "setItem", "success", "register", "username", "logout", "updateProfile", "updates", "value", "isAuthenticated", "isVerified", "verified_status", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('politica_token'));\n  const [loading, setLoading] = useState(true);\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await fetch(`${API_URL}/api/auth/profile`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            setUser(data.user);\n          } else {\n            // Token is invalid, remove it\n            localStorage.removeItem('politica_token');\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          localStorage.removeItem('politica_token');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token, API_URL]);\n\n  const login = async (email, password) => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ email, password })\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setUser(data.user);\n        setToken(data.token);\n        localStorage.setItem('politica_token', data.token);\n        return { success: true, user: data.user };\n      } else {\n        return { success: false, error: data.error };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const register = async (username, email, password) => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ username, email, password })\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setUser(data.user);\n        setToken(data.token);\n        localStorage.setItem('politica_token', data.token);\n        return { success: true, user: data.user };\n      } else {\n        return { success: false, error: data.error };\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('politica_token');\n  };\n\n  const updateProfile = async (updates) => {\n    try {\n      const response = await fetch(`${API_URL}/api/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updates)\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setUser(data.user);\n        return { success: true, user: data.user };\n      } else {\n        return { success: false, error: data.error };\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAuthenticated: !!user,\n    isVerified: user?.verified_status || false,\n    isAdmin: user?.role === 'admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAACgB,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;EAC1E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMoB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;EAExE;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIV,KAAK,EAAE;QACT,IAAI;UACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,mBAAmB,EAAE;YAC1DO,OAAO,EAAE;cACP,eAAe,EAAE,UAAUb,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UAEF,IAAIW,QAAQ,CAACG,EAAE,EAAE;YACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;YAClCjB,OAAO,CAACgB,IAAI,CAACjB,IAAI,CAAC;UACpB,CAAC,MAAM;YACL;YACAI,YAAY,CAACe,UAAU,CAAC,gBAAgB,CAAC;YACzChB,QAAQ,CAAC,IAAI,CAAC;UAChB;QACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1ChB,YAAY,CAACe,UAAU,CAAC,gBAAgB,CAAC;UACzChB,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACV,KAAK,EAAEM,OAAO,CAAC,CAAC;EAEpB,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,iBAAiB,EAAE;QACxDiB,MAAM,EAAE,MAAM;QACdV,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDW,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEL,KAAK;UAAEC;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMP,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACG,EAAE,EAAE;QACff,OAAO,CAACgB,IAAI,CAACjB,IAAI,CAAC;QAClBG,QAAQ,CAACc,IAAI,CAACf,KAAK,CAAC;QACpBE,YAAY,CAACyB,OAAO,CAAC,gBAAgB,EAAEZ,IAAI,CAACf,KAAK,CAAC;QAClD,OAAO;UAAE4B,OAAO,EAAE,IAAI;UAAE9B,IAAI,EAAEiB,IAAI,CAACjB;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAE8B,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAEH,IAAI,CAACG;QAAM,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAE;MAAyB,CAAC;IAC5D;EACF,CAAC;EAED,MAAMW,QAAQ,GAAG,MAAAA,CAAOC,QAAQ,EAAET,KAAK,EAAEC,QAAQ,KAAK;IACpD,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,oBAAoB,EAAE;QAC3DiB,MAAM,EAAE,MAAM;QACdV,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDW,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEI,QAAQ;UAAET,KAAK;UAAEC;QAAS,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMP,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACG,EAAE,EAAE;QACff,OAAO,CAACgB,IAAI,CAACjB,IAAI,CAAC;QAClBG,QAAQ,CAACc,IAAI,CAACf,KAAK,CAAC;QACpBE,YAAY,CAACyB,OAAO,CAAC,gBAAgB,EAAEZ,IAAI,CAACf,KAAK,CAAC;QAClD,OAAO;UAAE4B,OAAO,EAAE,IAAI;UAAE9B,IAAI,EAAEiB,IAAI,CAACjB;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAE8B,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAEH,IAAI,CAACG;QAAM,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAE;MAAyB,CAAC;IAC5D;EACF,CAAC;EAED,MAAMa,MAAM,GAAGA,CAAA,KAAM;IACnBhC,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAACe,UAAU,CAAC,gBAAgB,CAAC;EAC3C,CAAC;EAED,MAAMe,aAAa,GAAG,MAAOC,OAAO,IAAK;IACvC,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,mBAAmB,EAAE;QAC1DiB,MAAM,EAAE,KAAK;QACbV,OAAO,EAAE;UACP,eAAe,EAAE,UAAUb,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACO,OAAO;MAC9B,CAAC,CAAC;MAEF,MAAMlB,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACG,EAAE,EAAE;QACff,OAAO,CAACgB,IAAI,CAACjB,IAAI,CAAC;QAClB,OAAO;UAAE8B,OAAO,EAAE,IAAI;UAAE9B,IAAI,EAAEiB,IAAI,CAACjB;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAE8B,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAEH,IAAI,CAACG;QAAM,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAE;MAAyB,CAAC;IAC5D;EACF,CAAC;EAED,MAAMgB,KAAK,GAAG;IACZpC,IAAI;IACJE,KAAK;IACLI,OAAO;IACPgB,KAAK;IACLS,QAAQ;IACRE,MAAM;IACNC,aAAa;IACbG,eAAe,EAAE,CAAC,CAACrC,IAAI;IACvBsC,UAAU,EAAE,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,eAAe,KAAI,KAAK;IAC1CC,OAAO,EAAE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,IAAI,MAAK;EAC1B,CAAC;EAED,oBACElD,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACN,KAAK,EAAEA,KAAM;IAAAtC,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CA5IWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}