import React, { useState, useEffect, useRef, useCallback } from 'react';
import Peer from 'simple-peer';
import { useWebSocket } from '../contexts/WebSocketContext';
import { useAuth } from '../contexts/AuthContext';

const VideoDebate = ({ debateId, isVisible, onClose, debate }) => {
  const [localStream, setLocalStream] = useState(null);
  const [peers, setPeers] = useState({});
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isModerator, setIsModerator] = useState(false);
  const [speakingQueue, setSpeakingQueue] = useState([]);
  const [currentSpeaker, setCurrentSpeaker] = useState(null);
  const [handRaised, setHandRaised] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState('stopped');
  
  const localVideoRef = useRef(null);
  const peersRef = useRef({});
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);
  
  const { socket, isConnected } = useWebSocket();
  const { user } = useAuth();

  // Initialize video stream
  useEffect(() => {
    if (isVisible && !localStream) {
      initializeMedia();
    }
    
    return () => {
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
      Object.values(peersRef.current).forEach(peer => {
        if (peer.destroy) peer.destroy();
      });
    };
  }, [isVisible]);

  // Check if user is moderator
  useEffect(() => {
    if (debate && user) {
      setIsModerator(debate.moderator_id === user.id);
    }
  }, [debate, user]);

  // WebSocket event listeners
  useEffect(() => {
    if (!socket || !isVisible) return;

    socket.on('video-signal', handleVideoSignal);
    socket.on('user-joined-video', handleUserJoinedVideo);
    socket.on('user-left-video', handleUserLeftVideo);
    socket.on('speaking-queue-updated', setSpeakingQueue);
    socket.on('current-speaker-changed', setCurrentSpeaker);
    socket.on('hand-raised', handleHandRaised);
    socket.on('hand-lowered', handleHandLowered);
    socket.on('recording-status-changed', setRecordingStatus);

    return () => {
      socket.off('video-signal');
      socket.off('user-joined-video');
      socket.off('user-left-video');
      socket.off('speaking-queue-updated');
      socket.off('current-speaker-changed');
      socket.off('hand-raised');
      socket.off('hand-lowered');
      socket.off('recording-status-changed');
    };
  }, [socket, isVisible]);

  const initializeMedia = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 },
        audio: true
      });
      
      setLocalStream(stream);
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
      
      // Join video room
      socket.emit('join-video-debate', { debateId, userId: user.id });
      
    } catch (error) {
      console.error('Error accessing media devices:', error);
      alert('Unable to access camera/microphone. Please check permissions.');
    }
  };

  const createPeer = useCallback((initiator, stream, socketId) => {
    const peer = new Peer({
      initiator,
      trickle: false,
      stream
    });

    peer.on('signal', signal => {
      socket.emit('video-signal', {
        signal,
        to: socketId,
        from: socket.id,
        debateId
      });
    });

    peer.on('stream', remoteStream => {
      setPeers(prev => ({
        ...prev,
        [socketId]: { ...prev[socketId], stream: remoteStream }
      }));
    });

    peer.on('error', err => {
      console.error('Peer error:', err);
    });

    return peer;
  }, [socket, debateId]);

  const handleVideoSignal = useCallback((data) => {
    if (data.to === socket.id) {
      if (!peersRef.current[data.from]) {
        const peer = createPeer(false, localStream, data.from);
        peersRef.current[data.from] = peer;
      }
      peersRef.current[data.from].signal(data.signal);
    }
  }, [createPeer, localStream, socket]);

  const handleUserJoinedVideo = useCallback((data) => {
    if (data.socketId !== socket.id && localStream) {
      const peer = createPeer(true, localStream, data.socketId);
      peersRef.current[data.socketId] = peer;
      
      setPeers(prev => ({
        ...prev,
        [data.socketId]: { 
          userId: data.userId, 
          username: data.username, 
          peer,
          stream: null 
        }
      }));
    }
  }, [createPeer, localStream, socket]);

  const handleUserLeftVideo = useCallback((data) => {
    if (peersRef.current[data.socketId]) {
      peersRef.current[data.socketId].destroy();
      delete peersRef.current[data.socketId];
    }
    
    setPeers(prev => {
      const newPeers = { ...prev };
      delete newPeers[data.socketId];
      return newPeers;
    });
  }, []);

  const toggleVideo = () => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
        
        socket.emit('video-toggle', {
          debateId,
          enabled: videoTrack.enabled
        });
      }
    }
  };

  const toggleAudio = () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
        
        socket.emit('audio-toggle', {
          debateId,
          enabled: audioTrack.enabled
        });
      }
    }
  };

  const startScreenShare = async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
      
      // Replace video track in all peer connections
      const videoTrack = screenStream.getVideoTracks()[0];
      Object.values(peersRef.current).forEach(peer => {
        const sender = peer._pc.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );
        if (sender) {
          sender.replaceTrack(videoTrack);
        }
      });
      
      // Update local video
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = screenStream;
      }
      
      setIsScreenSharing(true);
      
      // Handle screen share end
      videoTrack.onended = () => {
        stopScreenShare();
      };
      
      socket.emit('screen-share-started', { debateId });
      
    } catch (error) {
      console.error('Error starting screen share:', error);
    }
  };

  const stopScreenShare = async () => {
    try {
      // Get camera stream back
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      // Replace screen share track with camera track
      const videoTrack = cameraStream.getVideoTracks()[0];
      Object.values(peersRef.current).forEach(peer => {
        const sender = peer._pc.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );
        if (sender) {
          sender.replaceTrack(videoTrack);
        }
      });
      
      // Update local video
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = cameraStream;
      }
      
      setLocalStream(cameraStream);
      setIsScreenSharing(false);
      
      socket.emit('screen-share-stopped', { debateId });
      
    } catch (error) {
      console.error('Error stopping screen share:', error);
    }
  };

  const raiseHand = () => {
    setHandRaised(true);
    socket.emit('raise-hand', { debateId, userId: user.id, username: user.username });
  };

  const lowerHand = () => {
    setHandRaised(false);
    socket.emit('lower-hand', { debateId, userId: user.id });
  };

  const handleHandRaised = (data) => {
    setSpeakingQueue(prev => [...prev, data]);
  };

  const handleHandLowered = (data) => {
    setSpeakingQueue(prev => prev.filter(item => item.userId !== data.userId));
  };

  const giveFloorTo = (userId) => {
    if (isModerator) {
      socket.emit('give-floor', { debateId, userId });
      setSpeakingQueue(prev => prev.filter(item => item.userId !== userId));
    }
  };

  const startRecording = () => {
    if (isModerator && localStream) {
      try {
        const mediaRecorder = new MediaRecorder(localStream);
        mediaRecorderRef.current = mediaRecorder;
        recordedChunksRef.current = [];
        
        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            recordedChunksRef.current.push(event.data);
          }
        };
        
        mediaRecorder.onstop = () => {
          const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `debate-${debateId}-${new Date().toISOString()}.webm`;
          a.click();
        };
        
        mediaRecorder.start();
        setRecordingStatus('recording');
        socket.emit('recording-started', { debateId });
        
      } catch (error) {
        console.error('Error starting recording:', error);
      }
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && recordingStatus === 'recording') {
      mediaRecorderRef.current.stop();
      setRecordingStatus('stopped');
      socket.emit('recording-stopped', { debateId });
    }
  };

  if (!isVisible) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      backgroundColor: '#000',
      zIndex: 2000,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: '#1f2937',
        color: 'white',
        padding: '1rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h2 style={{ margin: 0, fontSize: '1.25rem' }}>
            🎥 {debate?.title || 'Live Video Debate'}
          </h2>
          <div style={{ fontSize: '0.875rem', opacity: 0.8, marginTop: '0.25rem' }}>
            {Object.keys(peers).length + 1} participants • 
            {recordingStatus === 'recording' && ' 🔴 Recording • '}
            {currentSpeaker && ` Speaking: ${currentSpeaker.username}`}
          </div>
        </div>
        
        <button
          onClick={onClose}
          style={{
            background: '#ef4444',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          Leave Debate
        </button>
      </div>

      {/* Video Grid */}
      <div style={{
        flex: 1,
        display: 'grid',
        gridTemplateColumns: Object.keys(peers).length > 3 ? 'repeat(3, 1fr)' : 'repeat(2, 1fr)',
        gap: '1rem',
        padding: '1rem',
        overflow: 'auto'
      }}>
        {/* Local Video */}
        <div style={{
          position: 'relative',
          backgroundColor: '#374151',
          borderRadius: '8px',
          overflow: 'hidden',
          border: currentSpeaker?.userId === user.id ? '3px solid #10b981' : 'none'
        }}>
          <video
            ref={localVideoRef}
            autoPlay
            muted
            playsInline
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
          <div style={{
            position: 'absolute',
            bottom: '0.5rem',
            left: '0.5rem',
            backgroundColor: 'rgba(0,0,0,0.7)',
            color: 'white',
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            fontSize: '0.875rem'
          }}>
            {user?.username} (You) {handRaised && '✋'}
          </div>
          {!isVideoEnabled && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: 'white',
              fontSize: '3rem'
            }}>
              📹
            </div>
          )}
        </div>

        {/* Remote Videos */}
        {Object.entries(peers).map(([socketId, peerData]) => (
          <RemoteVideo
            key={socketId}
            peerData={peerData}
            currentSpeaker={currentSpeaker}
            isModerator={isModerator}
            onGiveFloor={giveFloorTo}
          />
        ))}
      </div>

      {/* Controls */}
      <div style={{
        backgroundColor: '#1f2937',
        padding: '1rem',
        display: 'flex',
        justifyContent: 'center',
        gap: '1rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={toggleVideo}
          style={{
            backgroundColor: isVideoEnabled ? '#374151' : '#ef4444',
            color: 'white',
            border: 'none',
            padding: '0.75rem',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '1.25rem'
          }}
        >
          {isVideoEnabled ? '📹' : '📹'}
        </button>

        <button
          onClick={toggleAudio}
          style={{
            backgroundColor: isAudioEnabled ? '#374151' : '#ef4444',
            color: 'white',
            border: 'none',
            padding: '0.75rem',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '1.25rem'
          }}
        >
          {isAudioEnabled ? '🎤' : '🔇'}
        </button>

        <button
          onClick={isScreenSharing ? stopScreenShare : startScreenShare}
          style={{
            backgroundColor: isScreenSharing ? '#10b981' : '#374151',
            color: 'white',
            border: 'none',
            padding: '0.75rem',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '1.25rem'
          }}
        >
          🖥️
        </button>

        <button
          onClick={handRaised ? lowerHand : raiseHand}
          style={{
            backgroundColor: handRaised ? '#f59e0b' : '#374151',
            color: 'white',
            border: 'none',
            padding: '0.75rem',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '1.25rem'
          }}
        >
          ✋
        </button>

        {isModerator && (
          <button
            onClick={recordingStatus === 'recording' ? stopRecording : startRecording}
            style={{
              backgroundColor: recordingStatus === 'recording' ? '#ef4444' : '#374151',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1rem',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.875rem'
            }}
          >
            {recordingStatus === 'recording' ? '⏹️ Stop Recording' : '🔴 Start Recording'}
          </button>
        )}
      </div>

      {/* Speaking Queue */}
      {speakingQueue.length > 0 && (
        <div style={{
          position: 'absolute',
          top: '5rem',
          right: '1rem',
          backgroundColor: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '1rem',
          borderRadius: '8px',
          minWidth: '200px'
        }}>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Speaking Queue</h4>
          {speakingQueue.map((item, index) => (
            <div key={item.userId} style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '0.25rem 0'
            }}>
              <span>{index + 1}. {item.username}</span>
              {isModerator && (
                <button
                  onClick={() => giveFloorTo(item.userId)}
                  style={{
                    background: '#10b981',
                    color: 'white',
                    border: 'none',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.75rem'
                  }}
                >
                  Give Floor
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Remote Video Component
const RemoteVideo = ({ peerData, currentSpeaker, isModerator, onGiveFloor }) => {
  const videoRef = useRef(null);

  useEffect(() => {
    if (peerData.stream && videoRef.current) {
      videoRef.current.srcObject = peerData.stream;
    }
  }, [peerData.stream]);

  return (
    <div style={{
      position: 'relative',
      backgroundColor: '#374151',
      borderRadius: '8px',
      overflow: 'hidden',
      border: currentSpeaker?.userId === peerData.userId ? '3px solid #10b981' : 'none'
    }}>
      <video
        ref={videoRef}
        autoPlay
        playsInline
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      />
      <div style={{
        position: 'absolute',
        bottom: '0.5rem',
        left: '0.5rem',
        backgroundColor: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '0.25rem 0.5rem',
        borderRadius: '4px',
        fontSize: '0.875rem'
      }}>
        {peerData.username}
      </div>
    </div>
  );
};

export default VideoDebate;
