const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const { authenticateToken } = require('../middleware/auth');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'POLITICA_AUG',
  password: process.env.DB_PASSWORD || 'Rayvical',
  port: process.env.DB_PORT || 5432,
});

// Get main feed (posts from followed users + trending)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, filter = 'all' } = req.query;
    const offset = (page - 1) * limit;
    const userId = req.user.id;

    let whereClause = "WHERE p.deleted_at IS NULL";
    let joinClause = "";

    if (filter === 'following') {
      joinClause = `
        INNER JOIN user_follows uf ON p.user_id = uf.following_id 
        AND uf.follower_id = $1
      `;
    } else if (filter === 'trending') {
      whereClause += " AND p.created_at >= NOW() - INTERVAL '24 hours'";
    }

    const query = `
      SELECT 
        p.*,
        u.username,
        u.email,
        u.role,
        u.verified_status,
        COALESCE(vote_counts.upvotes, 0) as upvotes,
        COALESCE(vote_counts.downvotes, 0) as downvotes,
        COALESCE(reaction_counts.total_reactions, 0) as total_reactions,
        COALESCE(comment_counts.comment_count, 0) as comment_count,
        COALESCE(share_counts.share_count, 0) as share_count,
        user_vote.vote_type as user_vote,
        user_reactions.reactions as user_reactions
      FROM posts p
      INNER JOIN users u ON p.user_id = u.id
      ${joinClause}
      LEFT JOIN (
        SELECT 
          post_id,
          SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as upvotes,
          SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as downvotes
        FROM post_votes 
        GROUP BY post_id
      ) vote_counts ON p.id = vote_counts.post_id
      LEFT JOIN (
        SELECT 
          post_id,
          COUNT(*) as total_reactions
        FROM post_reactions 
        GROUP BY post_id
      ) reaction_counts ON p.id = reaction_counts.post_id
      LEFT JOIN (
        SELECT 
          post_id,
          COUNT(*) as comment_count
        FROM comments 
        WHERE deleted_at IS NULL
        GROUP BY post_id
      ) comment_counts ON p.id = comment_counts.post_id
      LEFT JOIN (
        SELECT 
          post_id,
          COUNT(*) as share_count
        FROM post_shares 
        GROUP BY post_id
      ) share_counts ON p.id = share_counts.post_id
      LEFT JOIN post_votes user_vote ON p.id = user_vote.post_id AND user_vote.user_id = $${filter === 'following' ? '2' : '1'}
      LEFT JOIN (
        SELECT 
          post_id,
          ARRAY_AGG(reaction_type) as reactions
        FROM post_reactions 
        WHERE user_id = $${filter === 'following' ? '2' : '1'}
        GROUP BY post_id
      ) user_reactions ON p.id = user_reactions.post_id
      ${whereClause}
      ORDER BY 
        CASE 
          WHEN $${filter === 'following' ? '3' : '2'} = 'trending' THEN (upvotes - downvotes + total_reactions * 0.5)
          ELSE 0 
        END DESC,
        p.created_at DESC
      LIMIT $${filter === 'following' ? '4' : '3'} OFFSET $${filter === 'following' ? '5' : '4'}
    `;

    const params = filter === 'following' 
      ? [userId, userId, filter, limit, offset]
      : [userId, filter, limit, offset];

    const result = await pool.query(query, params);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM posts p
      INNER JOIN users u ON p.user_id = u.id
      ${joinClause}
      ${whereClause}
    `;
    
    const countParams = filter === 'following' ? [userId] : [];
    const countResult = await pool.query(countQuery, countParams);

    res.json({
      posts: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching feed:', error);
    res.status(500).json({ error: 'Failed to fetch feed' });
  }
});

// Create a new post
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { content, post_type = 'text', media_urls = [], hashtags = [], topics = [], visibility = 'public' } = req.body;
    const userId = req.user.id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Content is required' });
    }

    if (content.length > 2000) {
      return res.status(400).json({ error: 'Content too long (max 2000 characters)' });
    }

    const query = `
      INSERT INTO posts (user_id, content, post_type, media_urls, hashtags, topics, visibility)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await pool.query(query, [
      userId, content, post_type, media_urls, hashtags, topics, visibility
    ]);

    // Update hashtag counts
    if (hashtags.length > 0) {
      for (const hashtag of hashtags) {
        await pool.query(`
          INSERT INTO hashtags (name, post_count) 
          VALUES ($1, 1) 
          ON CONFLICT (name) 
          DO UPDATE SET post_count = hashtags.post_count + 1, updated_at = NOW()
        `, [hashtag.toLowerCase()]);
      }
    }

    // Update topic counts
    if (topics.length > 0) {
      for (const topic of topics) {
        await pool.query(`
          UPDATE political_topics 
          SET post_count = post_count + 1, updated_at = NOW() 
          WHERE name = $1
        `, [topic]);
      }
    }

    // Get the created post with user info
    const postQuery = `
      SELECT 
        p.*,
        u.username,
        u.email,
        u.role,
        u.verified_status
      FROM posts p
      INNER JOIN users u ON p.user_id = u.id
      WHERE p.id = $1
    `;

    const postResult = await pool.query(postQuery, [result.rows[0].id]);

    res.status(201).json(postResult.rows[0]);

  } catch (error) {
    console.error('Error creating post:', error);
    res.status(500).json({ error: 'Failed to create post' });
  }
});

// Vote on a post
router.post('/:postId/vote', authenticateToken, async (req, res) => {
  try {
    const { postId } = req.params;
    const { vote_type } = req.body; // 1 for upvote, -1 for downvote, 0 to remove vote
    const userId = req.user.id;

    if (![1, -1, 0].includes(vote_type)) {
      return res.status(400).json({ error: 'Invalid vote type' });
    }

    if (vote_type === 0) {
      // Remove vote
      await pool.query('DELETE FROM post_votes WHERE post_id = $1 AND user_id = $2', [postId, userId]);
    } else {
      // Add or update vote
      await pool.query(`
        INSERT INTO post_votes (post_id, user_id, vote_type)
        VALUES ($1, $2, $3)
        ON CONFLICT (post_id, user_id)
        DO UPDATE SET vote_type = $3, updated_at = NOW()
      `, [postId, userId, vote_type]);
    }

    // Get updated vote counts
    const voteCountQuery = `
      SELECT 
        SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as upvotes,
        SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as downvotes
      FROM post_votes 
      WHERE post_id = $1
    `;

    const voteResult = await pool.query(voteCountQuery, [postId]);

    res.json({
      upvotes: parseInt(voteResult.rows[0].upvotes) || 0,
      downvotes: parseInt(voteResult.rows[0].downvotes) || 0,
      user_vote: vote_type === 0 ? null : vote_type
    });

  } catch (error) {
    console.error('Error voting on post:', error);
    res.status(500).json({ error: 'Failed to vote on post' });
  }
});

// React to a post
router.post('/:postId/react', authenticateToken, async (req, res) => {
  try {
    const { postId } = req.params;
    const { reaction_type } = req.body;
    const userId = req.user.id;

    const validReactions = ['like', 'love', 'laugh', 'angry', 'sad', 'support', 'oppose'];
    if (!validReactions.includes(reaction_type)) {
      return res.status(400).json({ error: 'Invalid reaction type' });
    }

    // Toggle reaction (remove if exists, add if doesn't)
    const existingReaction = await pool.query(
      'SELECT id FROM post_reactions WHERE post_id = $1 AND user_id = $2 AND reaction_type = $3',
      [postId, userId, reaction_type]
    );

    if (existingReaction.rows.length > 0) {
      // Remove reaction
      await pool.query(
        'DELETE FROM post_reactions WHERE post_id = $1 AND user_id = $2 AND reaction_type = $3',
        [postId, userId, reaction_type]
      );
    } else {
      // Add reaction
      await pool.query(
        'INSERT INTO post_reactions (post_id, user_id, reaction_type) VALUES ($1, $2, $3)',
        [postId, userId, reaction_type]
      );
    }

    // Get updated reaction counts
    const reactionCountQuery = `
      SELECT 
        reaction_type,
        COUNT(*) as count
      FROM post_reactions 
      WHERE post_id = $1
      GROUP BY reaction_type
    `;

    const reactionResult = await pool.query(reactionCountQuery, [postId]);

    res.json({
      reactions: reactionResult.rows.reduce((acc, row) => {
        acc[row.reaction_type] = parseInt(row.count);
        return acc;
      }, {})
    });

  } catch (error) {
    console.error('Error reacting to post:', error);
    res.status(500).json({ error: 'Failed to react to post' });
  }
});

// Get trending hashtags
router.get('/hashtags/trending', async (req, res) => {
  try {
    const query = `
      SELECT name, post_count, trending_score
      FROM hashtags
      ORDER BY trending_score DESC, post_count DESC
      LIMIT 20
    `;

    const result = await pool.query(query);
    res.json(result.rows);

  } catch (error) {
    console.error('Error fetching trending hashtags:', error);
    res.status(500).json({ error: 'Failed to fetch trending hashtags' });
  }
});

// Get trending topics
router.get('/topics/trending', async (req, res) => {
  try {
    const query = `
      SELECT name, description, category, post_count, trending_score
      FROM political_topics
      ORDER BY trending_score DESC, post_count DESC
      LIMIT 20
    `;

    const result = await pool.query(query);
    res.json(result.rows);

  } catch (error) {
    console.error('Error fetching trending topics:', error);
    res.status(500).json({ error: 'Failed to fetch trending topics' });
  }
});

module.exports = router;
