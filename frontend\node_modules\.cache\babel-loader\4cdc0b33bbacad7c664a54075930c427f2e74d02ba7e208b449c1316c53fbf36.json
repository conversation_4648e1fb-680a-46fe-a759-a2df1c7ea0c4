{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict';\n\nmodule.exports = Writable;\n\n/* <replacement> */\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n}\n\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n  var _this = this;\n  this.next = null;\n  this.entry = null;\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n\n/*<replacement>*/\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED,\n  ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES,\n  ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END,\n  ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nrequire('inherits')(Writable, Stream);\nfunction nop() {}\nfunction WritableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n\n  // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n  this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex);\n\n  // if _final has been called\n  this.finalCalled = false;\n\n  // drain event flag.\n  this.needDrain = false;\n  // at the start of calling end()\n  this.ending = false;\n  // when end() has been called, and returned\n  this.ended = false;\n  // when 'finish' is emitted\n  this.finished = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0;\n\n  // a flag to see when we're in the middle of a write.\n  this.writing = false;\n\n  // when true all writes will be buffered until .uncork() call\n  this.corked = 0;\n\n  // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true;\n\n  // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false;\n\n  // the callback that's passed to _write(chunk,cb)\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  };\n\n  // the callback that the user supplies to write(chunk,encoding,cb)\n  this.writecb = null;\n\n  // the amount that is being written when _write is called.\n  this.writelen = 0;\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null;\n\n  // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n  this.pendingcb = 0;\n\n  // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n  this.prefinished = false;\n\n  // True if the error was already emitted and should not be thrown again\n  this.errorEmitted = false;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'finish' (and potentially 'end')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // count buffered requests\n  this.bufferedRequestCount = 0;\n\n  // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n  return out;\n};\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function writableStateBufferGetter() {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})();\n\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function value(object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function realHasInstance(object) {\n    return object instanceof this;\n  };\n}\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);\n  this._writableState = new WritableState(options, this, isDuplex);\n\n  // legacy.\n  this.writable = true;\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n    if (typeof options.writev === 'function') this._writev = options.writev;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n  Stream.call(this);\n}\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());\n};\nfunction writeAfterEnd(stream, cb) {\n  var er = new ERR_STREAM_WRITE_AFTER_END();\n  // TODO: defer error events consistently everywhere, not just the cb\n  errorOrDestroy(stream, er);\n  process.nextTick(cb, er);\n}\n\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n  var er;\n  if (chunk === null) {\n    er = new ERR_STREAM_NULL_VALUES();\n  } else if (typeof chunk !== 'string' && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk);\n  }\n  if (er) {\n    errorOrDestroy(stream, er);\n    process.nextTick(cb, er);\n    return false;\n  }\n  return true;\n}\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n  if (typeof cb !== 'function') cb = nop;\n  if (state.ending) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n  return ret;\n};\nWritable.prototype.cork = function () {\n  this._writableState.corked++;\n};\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n  if (state.corked) {\n    state.corked--;\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n  return chunk;\n}\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n  var len = state.objectMode ? 1 : chunk.length;\n  state.length += len;\n  var ret = state.length < state.highWaterMark;\n  // we must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true;\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n  return ret;\n}\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));else if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    process.nextTick(cb, er);\n    // this can emit finish, and it will always happen\n    // after error\n    process.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n    // this can emit finish, but finish must\n    // always follow error\n    finishMaybe(stream, state);\n  }\n}\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n  if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();\n  onwriteStateUpdate(state);\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state) || stream.destroyed;\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n    if (sync) {\n      process.nextTick(afterWrite, stream, state, finished, cb);\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n}\n\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n}\n\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n    var count = 0;\n    var allBuffers = true;\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n    buffer.allBuffers = allBuffers;\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish);\n\n    // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--;\n      // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n      if (state.writing) {\n        break;\n      }\n    }\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));\n};\nWritable.prototype._writev = null;\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n\n  // .end() fully uncorks\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  }\n\n  // ignore unnecessary end() calls.\n  if (!state.ending) endWritable(this, state, cb);\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n    if (err) {\n      errorOrDestroy(stream, err);\n    }\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.pendingcb++;\n      state.finalCalled = true;\n      process.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n  if (need) {\n    prefinish(stream, state);\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n      if (state.autoDestroy) {\n        // In case of duplex streams we need a way to detect\n        // if the readable side is ready for autoDestroy as well\n        var rState = stream._readableState;\n        if (!rState || rState.autoDestroy && rState.endEmitted) {\n          stream.destroy();\n        }\n      }\n    }\n  }\n  return need;\n}\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n  if (cb) {\n    if (state.finished) process.nextTick(cb);else stream.once('finish', cb);\n  }\n  state.ended = true;\n  stream.writable = false;\n}\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  }\n\n  // reuse the free corkReq.\n  state.corkedRequestsFree.next = corkReq;\n}\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._writableState === undefined) {\n      return false;\n    }\n    return this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._writableState.destroyed = value;\n  }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function (err, cb) {\n  cb(err);\n};", "map": {"version": 3, "names": ["module", "exports", "Writable", "WriteReq", "chunk", "encoding", "cb", "callback", "next", "CorkedRequest", "state", "_this", "entry", "finish", "onCorkedFinish", "Duplex", "WritableState", "internalUtil", "deprecate", "require", "Stream", "<PERSON><PERSON><PERSON>", "OurUint8Array", "global", "window", "self", "Uint8Array", "_uint8ArrayToBuffer", "from", "_isUint8Array", "obj", "<PERSON><PERSON><PERSON><PERSON>", "destroyImpl", "_require", "getHighWaterMark", "_require$codes", "codes", "ERR_INVALID_ARG_TYPE", "ERR_METHOD_NOT_IMPLEMENTED", "ERR_MULTIPLE_CALLBACK", "ERR_STREAM_CANNOT_PIPE", "ERR_STREAM_DESTROYED", "ERR_STREAM_NULL_VALUES", "ERR_STREAM_WRITE_AFTER_END", "ERR_UNKNOWN_ENCODING", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nop", "options", "stream", "isDuplex", "objectMode", "writableObjectMode", "highWaterMark", "finalCalled", "needDrain", "ending", "ended", "finished", "destroyed", "noDecode", "decodeStrings", "defaultEncoding", "length", "writing", "corked", "sync", "bufferProcessing", "onwrite", "er", "writecb", "writelen", "bufferedRequest", "lastBufferedRequest", "pendingcb", "prefinished", "errorEmitted", "emitClose", "autoDestroy", "bufferedRequestCount", "corkedRequestsFree", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "current", "out", "push", "Object", "defineProperty", "get", "writableStateBufferGetter", "_", "realHasInstance", "Symbol", "hasInstance", "Function", "value", "object", "call", "_writableState", "writable", "write", "_write", "writev", "_writev", "destroy", "_destroy", "final", "_final", "pipe", "writeAfterEnd", "process", "nextTick", "validChunk", "ret", "isBuf", "writeOr<PERSON>uffer", "cork", "uncork", "<PERSON><PERSON><PERSON><PERSON>", "setDefaultEncoding", "toLowerCase", "indexOf", "enumerable", "decodeChunk", "newChunk", "len", "last", "doWrite", "onwriteError", "finishMaybe", "onwriteStateUpdate", "<PERSON><PERSON><PERSON>sh", "afterWrite", "onwriteDrain", "emit", "l", "buffer", "Array", "holder", "count", "allBuffers", "end", "undefined", "endWritable", "callFinal", "err", "prefinish", "need", "rState", "_readableState", "endEmitted", "once", "corkReq", "set", "_undestroy", "undestroy"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/readable-stream/lib/_stream_writable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict';\n\nmodule.exports = Writable;\n\n/* <replacement> */\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n}\n\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n  var _this = this;\n  this.next = null;\n  this.entry = null;\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n\n/*<replacement>*/\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED,\n  ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES,\n  ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END,\n  ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nrequire('inherits')(Writable, Stream);\nfunction nop() {}\nfunction WritableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n\n  // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n  this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex);\n\n  // if _final has been called\n  this.finalCalled = false;\n\n  // drain event flag.\n  this.needDrain = false;\n  // at the start of calling end()\n  this.ending = false;\n  // when end() has been called, and returned\n  this.ended = false;\n  // when 'finish' is emitted\n  this.finished = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0;\n\n  // a flag to see when we're in the middle of a write.\n  this.writing = false;\n\n  // when true all writes will be buffered until .uncork() call\n  this.corked = 0;\n\n  // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true;\n\n  // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false;\n\n  // the callback that's passed to _write(chunk,cb)\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  };\n\n  // the callback that the user supplies to write(chunk,encoding,cb)\n  this.writecb = null;\n\n  // the amount that is being written when _write is called.\n  this.writelen = 0;\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null;\n\n  // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n  this.pendingcb = 0;\n\n  // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n  this.prefinished = false;\n\n  // True if the error was already emitted and should not be thrown again\n  this.errorEmitted = false;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'finish' (and potentially 'end')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // count buffered requests\n  this.bufferedRequestCount = 0;\n\n  // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n  return out;\n};\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function writableStateBufferGetter() {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})();\n\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function value(object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function realHasInstance(object) {\n    return object instanceof this;\n  };\n}\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);\n  this._writableState = new WritableState(options, this, isDuplex);\n\n  // legacy.\n  this.writable = true;\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n    if (typeof options.writev === 'function') this._writev = options.writev;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n  Stream.call(this);\n}\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());\n};\nfunction writeAfterEnd(stream, cb) {\n  var er = new ERR_STREAM_WRITE_AFTER_END();\n  // TODO: defer error events consistently everywhere, not just the cb\n  errorOrDestroy(stream, er);\n  process.nextTick(cb, er);\n}\n\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n  var er;\n  if (chunk === null) {\n    er = new ERR_STREAM_NULL_VALUES();\n  } else if (typeof chunk !== 'string' && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk);\n  }\n  if (er) {\n    errorOrDestroy(stream, er);\n    process.nextTick(cb, er);\n    return false;\n  }\n  return true;\n}\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n  if (typeof cb !== 'function') cb = nop;\n  if (state.ending) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n  return ret;\n};\nWritable.prototype.cork = function () {\n  this._writableState.corked++;\n};\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n  if (state.corked) {\n    state.corked--;\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n  return chunk;\n}\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n  var len = state.objectMode ? 1 : chunk.length;\n  state.length += len;\n  var ret = state.length < state.highWaterMark;\n  // we must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true;\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n  return ret;\n}\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));else if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    process.nextTick(cb, er);\n    // this can emit finish, and it will always happen\n    // after error\n    process.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n    // this can emit finish, but finish must\n    // always follow error\n    finishMaybe(stream, state);\n  }\n}\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n  if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();\n  onwriteStateUpdate(state);\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state) || stream.destroyed;\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n    if (sync) {\n      process.nextTick(afterWrite, stream, state, finished, cb);\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n}\n\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n}\n\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n    var count = 0;\n    var allBuffers = true;\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n    buffer.allBuffers = allBuffers;\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish);\n\n    // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--;\n      // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n      if (state.writing) {\n        break;\n      }\n    }\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));\n};\nWritable.prototype._writev = null;\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n\n  // .end() fully uncorks\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  }\n\n  // ignore unnecessary end() calls.\n  if (!state.ending) endWritable(this, state, cb);\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n    if (err) {\n      errorOrDestroy(stream, err);\n    }\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.pendingcb++;\n      state.finalCalled = true;\n      process.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n  if (need) {\n    prefinish(stream, state);\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n      if (state.autoDestroy) {\n        // In case of duplex streams we need a way to detect\n        // if the readable side is ready for autoDestroy as well\n        var rState = stream._readableState;\n        if (!rState || rState.autoDestroy && rState.endEmitted) {\n          stream.destroy();\n        }\n      }\n    }\n  }\n  return need;\n}\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n  if (cb) {\n    if (state.finished) process.nextTick(cb);else stream.once('finish', cb);\n  }\n  state.ended = true;\n  stream.writable = false;\n}\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  }\n\n  // reuse the free corkReq.\n  state.corkedRequestsFree.next = corkReq;\n}\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._writableState === undefined) {\n      return false;\n    }\n    return this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._writableState.destroyed = value;\n  }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function (err, cb) {\n  cb(err);\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;;AAEzB;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACrC,IAAI,CAACF,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACE,QAAQ,GAAGD,EAAE;EAClB,IAAI,CAACE,IAAI,GAAG,IAAI;AAClB;;AAEA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAI,CAACH,IAAI,GAAG,IAAI;EAChB,IAAI,CAACI,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,MAAM,GAAG,YAAY;IACxBC,cAAc,CAACH,KAAK,EAAED,KAAK,CAAC;EAC9B,CAAC;AACH;AACA;;AAEA;AACA,IAAIK,MAAM;AACV;;AAEAb,QAAQ,CAACc,aAAa,GAAGA,aAAa;;AAEtC;AACA,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAEC,OAAO,CAAC,gBAAgB;AACrC,CAAC;AACD;;AAEA;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AACjD;;AAEA,IAAIE,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC,CAACE,MAAM;AACrC,IAAIC,aAAa,GAAG,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAEC,UAAU,IAAI,YAAY,CAAC,CAAC;AAC5K,SAASC,mBAAmBA,CAACvB,KAAK,EAAE;EAClC,OAAOiB,MAAM,CAACO,IAAI,CAACxB,KAAK,CAAC;AAC3B;AACA,SAASyB,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAOT,MAAM,CAACU,QAAQ,CAACD,GAAG,CAAC,IAAIA,GAAG,YAAYR,aAAa;AAC7D;AACA,IAAIU,WAAW,GAAGb,OAAO,CAAC,4BAA4B,CAAC;AACvD,IAAIc,QAAQ,GAAGd,OAAO,CAAC,0BAA0B,CAAC;EAChDe,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB;AAC9C,IAAIC,cAAc,GAAGhB,OAAO,CAAC,WAAW,CAAC,CAACiB,KAAK;EAC7CC,oBAAoB,GAAGF,cAAc,CAACE,oBAAoB;EAC1DC,0BAA0B,GAAGH,cAAc,CAACG,0BAA0B;EACtEC,qBAAqB,GAAGJ,cAAc,CAACI,qBAAqB;EAC5DC,sBAAsB,GAAGL,cAAc,CAACK,sBAAsB;EAC9DC,oBAAoB,GAAGN,cAAc,CAACM,oBAAoB;EAC1DC,sBAAsB,GAAGP,cAAc,CAACO,sBAAsB;EAC9DC,0BAA0B,GAAGR,cAAc,CAACQ,0BAA0B;EACtEC,oBAAoB,GAAGT,cAAc,CAACS,oBAAoB;AAC5D,IAAIC,cAAc,GAAGb,WAAW,CAACa,cAAc;AAC/C1B,OAAO,CAAC,UAAU,CAAC,CAACjB,QAAQ,EAAEkB,MAAM,CAAC;AACrC,SAAS0B,GAAGA,CAAA,EAAG,CAAC;AAChB,SAAS9B,aAAaA,CAAC+B,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAChDlC,MAAM,GAAGA,MAAM,IAAII,OAAO,CAAC,kBAAkB,CAAC;EAC9C4B,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOE,QAAQ,KAAK,SAAS,EAAEA,QAAQ,GAAGD,MAAM,YAAYjC,MAAM;;EAEtE;EACA;EACA,IAAI,CAACmC,UAAU,GAAG,CAAC,CAACH,OAAO,CAACG,UAAU;EACtC,IAAID,QAAQ,EAAE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAACH,OAAO,CAACI,kBAAkB;;EAE/E;EACA;EACA;EACA,IAAI,CAACC,aAAa,GAAGlB,gBAAgB,CAAC,IAAI,EAAEa,OAAO,EAAE,uBAAuB,EAAEE,QAAQ,CAAC;;EAEvF;EACA,IAAI,CAACI,WAAW,GAAG,KAAK;;EAExB;EACA,IAAI,CAACC,SAAS,GAAG,KAAK;EACtB;EACA,IAAI,CAACC,MAAM,GAAG,KAAK;EACnB;EACA,IAAI,CAACC,KAAK,GAAG,KAAK;EAClB;EACA,IAAI,CAACC,QAAQ,GAAG,KAAK;;EAErB;EACA,IAAI,CAACC,SAAS,GAAG,KAAK;;EAEtB;EACA;EACA;EACA,IAAIC,QAAQ,GAAGZ,OAAO,CAACa,aAAa,KAAK,KAAK;EAC9C,IAAI,CAACA,aAAa,GAAG,CAACD,QAAQ;;EAE9B;EACA;EACA;EACA,IAAI,CAACE,eAAe,GAAGd,OAAO,CAACc,eAAe,IAAI,MAAM;;EAExD;EACA;EACA;EACA,IAAI,CAACC,MAAM,GAAG,CAAC;;EAEf;EACA,IAAI,CAACC,OAAO,GAAG,KAAK;;EAEpB;EACA,IAAI,CAACC,MAAM,GAAG,CAAC;;EAEf;EACA;EACA;EACA;EACA,IAAI,CAACC,IAAI,GAAG,IAAI;;EAEhB;EACA;EACA;EACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;;EAE7B;EACA,IAAI,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;IAC3BD,OAAO,CAACnB,MAAM,EAAEoB,EAAE,CAAC;EACrB,CAAC;;EAED;EACA,IAAI,CAACC,OAAO,GAAG,IAAI;;EAEnB;EACA,IAAI,CAACC,QAAQ,GAAG,CAAC;EACjB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC3B,IAAI,CAACC,mBAAmB,GAAG,IAAI;;EAE/B;EACA;EACA,IAAI,CAACC,SAAS,GAAG,CAAC;;EAElB;EACA;EACA,IAAI,CAACC,WAAW,GAAG,KAAK;;EAExB;EACA,IAAI,CAACC,YAAY,GAAG,KAAK;;EAEzB;EACA,IAAI,CAACC,SAAS,GAAG7B,OAAO,CAAC6B,SAAS,KAAK,KAAK;;EAE5C;EACA,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC9B,OAAO,CAAC8B,WAAW;;EAExC;EACA,IAAI,CAACC,oBAAoB,GAAG,CAAC;;EAE7B;EACA;EACA,IAAI,CAACC,kBAAkB,GAAG,IAAItE,aAAa,CAAC,IAAI,CAAC;AACnD;AACAO,aAAa,CAACgE,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACvD,IAAIC,OAAO,GAAG,IAAI,CAACX,eAAe;EAClC,IAAIY,GAAG,GAAG,EAAE;EACZ,OAAOD,OAAO,EAAE;IACdC,GAAG,CAACC,IAAI,CAACF,OAAO,CAAC;IACjBA,OAAO,GAAGA,OAAO,CAAC1E,IAAI;EACxB;EACA,OAAO2E,GAAG;AACZ,CAAC;AACD,CAAC,YAAY;EACX,IAAI;IACFE,MAAM,CAACC,cAAc,CAACtE,aAAa,CAACgE,SAAS,EAAE,QAAQ,EAAE;MACvDO,GAAG,EAAEtE,YAAY,CAACC,SAAS,CAAC,SAASsE,yBAAyBA,CAAA,EAAG;QAC/D,OAAO,IAAI,CAACP,SAAS,CAAC,CAAC;MACzB,CAAC,EAAE,oEAAoE,GAAG,UAAU,EAAE,SAAS;IACjG,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOQ,CAAC,EAAE,CAAC;AACf,CAAC,EAAE,CAAC;;AAEJ;AACA;AACA,IAAIC,eAAe;AACnB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,WAAW,IAAI,OAAOC,QAAQ,CAACb,SAAS,CAACW,MAAM,CAACC,WAAW,CAAC,KAAK,UAAU,EAAE;EACtHF,eAAe,GAAGG,QAAQ,CAACb,SAAS,CAACW,MAAM,CAACC,WAAW,CAAC;EACxDP,MAAM,CAACC,cAAc,CAACpF,QAAQ,EAAEyF,MAAM,CAACC,WAAW,EAAE;IAClDE,KAAK,EAAE,SAASA,KAAKA,CAACC,MAAM,EAAE;MAC5B,IAAIL,eAAe,CAACM,IAAI,CAAC,IAAI,EAAED,MAAM,CAAC,EAAE,OAAO,IAAI;MACnD,IAAI,IAAI,KAAK7F,QAAQ,EAAE,OAAO,KAAK;MACnC,OAAO6F,MAAM,IAAIA,MAAM,CAACE,cAAc,YAAYjF,aAAa;IACjE;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACL0E,eAAe,GAAG,SAASA,eAAeA,CAACK,MAAM,EAAE;IACjD,OAAOA,MAAM,YAAY,IAAI;EAC/B,CAAC;AACH;AACA,SAAS7F,QAAQA,CAAC6C,OAAO,EAAE;EACzBhC,MAAM,GAAGA,MAAM,IAAII,OAAO,CAAC,kBAAkB,CAAC;;EAE9C;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA,IAAI8B,QAAQ,GAAG,IAAI,YAAYlC,MAAM;EACrC,IAAI,CAACkC,QAAQ,IAAI,CAACyC,eAAe,CAACM,IAAI,CAAC9F,QAAQ,EAAE,IAAI,CAAC,EAAE,OAAO,IAAIA,QAAQ,CAAC6C,OAAO,CAAC;EACpF,IAAI,CAACkD,cAAc,GAAG,IAAIjF,aAAa,CAAC+B,OAAO,EAAE,IAAI,EAAEE,QAAQ,CAAC;;EAEhE;EACA,IAAI,CAACiD,QAAQ,GAAG,IAAI;EACpB,IAAInD,OAAO,EAAE;IACX,IAAI,OAAOA,OAAO,CAACoD,KAAK,KAAK,UAAU,EAAE,IAAI,CAACC,MAAM,GAAGrD,OAAO,CAACoD,KAAK;IACpE,IAAI,OAAOpD,OAAO,CAACsD,MAAM,KAAK,UAAU,EAAE,IAAI,CAACC,OAAO,GAAGvD,OAAO,CAACsD,MAAM;IACvE,IAAI,OAAOtD,OAAO,CAACwD,OAAO,KAAK,UAAU,EAAE,IAAI,CAACC,QAAQ,GAAGzD,OAAO,CAACwD,OAAO;IAC1E,IAAI,OAAOxD,OAAO,CAAC0D,KAAK,KAAK,UAAU,EAAE,IAAI,CAACC,MAAM,GAAG3D,OAAO,CAAC0D,KAAK;EACtE;EACArF,MAAM,CAAC4E,IAAI,CAAC,IAAI,CAAC;AACnB;;AAEA;AACA9F,QAAQ,CAAC8E,SAAS,CAAC2B,IAAI,GAAG,YAAY;EACpC9D,cAAc,CAAC,IAAI,EAAE,IAAIL,sBAAsB,CAAC,CAAC,CAAC;AACpD,CAAC;AACD,SAASoE,aAAaA,CAAC5D,MAAM,EAAE1C,EAAE,EAAE;EACjC,IAAI8D,EAAE,GAAG,IAAIzB,0BAA0B,CAAC,CAAC;EACzC;EACAE,cAAc,CAACG,MAAM,EAAEoB,EAAE,CAAC;EAC1ByC,OAAO,CAACC,QAAQ,CAACxG,EAAE,EAAE8D,EAAE,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,SAAS2C,UAAUA,CAAC/D,MAAM,EAAEtC,KAAK,EAAEN,KAAK,EAAEE,EAAE,EAAE;EAC5C,IAAI8D,EAAE;EACN,IAAIhE,KAAK,KAAK,IAAI,EAAE;IAClBgE,EAAE,GAAG,IAAI1B,sBAAsB,CAAC,CAAC;EACnC,CAAC,MAAM,IAAI,OAAOtC,KAAK,KAAK,QAAQ,IAAI,CAACM,KAAK,CAACwC,UAAU,EAAE;IACzDkB,EAAE,GAAG,IAAI/B,oBAAoB,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEjC,KAAK,CAAC;EACrE;EACA,IAAIgE,EAAE,EAAE;IACNvB,cAAc,CAACG,MAAM,EAAEoB,EAAE,CAAC;IAC1ByC,OAAO,CAACC,QAAQ,CAACxG,EAAE,EAAE8D,EAAE,CAAC;IACxB,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACAlE,QAAQ,CAAC8E,SAAS,CAACmB,KAAK,GAAG,UAAU/F,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACxD,IAAII,KAAK,GAAG,IAAI,CAACuF,cAAc;EAC/B,IAAIe,GAAG,GAAG,KAAK;EACf,IAAIC,KAAK,GAAG,CAACvG,KAAK,CAACwC,UAAU,IAAIrB,aAAa,CAACzB,KAAK,CAAC;EACrD,IAAI6G,KAAK,IAAI,CAAC5F,MAAM,CAACU,QAAQ,CAAC3B,KAAK,CAAC,EAAE;IACpCA,KAAK,GAAGuB,mBAAmB,CAACvB,KAAK,CAAC;EACpC;EACA,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;IAClCC,EAAE,GAAGD,QAAQ;IACbA,QAAQ,GAAG,IAAI;EACjB;EACA,IAAI4G,KAAK,EAAE5G,QAAQ,GAAG,QAAQ,CAAC,KAAK,IAAI,CAACA,QAAQ,EAAEA,QAAQ,GAAGK,KAAK,CAACmD,eAAe;EACnF,IAAI,OAAOvD,EAAE,KAAK,UAAU,EAAEA,EAAE,GAAGwC,GAAG;EACtC,IAAIpC,KAAK,CAAC6C,MAAM,EAAEqD,aAAa,CAAC,IAAI,EAAEtG,EAAE,CAAC,CAAC,KAAK,IAAI2G,KAAK,IAAIF,UAAU,CAAC,IAAI,EAAErG,KAAK,EAAEN,KAAK,EAAEE,EAAE,CAAC,EAAE;IAC9FI,KAAK,CAAC+D,SAAS,EAAE;IACjBuC,GAAG,GAAGE,aAAa,CAAC,IAAI,EAAExG,KAAK,EAAEuG,KAAK,EAAE7G,KAAK,EAAEC,QAAQ,EAAEC,EAAE,CAAC;EAC9D;EACA,OAAO0G,GAAG;AACZ,CAAC;AACD9G,QAAQ,CAAC8E,SAAS,CAACmC,IAAI,GAAG,YAAY;EACpC,IAAI,CAAClB,cAAc,CAACjC,MAAM,EAAE;AAC9B,CAAC;AACD9D,QAAQ,CAAC8E,SAAS,CAACoC,MAAM,GAAG,YAAY;EACtC,IAAI1G,KAAK,GAAG,IAAI,CAACuF,cAAc;EAC/B,IAAIvF,KAAK,CAACsD,MAAM,EAAE;IAChBtD,KAAK,CAACsD,MAAM,EAAE;IACd,IAAI,CAACtD,KAAK,CAACqD,OAAO,IAAI,CAACrD,KAAK,CAACsD,MAAM,IAAI,CAACtD,KAAK,CAACwD,gBAAgB,IAAIxD,KAAK,CAAC6D,eAAe,EAAE8C,WAAW,CAAC,IAAI,EAAE3G,KAAK,CAAC;EACnH;AACF,CAAC;AACDR,QAAQ,CAAC8E,SAAS,CAACsC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACjH,QAAQ,EAAE;EAC5E;EACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,CAACkH,WAAW,CAAC,CAAC;EACnE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,CAACnH,QAAQ,GAAG,EAAE,EAAEkH,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI3E,oBAAoB,CAACvC,QAAQ,CAAC;EACjM,IAAI,CAAC4F,cAAc,CAACpC,eAAe,GAAGxD,QAAQ;EAC9C,OAAO,IAAI;AACb,CAAC;AACDgF,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAAC8E,SAAS,EAAE,gBAAgB,EAAE;EAC1D;EACA;EACA;EACAyC,UAAU,EAAE,KAAK;EACjBlC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACU,cAAc,IAAI,IAAI,CAACA,cAAc,CAAChB,SAAS,CAAC,CAAC;EAC/D;AACF,CAAC,CAAC;AACF,SAASyC,WAAWA,CAAChH,KAAK,EAAEN,KAAK,EAAEC,QAAQ,EAAE;EAC3C,IAAI,CAACK,KAAK,CAACwC,UAAU,IAAIxC,KAAK,CAACkD,aAAa,KAAK,KAAK,IAAI,OAAOxD,KAAK,KAAK,QAAQ,EAAE;IACnFA,KAAK,GAAGiB,MAAM,CAACO,IAAI,CAACxB,KAAK,EAAEC,QAAQ,CAAC;EACtC;EACA,OAAOD,KAAK;AACd;AACAiF,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAAC8E,SAAS,EAAE,uBAAuB,EAAE;EACjE;EACA;EACA;EACAyC,UAAU,EAAE,KAAK;EACjBlC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACU,cAAc,CAAC7C,aAAa;EAC1C;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAAS8D,aAAaA,CAAClE,MAAM,EAAEtC,KAAK,EAAEuG,KAAK,EAAE7G,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EAChE,IAAI,CAAC2G,KAAK,EAAE;IACV,IAAIU,QAAQ,GAAGD,WAAW,CAAChH,KAAK,EAAEN,KAAK,EAAEC,QAAQ,CAAC;IAClD,IAAID,KAAK,KAAKuH,QAAQ,EAAE;MACtBV,KAAK,GAAG,IAAI;MACZ5G,QAAQ,GAAG,QAAQ;MACnBD,KAAK,GAAGuH,QAAQ;IAClB;EACF;EACA,IAAIC,GAAG,GAAGlH,KAAK,CAACwC,UAAU,GAAG,CAAC,GAAG9C,KAAK,CAAC0D,MAAM;EAC7CpD,KAAK,CAACoD,MAAM,IAAI8D,GAAG;EACnB,IAAIZ,GAAG,GAAGtG,KAAK,CAACoD,MAAM,GAAGpD,KAAK,CAAC0C,aAAa;EAC5C;EACA,IAAI,CAAC4D,GAAG,EAAEtG,KAAK,CAAC4C,SAAS,GAAG,IAAI;EAChC,IAAI5C,KAAK,CAACqD,OAAO,IAAIrD,KAAK,CAACsD,MAAM,EAAE;IACjC,IAAI6D,IAAI,GAAGnH,KAAK,CAAC8D,mBAAmB;IACpC9D,KAAK,CAAC8D,mBAAmB,GAAG;MAC1BpE,KAAK,EAAEA,KAAK;MACZC,QAAQ,EAAEA,QAAQ;MAClB4G,KAAK,EAAEA,KAAK;MACZ1G,QAAQ,EAAED,EAAE;MACZE,IAAI,EAAE;IACR,CAAC;IACD,IAAIqH,IAAI,EAAE;MACRA,IAAI,CAACrH,IAAI,GAAGE,KAAK,CAAC8D,mBAAmB;IACvC,CAAC,MAAM;MACL9D,KAAK,CAAC6D,eAAe,GAAG7D,KAAK,CAAC8D,mBAAmB;IACnD;IACA9D,KAAK,CAACoE,oBAAoB,IAAI,CAAC;EACjC,CAAC,MAAM;IACLgD,OAAO,CAAC9E,MAAM,EAAEtC,KAAK,EAAE,KAAK,EAAEkH,GAAG,EAAExH,KAAK,EAAEC,QAAQ,EAAEC,EAAE,CAAC;EACzD;EACA,OAAO0G,GAAG;AACZ;AACA,SAASc,OAAOA,CAAC9E,MAAM,EAAEtC,KAAK,EAAE2F,MAAM,EAAEuB,GAAG,EAAExH,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EAChEI,KAAK,CAAC4D,QAAQ,GAAGsD,GAAG;EACpBlH,KAAK,CAAC2D,OAAO,GAAG/D,EAAE;EAClBI,KAAK,CAACqD,OAAO,GAAG,IAAI;EACpBrD,KAAK,CAACuD,IAAI,GAAG,IAAI;EACjB,IAAIvD,KAAK,CAACgD,SAAS,EAAEhD,KAAK,CAACyD,OAAO,CAAC,IAAI1B,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI4D,MAAM,EAAErD,MAAM,CAACsD,OAAO,CAAClG,KAAK,EAAEM,KAAK,CAACyD,OAAO,CAAC,CAAC,KAAKnB,MAAM,CAACoD,MAAM,CAAChG,KAAK,EAAEC,QAAQ,EAAEK,KAAK,CAACyD,OAAO,CAAC;EAC9KzD,KAAK,CAACuD,IAAI,GAAG,KAAK;AACpB;AACA,SAAS8D,YAAYA,CAAC/E,MAAM,EAAEtC,KAAK,EAAEuD,IAAI,EAAEG,EAAE,EAAE9D,EAAE,EAAE;EACjD,EAAEI,KAAK,CAAC+D,SAAS;EACjB,IAAIR,IAAI,EAAE;IACR;IACA;IACA4C,OAAO,CAACC,QAAQ,CAACxG,EAAE,EAAE8D,EAAE,CAAC;IACxB;IACA;IACAyC,OAAO,CAACC,QAAQ,CAACkB,WAAW,EAAEhF,MAAM,EAAEtC,KAAK,CAAC;IAC5CsC,MAAM,CAACiD,cAAc,CAACtB,YAAY,GAAG,IAAI;IACzC9B,cAAc,CAACG,MAAM,EAAEoB,EAAE,CAAC;EAC5B,CAAC,MAAM;IACL;IACA;IACA9D,EAAE,CAAC8D,EAAE,CAAC;IACNpB,MAAM,CAACiD,cAAc,CAACtB,YAAY,GAAG,IAAI;IACzC9B,cAAc,CAACG,MAAM,EAAEoB,EAAE,CAAC;IAC1B;IACA;IACA4D,WAAW,CAAChF,MAAM,EAAEtC,KAAK,CAAC;EAC5B;AACF;AACA,SAASuH,kBAAkBA,CAACvH,KAAK,EAAE;EACjCA,KAAK,CAACqD,OAAO,GAAG,KAAK;EACrBrD,KAAK,CAAC2D,OAAO,GAAG,IAAI;EACpB3D,KAAK,CAACoD,MAAM,IAAIpD,KAAK,CAAC4D,QAAQ;EAC9B5D,KAAK,CAAC4D,QAAQ,GAAG,CAAC;AACpB;AACA,SAASH,OAAOA,CAACnB,MAAM,EAAEoB,EAAE,EAAE;EAC3B,IAAI1D,KAAK,GAAGsC,MAAM,CAACiD,cAAc;EACjC,IAAIhC,IAAI,GAAGvD,KAAK,CAACuD,IAAI;EACrB,IAAI3D,EAAE,GAAGI,KAAK,CAAC2D,OAAO;EACtB,IAAI,OAAO/D,EAAE,KAAK,UAAU,EAAE,MAAM,IAAIiC,qBAAqB,CAAC,CAAC;EAC/D0F,kBAAkB,CAACvH,KAAK,CAAC;EACzB,IAAI0D,EAAE,EAAE2D,YAAY,CAAC/E,MAAM,EAAEtC,KAAK,EAAEuD,IAAI,EAAEG,EAAE,EAAE9D,EAAE,CAAC,CAAC,KAAK;IACrD;IACA,IAAImD,QAAQ,GAAGyE,UAAU,CAACxH,KAAK,CAAC,IAAIsC,MAAM,CAACU,SAAS;IACpD,IAAI,CAACD,QAAQ,IAAI,CAAC/C,KAAK,CAACsD,MAAM,IAAI,CAACtD,KAAK,CAACwD,gBAAgB,IAAIxD,KAAK,CAAC6D,eAAe,EAAE;MAClF8C,WAAW,CAACrE,MAAM,EAAEtC,KAAK,CAAC;IAC5B;IACA,IAAIuD,IAAI,EAAE;MACR4C,OAAO,CAACC,QAAQ,CAACqB,UAAU,EAAEnF,MAAM,EAAEtC,KAAK,EAAE+C,QAAQ,EAAEnD,EAAE,CAAC;IAC3D,CAAC,MAAM;MACL6H,UAAU,CAACnF,MAAM,EAAEtC,KAAK,EAAE+C,QAAQ,EAAEnD,EAAE,CAAC;IACzC;EACF;AACF;AACA,SAAS6H,UAAUA,CAACnF,MAAM,EAAEtC,KAAK,EAAE+C,QAAQ,EAAEnD,EAAE,EAAE;EAC/C,IAAI,CAACmD,QAAQ,EAAE2E,YAAY,CAACpF,MAAM,EAAEtC,KAAK,CAAC;EAC1CA,KAAK,CAAC+D,SAAS,EAAE;EACjBnE,EAAE,CAAC,CAAC;EACJ0H,WAAW,CAAChF,MAAM,EAAEtC,KAAK,CAAC;AAC5B;;AAEA;AACA;AACA;AACA,SAAS0H,YAAYA,CAACpF,MAAM,EAAEtC,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACoD,MAAM,KAAK,CAAC,IAAIpD,KAAK,CAAC4C,SAAS,EAAE;IACzC5C,KAAK,CAAC4C,SAAS,GAAG,KAAK;IACvBN,MAAM,CAACqF,IAAI,CAAC,OAAO,CAAC;EACtB;AACF;;AAEA;AACA,SAAShB,WAAWA,CAACrE,MAAM,EAAEtC,KAAK,EAAE;EAClCA,KAAK,CAACwD,gBAAgB,GAAG,IAAI;EAC7B,IAAItD,KAAK,GAAGF,KAAK,CAAC6D,eAAe;EACjC,IAAIvB,MAAM,CAACsD,OAAO,IAAI1F,KAAK,IAAIA,KAAK,CAACJ,IAAI,EAAE;IACzC;IACA,IAAI8H,CAAC,GAAG5H,KAAK,CAACoE,oBAAoB;IAClC,IAAIyD,MAAM,GAAG,IAAIC,KAAK,CAACF,CAAC,CAAC;IACzB,IAAIG,MAAM,GAAG/H,KAAK,CAACqE,kBAAkB;IACrC0D,MAAM,CAAC7H,KAAK,GAAGA,KAAK;IACpB,IAAI8H,KAAK,GAAG,CAAC;IACb,IAAIC,UAAU,GAAG,IAAI;IACrB,OAAO/H,KAAK,EAAE;MACZ2H,MAAM,CAACG,KAAK,CAAC,GAAG9H,KAAK;MACrB,IAAI,CAACA,KAAK,CAACqG,KAAK,EAAE0B,UAAU,GAAG,KAAK;MACpC/H,KAAK,GAAGA,KAAK,CAACJ,IAAI;MAClBkI,KAAK,IAAI,CAAC;IACZ;IACAH,MAAM,CAACI,UAAU,GAAGA,UAAU;IAC9Bb,OAAO,CAAC9E,MAAM,EAAEtC,KAAK,EAAE,IAAI,EAAEA,KAAK,CAACoD,MAAM,EAAEyE,MAAM,EAAE,EAAE,EAAEE,MAAM,CAAC5H,MAAM,CAAC;;IAErE;IACA;IACAH,KAAK,CAAC+D,SAAS,EAAE;IACjB/D,KAAK,CAAC8D,mBAAmB,GAAG,IAAI;IAChC,IAAIiE,MAAM,CAACjI,IAAI,EAAE;MACfE,KAAK,CAACqE,kBAAkB,GAAG0D,MAAM,CAACjI,IAAI;MACtCiI,MAAM,CAACjI,IAAI,GAAG,IAAI;IACpB,CAAC,MAAM;MACLE,KAAK,CAACqE,kBAAkB,GAAG,IAAItE,aAAa,CAACC,KAAK,CAAC;IACrD;IACAA,KAAK,CAACoE,oBAAoB,GAAG,CAAC;EAChC,CAAC,MAAM;IACL;IACA,OAAOlE,KAAK,EAAE;MACZ,IAAIR,KAAK,GAAGQ,KAAK,CAACR,KAAK;MACvB,IAAIC,QAAQ,GAAGO,KAAK,CAACP,QAAQ;MAC7B,IAAIC,EAAE,GAAGM,KAAK,CAACL,QAAQ;MACvB,IAAIqH,GAAG,GAAGlH,KAAK,CAACwC,UAAU,GAAG,CAAC,GAAG9C,KAAK,CAAC0D,MAAM;MAC7CgE,OAAO,CAAC9E,MAAM,EAAEtC,KAAK,EAAE,KAAK,EAAEkH,GAAG,EAAExH,KAAK,EAAEC,QAAQ,EAAEC,EAAE,CAAC;MACvDM,KAAK,GAAGA,KAAK,CAACJ,IAAI;MAClBE,KAAK,CAACoE,oBAAoB,EAAE;MAC5B;MACA;MACA;MACA;MACA,IAAIpE,KAAK,CAACqD,OAAO,EAAE;QACjB;MACF;IACF;IACA,IAAInD,KAAK,KAAK,IAAI,EAAEF,KAAK,CAAC8D,mBAAmB,GAAG,IAAI;EACtD;EACA9D,KAAK,CAAC6D,eAAe,GAAG3D,KAAK;EAC7BF,KAAK,CAACwD,gBAAgB,GAAG,KAAK;AAChC;AACAhE,QAAQ,CAAC8E,SAAS,CAACoB,MAAM,GAAG,UAAUhG,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACzDA,EAAE,CAAC,IAAIgC,0BAA0B,CAAC,UAAU,CAAC,CAAC;AAChD,CAAC;AACDpC,QAAQ,CAAC8E,SAAS,CAACsB,OAAO,GAAG,IAAI;AACjCpG,QAAQ,CAAC8E,SAAS,CAAC4D,GAAG,GAAG,UAAUxI,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACtD,IAAII,KAAK,GAAG,IAAI,CAACuF,cAAc;EAC/B,IAAI,OAAO7F,KAAK,KAAK,UAAU,EAAE;IAC/BE,EAAE,GAAGF,KAAK;IACVA,KAAK,GAAG,IAAI;IACZC,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IACzCC,EAAE,GAAGD,QAAQ;IACbA,QAAQ,GAAG,IAAI;EACjB;EACA,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKyI,SAAS,EAAE,IAAI,CAAC1C,KAAK,CAAC/F,KAAK,EAAEC,QAAQ,CAAC;;EAEtE;EACA,IAAIK,KAAK,CAACsD,MAAM,EAAE;IAChBtD,KAAK,CAACsD,MAAM,GAAG,CAAC;IAChB,IAAI,CAACoD,MAAM,CAAC,CAAC;EACf;;EAEA;EACA,IAAI,CAAC1G,KAAK,CAAC6C,MAAM,EAAEuF,WAAW,CAAC,IAAI,EAAEpI,KAAK,EAAEJ,EAAE,CAAC;EAC/C,OAAO,IAAI;AACb,CAAC;AACD+E,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAAC8E,SAAS,EAAE,gBAAgB,EAAE;EAC1D;EACA;EACA;EACAyC,UAAU,EAAE,KAAK;EACjBlC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACU,cAAc,CAACnC,MAAM;EACnC;AACF,CAAC,CAAC;AACF,SAASoE,UAAUA,CAACxH,KAAK,EAAE;EACzB,OAAOA,KAAK,CAAC6C,MAAM,IAAI7C,KAAK,CAACoD,MAAM,KAAK,CAAC,IAAIpD,KAAK,CAAC6D,eAAe,KAAK,IAAI,IAAI,CAAC7D,KAAK,CAAC+C,QAAQ,IAAI,CAAC/C,KAAK,CAACqD,OAAO;AAClH;AACA,SAASgF,SAASA,CAAC/F,MAAM,EAAEtC,KAAK,EAAE;EAChCsC,MAAM,CAAC0D,MAAM,CAAC,UAAUsC,GAAG,EAAE;IAC3BtI,KAAK,CAAC+D,SAAS,EAAE;IACjB,IAAIuE,GAAG,EAAE;MACPnG,cAAc,CAACG,MAAM,EAAEgG,GAAG,CAAC;IAC7B;IACAtI,KAAK,CAACgE,WAAW,GAAG,IAAI;IACxB1B,MAAM,CAACqF,IAAI,CAAC,WAAW,CAAC;IACxBL,WAAW,CAAChF,MAAM,EAAEtC,KAAK,CAAC;EAC5B,CAAC,CAAC;AACJ;AACA,SAASuI,SAASA,CAACjG,MAAM,EAAEtC,KAAK,EAAE;EAChC,IAAI,CAACA,KAAK,CAACgE,WAAW,IAAI,CAAChE,KAAK,CAAC2C,WAAW,EAAE;IAC5C,IAAI,OAAOL,MAAM,CAAC0D,MAAM,KAAK,UAAU,IAAI,CAAChG,KAAK,CAACgD,SAAS,EAAE;MAC3DhD,KAAK,CAAC+D,SAAS,EAAE;MACjB/D,KAAK,CAAC2C,WAAW,GAAG,IAAI;MACxBwD,OAAO,CAACC,QAAQ,CAACiC,SAAS,EAAE/F,MAAM,EAAEtC,KAAK,CAAC;IAC5C,CAAC,MAAM;MACLA,KAAK,CAACgE,WAAW,GAAG,IAAI;MACxB1B,MAAM,CAACqF,IAAI,CAAC,WAAW,CAAC;IAC1B;EACF;AACF;AACA,SAASL,WAAWA,CAAChF,MAAM,EAAEtC,KAAK,EAAE;EAClC,IAAIwI,IAAI,GAAGhB,UAAU,CAACxH,KAAK,CAAC;EAC5B,IAAIwI,IAAI,EAAE;IACRD,SAAS,CAACjG,MAAM,EAAEtC,KAAK,CAAC;IACxB,IAAIA,KAAK,CAAC+D,SAAS,KAAK,CAAC,EAAE;MACzB/D,KAAK,CAAC+C,QAAQ,GAAG,IAAI;MACrBT,MAAM,CAACqF,IAAI,CAAC,QAAQ,CAAC;MACrB,IAAI3H,KAAK,CAACmE,WAAW,EAAE;QACrB;QACA;QACA,IAAIsE,MAAM,GAAGnG,MAAM,CAACoG,cAAc;QAClC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACtE,WAAW,IAAIsE,MAAM,CAACE,UAAU,EAAE;UACtDrG,MAAM,CAACuD,OAAO,CAAC,CAAC;QAClB;MACF;IACF;EACF;EACA,OAAO2C,IAAI;AACb;AACA,SAASJ,WAAWA,CAAC9F,MAAM,EAAEtC,KAAK,EAAEJ,EAAE,EAAE;EACtCI,KAAK,CAAC6C,MAAM,GAAG,IAAI;EACnByE,WAAW,CAAChF,MAAM,EAAEtC,KAAK,CAAC;EAC1B,IAAIJ,EAAE,EAAE;IACN,IAAII,KAAK,CAAC+C,QAAQ,EAAEoD,OAAO,CAACC,QAAQ,CAACxG,EAAE,CAAC,CAAC,KAAK0C,MAAM,CAACsG,IAAI,CAAC,QAAQ,EAAEhJ,EAAE,CAAC;EACzE;EACAI,KAAK,CAAC8C,KAAK,GAAG,IAAI;EAClBR,MAAM,CAACkD,QAAQ,GAAG,KAAK;AACzB;AACA,SAASpF,cAAcA,CAACyI,OAAO,EAAE7I,KAAK,EAAEsI,GAAG,EAAE;EAC3C,IAAIpI,KAAK,GAAG2I,OAAO,CAAC3I,KAAK;EACzB2I,OAAO,CAAC3I,KAAK,GAAG,IAAI;EACpB,OAAOA,KAAK,EAAE;IACZ,IAAIN,EAAE,GAAGM,KAAK,CAACL,QAAQ;IACvBG,KAAK,CAAC+D,SAAS,EAAE;IACjBnE,EAAE,CAAC0I,GAAG,CAAC;IACPpI,KAAK,GAAGA,KAAK,CAACJ,IAAI;EACpB;;EAEA;EACAE,KAAK,CAACqE,kBAAkB,CAACvE,IAAI,GAAG+I,OAAO;AACzC;AACAlE,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAAC8E,SAAS,EAAE,WAAW,EAAE;EACrD;EACA;EACA;EACAyC,UAAU,EAAE,KAAK;EACjBlC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACU,cAAc,KAAK4C,SAAS,EAAE;MACrC,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAAC5C,cAAc,CAACvC,SAAS;EACtC,CAAC;EACD8F,GAAG,EAAE,SAASA,GAAGA,CAAC1D,KAAK,EAAE;IACvB;IACA;IACA,IAAI,CAAC,IAAI,CAACG,cAAc,EAAE;MACxB;IACF;;IAEA;IACA;IACA,IAAI,CAACA,cAAc,CAACvC,SAAS,GAAGoC,KAAK;EACvC;AACF,CAAC,CAAC;AACF5F,QAAQ,CAAC8E,SAAS,CAACuB,OAAO,GAAGvE,WAAW,CAACuE,OAAO;AAChDrG,QAAQ,CAAC8E,SAAS,CAACyE,UAAU,GAAGzH,WAAW,CAAC0H,SAAS;AACrDxJ,QAAQ,CAAC8E,SAAS,CAACwB,QAAQ,GAAG,UAAUwC,GAAG,EAAE1I,EAAE,EAAE;EAC/CA,EAAE,CAAC0I,GAAG,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}