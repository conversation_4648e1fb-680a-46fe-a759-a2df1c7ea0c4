require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'POLITICA_AUG',
  password: process.env.DB_PASSWORD || 'Rayvical',
  port: process.env.DB_PORT || 5432,
});

async function checkUsers() {
  try {
    console.log('🔍 Checking users in database...');
    
    const result = await pool.query('SELECT id, username, email, role, verified_status FROM users ORDER BY created_at DESC LIMIT 10');
    
    console.log(`📊 Found ${result.rows.length} users:`);
    result.rows.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Verified: ${user.verified_status}`);
      console.log('');
    });
    
    // Also check debates
    const debatesResult = await pool.query('SELECT id, title, status, moderator_id FROM live_debates ORDER BY created_at DESC LIMIT 5');
    console.log(`🎙️ Found ${debatesResult.rows.length} debates:`);
    debatesResult.rows.forEach((debate, index) => {
      console.log(`${index + 1}. ${debate.title} (${debate.status})`);
    });
    
  } catch (error) {
    console.error('❌ Error checking users:', error);
  } finally {
    await pool.end();
  }
}

checkUsers();
