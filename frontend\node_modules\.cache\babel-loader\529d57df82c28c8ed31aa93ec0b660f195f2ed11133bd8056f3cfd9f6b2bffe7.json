{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Home.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Welcome to Politica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your comprehensive platform for political education, discussion, and research\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          justifyContent: 'center',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/knowledge\",\n          className: \"btn btn-primary\",\n          children: \"Explore Knowledge Base\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/discussions\",\n          className: \"btn btn-secondary\",\n          children: \"Join Discussions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Knowledge Base\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Multi-level political education content from basic to advanced concepts.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/knowledge\",\n          style: {\n            color: '#3b82f6',\n            fontWeight: '500'\n          },\n          children: \"Learn More \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Discussion Forums\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Engage in thoughtful political discussions with moderated threads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/discussions\",\n          style: {\n            color: '#3b82f6',\n            fontWeight: '500'\n          },\n          children: \"Join Discussion \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Live Debates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Participate in scheduled live video debates on current political topics.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/debates\",\n          style: {\n            color: '#3b82f6',\n            fontWeight: '500'\n          },\n          children: \"Watch Debates \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Research Repository\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Access and submit peer-reviewed political research papers.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/research\",\n          style: {\n            color: '#3b82f6',\n            fontWeight: '500'\n          },\n          children: \"Browse Research \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '4rem 0',\n        backgroundColor: '#374151',\n        color: 'white',\n        margin: '2rem -1rem 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Ready to Get Started?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Join our community of informed citizens and contribute to meaningful political discourse.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        className: \"btn btn-primary\",\n        children: \"Create Account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Home", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gap", "justifyContent", "flexWrap", "to", "color", "fontWeight", "textAlign", "padding", "backgroundColor", "margin", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Home.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home = () => {\n  return (\n    <div className=\"container\">\n      <div className=\"hero\">\n        <h1>Welcome to Politica</h1>\n        <p>Your comprehensive platform for political education, discussion, and research</p>\n        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>\n          <Link to=\"/knowledge\" className=\"btn btn-primary\">\n            Explore Knowledge Base\n          </Link>\n          <Link to=\"/discussions\" className=\"btn btn-secondary\">\n            Join Discussions\n          </Link>\n        </div>\n      </div>\n\n      <div className=\"features\">\n        <div className=\"feature-card\">\n          <h3>Knowledge Base</h3>\n          <p>Multi-level political education content from basic to advanced concepts.</p>\n          <Link to=\"/knowledge\" style={{ color: '#3b82f6', fontWeight: '500' }}>\n            Learn More →\n          </Link>\n        </div>\n\n        <div className=\"feature-card\">\n          <h3>Discussion Forums</h3>\n          <p>Engage in thoughtful political discussions with moderated threads.</p>\n          <Link to=\"/discussions\" style={{ color: '#3b82f6', fontWeight: '500' }}>\n            Join Discussion →\n          </Link>\n        </div>\n\n        <div className=\"feature-card\">\n          <h3>Live Debates</h3>\n          <p>Participate in scheduled live video debates on current political topics.</p>\n          <Link to=\"/debates\" style={{ color: '#3b82f6', fontWeight: '500' }}>\n            Watch Debates →\n          </Link>\n        </div>\n\n        <div className=\"feature-card\">\n          <h3>Research Repository</h3>\n          <p>Access and submit peer-reviewed political research papers.</p>\n          <Link to=\"/research\" style={{ color: '#3b82f6', fontWeight: '500' }}>\n            Browse Research →\n          </Link>\n        </div>\n      </div>\n\n      <div style={{ textAlign: 'center', padding: '4rem 0', backgroundColor: '#374151', color: 'white', margin: '2rem -1rem 0' }}>\n        <h2>Ready to Get Started?</h2>\n        <p>Join our community of informed citizens and contribute to meaningful political discourse.</p>\n        <Link to=\"/register\" className=\"btn btn-primary\">\n          Create Account\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBH,OAAA;QAAAG,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BP,OAAA;QAAAG,QAAA,EAAG;MAA6E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpFP,OAAA;QAAKQ,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACvFH,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,YAAY;UAACX,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPP,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,cAAc;UAACX,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBP,OAAA;UAAAG,QAAA,EAAG;QAAwE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/EP,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,YAAY;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BP,OAAA;UAAAG,QAAA,EAAG;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzEP,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,cAAc;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBP,OAAA;UAAAG,QAAA,EAAG;QAAwE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/EP,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,UAAU;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BP,OAAA;UAAAG,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEP,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,WAAW;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKQ,KAAK,EAAE;QAAEQ,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,QAAQ;QAAEC,eAAe,EAAE,SAAS;QAAEJ,KAAK,EAAE,OAAO;QAAEK,MAAM,EAAE;MAAe,CAAE;MAAAhB,QAAA,gBACzHH,OAAA;QAAAG,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BP,OAAA;QAAAG,QAAA,EAAG;MAAyF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChGP,OAAA,CAACF,IAAI;QAACe,EAAE,EAAC,WAAW;QAACX,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEjD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GA3DInB,IAAI;AA6DV,eAAeA,IAAI;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}