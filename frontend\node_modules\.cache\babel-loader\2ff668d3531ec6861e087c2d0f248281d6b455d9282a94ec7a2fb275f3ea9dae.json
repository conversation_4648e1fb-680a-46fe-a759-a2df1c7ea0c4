{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\LiveDebates.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport DebateChat from '../components/DebateChat';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LiveDebates = () => {\n  _s();\n  const [debates, setDebates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedDebate, setSelectedDebate] = useState(null);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [activeChatDebateId, setActiveChatDebateId] = useState(null);\n  const [newDebate, setNewDebate] = useState({\n    title: '',\n    description: '',\n    topic: '',\n    scheduled_at: ''\n  });\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isVerified\n  } = useAuth();\n  const {\n    isConnected,\n    activeDebates,\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    getDebateParticipantCount,\n    getDebateMessages\n  } = useWebSocket();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  const fetchDebates = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/debates`);\n      setDebates(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch debates');\n      console.error('Error fetching debates:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n  useEffect(() => {\n    fetchDebates();\n  }, [fetchDebates]);\n  const handleCreateDebate = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a debate');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/debates`, newDebate, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewDebate({\n          title: '',\n          description: '',\n          topic: '',\n          scheduled_at: ''\n        });\n        setShowCreateForm(false);\n        fetchDebates(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create debate');\n      console.error('Error creating debate:', err);\n    }\n  };\n  const handleJoinDebate = async debateId => {\n    if (!isAuthenticated) {\n      setError('Please login to join a debate');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/debates/${debateId}/join`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        fetchDebates(); // Refresh to show updated participant list\n      }\n    } catch (err) {\n      setError('Failed to join debate');\n      console.error('Error joining debate:', err);\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'live':\n        return 'bg-red-100 text-red-800';\n      case 'scheduled':\n        return 'bg-blue-100 text-blue-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'live':\n        return '🔴';\n      case 'scheduled':\n        return '📅';\n      case 'completed':\n        return '✅';\n      default:\n        return '📅';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading debates...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDebates,\n          className: \"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Create debate form\n  if (showCreateForm) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCreateForm(false),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Schedule New Debate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Create a new live debate for the community.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateDebate,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"title\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Debate Title *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"title\",\n                value: newDebate.title,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  title: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter a compelling debate title\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"topic\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Topic/Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"topic\",\n                value: newDebate.topic,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  topic: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a topic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Healthcare Policy\",\n                  children: \"Healthcare Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Environmental Policy\",\n                  children: \"Environmental Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Economic Policy\",\n                  children: \"Economic Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Education Policy\",\n                  children: \"Education Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Foreign Policy\",\n                  children: \"Foreign Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Social Issues\",\n                  children: \"Social Issues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Constitutional Law\",\n                  children: \"Constitutional Law\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Electoral Reform\",\n                  children: \"Electoral Reform\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                value: newDebate.description,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  description: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"4\",\n                placeholder: \"Describe the debate topic and format\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"scheduled_at\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Scheduled Date & Time *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"datetime-local\",\n                id: \"scheduled_at\",\n                value: newDebate.scheduled_at,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  scheduled_at: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowCreateForm(false),\n                className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                children: \"Schedule Debate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Live Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600\",\n            children: \"Watch and participate in live political debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), isAuthenticated && isVerified && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateForm(true),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n          children: \"Schedule Debate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: \"All Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: debates.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400 text-4xl mb-4\",\n                children: \"\\uD83C\\uDF99\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"No debates scheduled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"Be the first to schedule a political debate!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), isAuthenticated && isVerified && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateForm(true),\n                className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\",\n                children: \"Schedule First Debate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this) : debates.map(debate => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: debate.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(debate.status)}`,\n                  children: [getStatusIcon(debate.status), \" \", debate.status]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-3\",\n                children: debate.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Topic:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 26\n                  }, this), \" \", debate.topic]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Scheduled:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 26\n                  }, this), \" \", formatDate(debate.scheduled_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Moderator:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 26\n                  }, this), \" \", debate.moderator_username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 23\n                }, this), debate.participant_count > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Participants:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 28\n                  }, this), \" \", debate.participant_count, \" joined\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex space-x-2\",\n                children: [debate.status === 'live' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\",\n                  children: \"\\uD83D\\uDD34 Join Live\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 25\n                }, this), debate.status === 'scheduled' && isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleJoinDebate(debate.id),\n                  className: \"bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700\",\n                  children: \"Join Debate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSelectedDebate(debate),\n                  className: \"border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50\",\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this)]\n            }, debate.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: \"Live Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), debates.filter(d => d.status === 'live').length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: debates.filter(d => d.status === 'live').map(debate => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-2 border-red-200 rounded-lg p-4 bg-red-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 text-lg mr-2\",\n                  children: \"\\uD83D\\uDD34\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: debate.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-auto bg-red-600 text-white px-2 py-1 rounded text-xs\",\n                  children: \"LIVE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-3\",\n                children: debate.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700\",\n                children: \"\\uD83D\\uDD34 Join Live Debate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this)]\n            }, debate.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-6xl mb-4\",\n              children: \"\\uD83D\\uDCFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-2\",\n              children: \"No Live Debates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: \"There are no debates currently live. Check back later or browse scheduled debates.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/login\",\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\",\n              children: \"Login to Participate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-12 bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-4\",\n          children: \"How Live Debates Work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Join the Stream\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Click to join the live video stream when a debate is active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Participate in Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Engage with other viewers through the live chat feature\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Vote in Polls\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Participate in real-time polls during the debate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), isAuthenticated && !isVerified && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-yellow-600 text-xl mr-3\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-yellow-800 mb-2\",\n              children: \"Verification Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-700\",\n              children: [\"To schedule debates and participate as a moderator, you need to be a verified user.\", /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/profile\",\n                className: \"underline ml-1\",\n                children: \"Request verification here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveDebates, \"EAxbQXAekGayKnx+aDffFECYtB8=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = LiveDebates;\nexport default LiveDebates;\nvar _c;\n$RefreshReg$(_c, \"LiveDebates\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "useWebSocket", "DebateChat", "axios", "jsxDEV", "_jsxDEV", "LiveDebates", "_s", "debates", "setDebates", "loading", "setLoading", "error", "setError", "selectedDebate", "setSelectedDebate", "showCreateForm", "setShowCreateForm", "activeChatDebateId", "setActiveChatDebateId", "newDebate", "setNewDebate", "title", "description", "topic", "scheduled_at", "user", "token", "isAuthenticated", "isVerified", "isConnected", "activeDebates", "joinDebate", "leaveDebate", "sendDebateMessage", "getDebateParticipantCount", "getDebateMessages", "API_URL", "process", "env", "REACT_APP_API_URL", "fetchDebates", "response", "get", "data", "err", "console", "handleCreateDebate", "e", "preventDefault", "post", "headers", "success", "handleJoinDebate", "debateId", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "getStatusColor", "status", "getStatusIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "rows", "length", "map", "debate", "moderator_username", "participant_count", "filter", "d", "href", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/LiveDebates.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport DebateChat from '../components/DebateChat';\nimport axios from 'axios';\n\nconst LiveDebates = () => {\n  const [debates, setDebates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedDebate, setSelectedDebate] = useState(null);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [activeChatDebateId, setActiveChatDebateId] = useState(null);\n  const [newDebate, setNewDebate] = useState({\n    title: '',\n    description: '',\n    topic: '',\n    scheduled_at: ''\n  });\n  const { user, token, isAuthenticated, isVerified } = useAuth();\n  const {\n    isConnected,\n    activeDebates,\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    getDebateParticipantCount,\n    getDebateMessages\n  } = useWebSocket();\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  const fetchDebates = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/debates`);\n      setDebates(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch debates');\n      console.error('Error fetching debates:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n\n  useEffect(() => {\n    fetchDebates();\n  }, [fetchDebates]);\n\n  const handleCreateDebate = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a debate');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/debates`,\n        newDebate,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewDebate({ title: '', description: '', topic: '', scheduled_at: '' });\n        setShowCreateForm(false);\n        fetchDebates(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create debate');\n      console.error('Error creating debate:', err);\n    }\n  };\n\n  const handleJoinDebate = async (debateId) => {\n    if (!isAuthenticated) {\n      setError('Please login to join a debate');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/debates/${debateId}/join`,\n        {},\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        fetchDebates(); // Refresh to show updated participant list\n      }\n    } catch (err) {\n      setError('Failed to join debate');\n      console.error('Error joining debate:', err);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'live':\n        return 'bg-red-100 text-red-800';\n      case 'scheduled':\n        return 'bg-blue-100 text-blue-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'live':\n        return '🔴';\n      case 'scheduled':\n        return '📅';\n      case 'completed':\n        return '✅';\n      default:\n        return '📅';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading debates...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-xl mb-4\">⚠️</div>\n          <p className=\"text-gray-600\">{error}</p>\n          <button\n            onClick={fetchDebates}\n            className=\"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Create debate form\n  if (showCreateForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setShowCreateForm(false)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Debates\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Schedule New Debate</h1>\n            <p className=\"text-gray-600\">Create a new live debate for the community.</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <form onSubmit={handleCreateDebate}>\n              <div className=\"mb-6\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Debate Title *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  value={newDebate.title}\n                  onChange={(e) => setNewDebate({ ...newDebate, title: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter a compelling debate title\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"topic\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Topic/Category *\n                </label>\n                <select\n                  id=\"topic\"\n                  value={newDebate.topic}\n                  onChange={(e) => setNewDebate({ ...newDebate, topic: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">Select a topic</option>\n                  <option value=\"Healthcare Policy\">Healthcare Policy</option>\n                  <option value=\"Environmental Policy\">Environmental Policy</option>\n                  <option value=\"Economic Policy\">Economic Policy</option>\n                  <option value=\"Education Policy\">Education Policy</option>\n                  <option value=\"Foreign Policy\">Foreign Policy</option>\n                  <option value=\"Social Issues\">Social Issues</option>\n                  <option value=\"Constitutional Law\">Constitutional Law</option>\n                  <option value=\"Electoral Reform\">Electoral Reform</option>\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description *\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={newDebate.description}\n                  onChange={(e) => setNewDebate({ ...newDebate, description: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"4\"\n                  placeholder=\"Describe the debate topic and format\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"scheduled_at\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Scheduled Date & Time *\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  id=\"scheduled_at\"\n                  value={newDebate.scheduled_at}\n                  onChange={(e) => setNewDebate({ ...newDebate, scheduled_at: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowCreateForm(false)}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Schedule Debate\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Live Debates</h1>\n            <p className=\"text-lg text-gray-600\">\n              Watch and participate in live political debates\n            </p>\n          </div>\n          {isAuthenticated && isVerified && (\n            <button\n              onClick={() => setShowCreateForm(true)}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n            >\n              Schedule Debate\n            </button>\n          )}\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* All Debates */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">All Debates</h2>\n            <div className=\"space-y-4\">\n              {debates.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"text-gray-400 text-4xl mb-4\">🎙️</div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debates scheduled</h3>\n                  <p className=\"text-gray-600 mb-4\">Be the first to schedule a political debate!</p>\n                  {isAuthenticated && isVerified && (\n                    <button\n                      onClick={() => setShowCreateForm(true)}\n                      className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n                    >\n                      Schedule First Debate\n                    </button>\n                  )}\n                </div>\n              ) : (\n                debates.map((debate) => (\n                  <div key={debate.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h3 className=\"text-lg font-medium text-gray-900\">{debate.title}</h3>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(debate.status)}`}>\n                        {getStatusIcon(debate.status)} {debate.status}\n                      </span>\n                    </div>\n                    <p className=\"text-gray-600 mb-3\">{debate.description}</p>\n                    <div className=\"text-sm text-gray-500 space-y-1\">\n                      <p><strong>Topic:</strong> {debate.topic}</p>\n                      <p><strong>Scheduled:</strong> {formatDate(debate.scheduled_at)}</p>\n                      <p><strong>Moderator:</strong> {debate.moderator_username}</p>\n                      {debate.participant_count > 0 && (\n                        <p><strong>Participants:</strong> {debate.participant_count} joined</p>\n                      )}\n                    </div>\n                    <div className=\"mt-4 flex space-x-2\">\n                      {debate.status === 'live' && (\n                        <button className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\">\n                          🔴 Join Live\n                        </button>\n                      )}\n                      {debate.status === 'scheduled' && isAuthenticated && (\n                        <button\n                          onClick={() => handleJoinDebate(debate.id)}\n                          className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700\"\n                        >\n                          Join Debate\n                        </button>\n                      )}\n                      <button\n                        onClick={() => setSelectedDebate(debate)}\n                        className=\"border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50\"\n                      >\n                        View Details\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Live Now / Featured */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Live Now</h2>\n            {debates.filter(d => d.status === 'live').length > 0 ? (\n              <div className=\"space-y-4\">\n                {debates.filter(d => d.status === 'live').map((debate) => (\n                  <div key={debate.id} className=\"border-2 border-red-200 rounded-lg p-4 bg-red-50\">\n                    <div className=\"flex items-center mb-2\">\n                      <span className=\"text-red-600 text-lg mr-2\">🔴</span>\n                      <h3 className=\"text-lg font-medium text-gray-900\">{debate.title}</h3>\n                      <span className=\"ml-auto bg-red-600 text-white px-2 py-1 rounded text-xs\">LIVE</span>\n                    </div>\n                    <p className=\"text-gray-600 mb-3\">{debate.description}</p>\n                    <button className=\"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700\">\n                      🔴 Join Live Debate\n                    </button>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">📺</div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Live Debates</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  There are no debates currently live. Check back later or browse scheduled debates.\n                </p>\n                {!isAuthenticated && (\n                  <a href=\"/login\" className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\">\n                    Login to Participate\n                  </a>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* How It Works */}\n        <div className=\"mt-12 bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">How Live Debates Work</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">1</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Join the Stream</h3>\n              <p className=\"text-sm text-gray-600\">\n                Click to join the live video stream when a debate is active\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">2</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Participate in Chat</h3>\n              <p className=\"text-sm text-gray-600\">\n                Engage with other viewers through the live chat feature\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">3</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Vote in Polls</h3>\n              <p className=\"text-sm text-gray-600\">\n                Participate in real-time polls during the debate\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Requirements Notice */}\n        {isAuthenticated && !isVerified && (\n          <div className=\"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"text-yellow-600 text-xl mr-3\">⚠️</div>\n              <div>\n                <h3 className=\"text-lg font-medium text-yellow-800 mb-2\">Verification Required</h3>\n                <p className=\"text-yellow-700\">\n                  To schedule debates and participate as a moderator, you need to be a verified user.\n                  <a href=\"/profile\" className=\"underline ml-1\">Request verification here</a>.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default LiveDebates;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC;IACzCyB,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC9D,MAAM;IACJ8B,WAAW;IACXC,aAAa;IACbC,UAAU;IACVC,WAAW;IACXC,iBAAiB;IACjBC,yBAAyB;IACzBC;EACF,CAAC,GAAGnC,YAAY,CAAC,CAAC;EAElB,MAAMoC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExE,MAAMC,YAAY,GAAG1C,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFY,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,GAAGN,OAAO,cAAc,CAAC;MAC1D5B,UAAU,CAACiC,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZhC,QAAQ,CAAC,yBAAyB,CAAC;MACnCiC,OAAO,CAAClC,KAAK,CAAC,yBAAyB,EAAEiC,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAC0B,OAAO,CAAC,CAAC;EAEbvC,SAAS,CAAC,MAAM;IACd2C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMM,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACrB,eAAe,EAAE;MACpBf,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMvC,KAAK,CAAC+C,IAAI,CAC/B,GAAGb,OAAO,cAAc,EACxBjB,SAAS,EACT;QACE+B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUxB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIe,QAAQ,CAACE,IAAI,CAACQ,OAAO,EAAE;QACzB/B,YAAY,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAC,CAAC;QACzER,iBAAiB,CAAC,KAAK,CAAC;QACxBwB,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZhC,QAAQ,CAAC,yBAAyB,CAAC;MACnCiC,OAAO,CAAClC,KAAK,CAAC,wBAAwB,EAAEiC,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAG,MAAOC,QAAQ,IAAK;IAC3C,IAAI,CAAC1B,eAAe,EAAE;MACpBf,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMvC,KAAK,CAAC+C,IAAI,CAC/B,GAAGb,OAAO,gBAAgBiB,QAAQ,OAAO,EACzC,CAAC,CAAC,EACF;QACEH,OAAO,EAAE;UACP,eAAe,EAAE,UAAUxB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIe,QAAQ,CAACE,IAAI,CAACQ,OAAO,EAAE;QACzBX,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZhC,QAAQ,CAAC,uBAAuB,CAAC;MACjCiC,OAAO,CAAClC,KAAK,CAAC,uBAAuB,EAAEiC,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMU,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAGF,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACvE,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,yBAAyB;MAClC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,aAAa,GAAID,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,WAAW;QACd,OAAO,GAAG;MACZ;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAIpD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK2D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAK2D,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FhE,OAAA;UAAG2D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIzD,KAAK,EAAE;IACT,oBACEP,OAAA;MAAK2D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAK2D,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnDhE,OAAA;UAAG2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAErD;QAAK;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxChE,OAAA;UACEiE,OAAO,EAAE7B,YAAa;UACtBuB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIrD,cAAc,EAAE;IAClB,oBACEX,OAAA;MAAK2D,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtC5D,OAAA;QAAK2D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D5D,OAAA;UAAK2D,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5D,OAAA;YACEiE,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,KAAK,CAAE;YACxC+C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YAAI2D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EhE,OAAA;YAAG2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eAENhE,OAAA;UAAK2D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD5D,OAAA;YAAMkE,QAAQ,EAAExB,kBAAmB;YAAAkB,QAAA,gBACjC5D,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOmE,OAAO,EAAC,OAAO;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhE,OAAA;gBACEoE,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEvD,SAAS,CAACE,KAAM;gBACvBsD,QAAQ,EAAG5B,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,KAAK,EAAE0B,CAAC,CAAC6B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEX,SAAS,EAAC,wFAAwF;gBAClGc,WAAW,EAAC,iCAAiC;gBAC7CC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOmE,OAAO,EAAC,OAAO;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhE,OAAA;gBACEqE,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEvD,SAAS,CAACI,KAAM;gBACvBoD,QAAQ,EAAG5B,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEI,KAAK,EAAEwB,CAAC,CAAC6B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEX,SAAS,EAAC,wFAAwF;gBAClGe,QAAQ;gBAAAd,QAAA,gBAER5D,OAAA;kBAAQsE,KAAK,EAAC,EAAE;kBAAAV,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxChE,OAAA;kBAAQsE,KAAK,EAAC,mBAAmB;kBAAAV,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DhE,OAAA;kBAAQsE,KAAK,EAAC,sBAAsB;kBAAAV,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClEhE,OAAA;kBAAQsE,KAAK,EAAC,iBAAiB;kBAAAV,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxDhE,OAAA;kBAAQsE,KAAK,EAAC,kBAAkB;kBAAAV,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1DhE,OAAA;kBAAQsE,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDhE,OAAA;kBAAQsE,KAAK,EAAC,eAAe;kBAAAV,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhE,OAAA;kBAAQsE,KAAK,EAAC,oBAAoB;kBAAAV,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DhE,OAAA;kBAAQsE,KAAK,EAAC,kBAAkB;kBAAAV,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOmE,OAAO,EAAC,aAAa;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhE,OAAA;gBACEqE,EAAE,EAAC,aAAa;gBAChBC,KAAK,EAAEvD,SAAS,CAACG,WAAY;gBAC7BqD,QAAQ,EAAG5B,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,WAAW,EAAEyB,CAAC,CAAC6B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC7EX,SAAS,EAAC,wFAAwF;gBAClGgB,IAAI,EAAC,GAAG;gBACRF,WAAW,EAAC,sCAAsC;gBAClDC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOmE,OAAO,EAAC,cAAc;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhE,OAAA;gBACEoE,IAAI,EAAC,gBAAgB;gBACrBC,EAAE,EAAC,cAAc;gBACjBC,KAAK,EAAEvD,SAAS,CAACK,YAAa;gBAC9BmD,QAAQ,EAAG5B,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEK,YAAY,EAAEuB,CAAC,CAAC6B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC9EX,SAAS,EAAC,wFAAwF;gBAClGe,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC5D,OAAA;gBACEoE,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,KAAK,CAAE;gBACxC+C,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA;gBACEoE,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC5D,OAAA;MAAK2D,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D5D,OAAA;QAAK2D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5D,OAAA;UAAA4D,QAAA,gBACE5D,OAAA;YAAI2D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEhE,OAAA;YAAG2D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLzC,eAAe,IAAIC,UAAU,iBAC5BxB,OAAA;UACEiE,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,IAAI,CAAE;UACvC+C,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAC1E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5D,OAAA;UAAK2D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5D,OAAA;YAAI2D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEhE,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzD,OAAO,CAACyE,MAAM,KAAK,CAAC,gBACnB5E,OAAA;cAAK2D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B5D,OAAA;gBAAK2D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtDhE,OAAA;gBAAI2D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFhE,OAAA;gBAAG2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFzC,eAAe,IAAIC,UAAU,iBAC5BxB,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,IAAI,CAAE;gBACvC+C,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,GAEN7D,OAAO,CAAC0E,GAAG,CAAEC,MAAM,iBACjB9E,OAAA;cAAqB2D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpE5D,OAAA;gBAAK2D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD5D,OAAA;kBAAI2D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEkB,MAAM,CAAC7D;gBAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrEhE,OAAA;kBAAM2D,SAAS,EAAE,8CAA8CH,cAAc,CAACsB,MAAM,CAACrB,MAAM,CAAC,EAAG;kBAAAG,QAAA,GAC5FF,aAAa,CAACoB,MAAM,CAACrB,MAAM,CAAC,EAAC,GAAC,EAACqB,MAAM,CAACrB,MAAM;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhE,OAAA;gBAAG2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEkB,MAAM,CAAC5D;cAAW;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DhE,OAAA;gBAAK2D,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C5D,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACc,MAAM,CAAC3D,KAAK;gBAAA;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7ChE,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACd,UAAU,CAAC4B,MAAM,CAAC1D,YAAY,CAAC;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEhE,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACc,MAAM,CAACC,kBAAkB;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC7Dc,MAAM,CAACE,iBAAiB,GAAG,CAAC,iBAC3BhF,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACc,MAAM,CAACE,iBAAiB,EAAC,SAAO;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNhE,OAAA;gBAAK2D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GACjCkB,MAAM,CAACrB,MAAM,KAAK,MAAM,iBACvBzD,OAAA;kBAAQ2D,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAExF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,EACAc,MAAM,CAACrB,MAAM,KAAK,WAAW,IAAIlC,eAAe,iBAC/CvB,OAAA;kBACEiE,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAAC8B,MAAM,CAACT,EAAE,CAAE;kBAC3CV,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,EAClF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACDhE,OAAA;kBACEiE,OAAO,EAAEA,CAAA,KAAMvD,iBAAiB,CAACoE,MAAM,CAAE;kBACzCnB,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,EAC/F;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GApCEc,MAAM,CAACT,EAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqCd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhE,OAAA;UAAK2D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5D,OAAA;YAAI2D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACrE7D,OAAO,CAAC8E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,MAAM,CAAC,CAACmB,MAAM,GAAG,CAAC,gBAClD5E,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzD,OAAO,CAAC8E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,MAAM,CAAC,CAACoB,GAAG,CAAEC,MAAM,iBACnD9E,OAAA;cAAqB2D,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/E5D,OAAA;gBAAK2D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC5D,OAAA;kBAAM2D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDhE,OAAA;kBAAI2D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEkB,MAAM,CAAC7D;gBAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrEhE,OAAA;kBAAM2D,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNhE,OAAA;gBAAG2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEkB,MAAM,CAAC5D;cAAW;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DhE,OAAA;gBAAQ2D,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GATDc,MAAM,CAACT,EAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENhE,OAAA;YAAK2D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5D,OAAA;cAAK2D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDhE,OAAA;cAAI2D,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EhE,OAAA;cAAG2D,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAACzC,eAAe,iBACfvB,OAAA;cAAGmF,IAAI,EAAC,QAAQ;cAACxB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE3F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhE,OAAA;QAAK2D,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD5D,OAAA;UAAI2D,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFhE,OAAA;UAAK2D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5D,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAK2D,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F5D,OAAA;gBAAM2D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNhE,OAAA;cAAI2D,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEhE,OAAA;cAAG2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAK2D,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F5D,OAAA;gBAAM2D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNhE,OAAA;cAAI2D,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEhE,OAAA;cAAG2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAK2D,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F5D,OAAA;gBAAM2D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNhE,OAAA;cAAI2D,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEhE,OAAA;cAAG2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzC,eAAe,IAAI,CAACC,UAAU,iBAC7BxB,OAAA;QAAK2D,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE5D,OAAA;UAAK2D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5D,OAAA;YAAK2D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtDhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAI2D,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFhE,OAAA;cAAG2D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,qFAE7B,eAAA5D,OAAA;gBAAGmF,IAAI,EAAC,UAAU;gBAACxB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,KAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAtbID,WAAW;EAAA,QAasCN,OAAO,EASxDC,YAAY;AAAA;AAAAwF,EAAA,GAtBZnF,WAAW;AAwbjB,eAAeA,WAAW;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}