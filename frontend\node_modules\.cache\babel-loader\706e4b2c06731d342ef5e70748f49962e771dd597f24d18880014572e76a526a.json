{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\components\\\\TrendingSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrendingSidebar = () => {\n  _s();\n  const [trendingHashtags, setTrendingHashtags] = useState([]);\n  const [trendingTopics, setTrendingTopics] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('topics');\n  useEffect(() => {\n    fetchTrendingData();\n  }, []);\n  const fetchTrendingData = async () => {\n    try {\n      const [hashtagsResponse, topicsResponse] = await Promise.all([axios.get('/api/feed/hashtags/trending'), axios.get('/api/feed/topics/trending')]);\n      setTrendingHashtags(hashtagsResponse.data);\n      setTrendingTopics(topicsResponse.data);\n    } catch (error) {\n      console.error('Error fetching trending data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatNumber = num => {\n    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;\n    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;\n    return num.toString();\n  };\n  const getCategoryEmoji = category => {\n    const emojis = {\n      domestic: '🏠',\n      foreign: '🌍',\n      economic: '💰',\n      social: '👥',\n      environmental: '🌱',\n      defense: '🛡️',\n      technology: '💻'\n    };\n    return emojis[category] || '📋';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-300 rounded w-3/4 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [...Array(5)].map((_, j) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded\"\n          }, j, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-2\",\n            children: \"\\uD83D\\uDD25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), \"Trending Now\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex border-b border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('topics'),\n          className: `flex-1 px-4 py-3 text-sm font-medium ${activeTab === 'topics' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-600 hover:text-gray-900'}`,\n          children: \"Topics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('hashtags'),\n          className: `flex-1 px-4 py-3 text-sm font-medium ${activeTab === 'hashtags' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-600 hover:text-gray-900'}`,\n          children: \"Hashtags\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [activeTab === 'topics' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: trendingTopics.slice(0, 8).map((topic, index) => {\n            var _topic$trending_score;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getCategoryEmoji(topic.category)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 116,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900\",\n                      children: topic.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 117,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: [formatNumber(topic.post_count), \" posts\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: ((_topic$trending_score = topic.trending_score) === null || _topic$trending_score === void 0 ? void 0 : _topic$trending_score.toFixed(1)) || '0.0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, topic.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: trendingHashtags.slice(0, 10).map((hashtag, index) => {\n            var _hashtag$trending_sco;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-blue-600\",\n                    children: [\"#\", hashtag.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [formatNumber(hashtag.post_count), \" posts\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: ((_hashtag$trending_sco = hashtag.trending_score) === null || _hashtag$trending_sco === void 0 ? void 0 : _hashtag$trending_sco.toFixed(1)) || '0.0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)]\n            }, hashtag.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), (activeTab === 'topics' && trendingTopics.length === 0 || activeTab === 'hashtags' && trendingHashtags.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl mb-2\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-sm\",\n            children: [\"No trending \", activeTab, \" yet\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), \"Platform Stats\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Active Discussions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-gray-900\",\n            children: \"1,234\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Live Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-green-600\",\n            children: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Online Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-blue-600\",\n            children: \"892\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Posts Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-gray-900\",\n            children: \"456\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: \"\\uD83D\\uDCA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), \"Suggested Topics\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [{\n          name: 'Election 2024',\n          category: 'domestic',\n          posts: 1250\n        }, {\n          name: 'Climate Action',\n          category: 'environmental',\n          posts: 890\n        }, {\n          name: 'Healthcare Debate',\n          category: 'domestic',\n          posts: 756\n        }, {\n          name: 'Tech Regulation',\n          category: 'technology',\n          posts: 432\n        }].map(topic => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: getCategoryEmoji(topic.category)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: topic.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [formatNumber(topic.posts), \" posts\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n            children: \"Follow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, topic.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), \"Who to Follow\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [{\n          username: 'PoliticalAnalyst',\n          role: 'verified',\n          followers: '12.5K'\n        }, {\n          username: 'PolicyExpert',\n          role: 'expert',\n          followers: '8.9K'\n        }, {\n          username: 'CitizenVoice',\n          role: 'user',\n          followers: '3.2K'\n        }].map(user => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold\",\n              children: user.username.charAt(0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), user.role === 'verified' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-500\",\n                  title: \"Verified\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [user.followers, \" followers\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm hover:bg-blue-700\",\n            children: \"Follow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)]\n        }, user.username, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(TrendingSidebar, \"wNQfs+7Lvw/cUXq4tzl2fD9W48E=\");\n_c = TrendingSidebar;\nexport default TrendingSidebar;\nvar _c;\n$RefreshReg$(_c, \"TrendingSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "TrendingSidebar", "_s", "trendingHashtags", "setTrendingHashtags", "trendingTopics", "setTrendingTopics", "loading", "setLoading", "activeTab", "setActiveTab", "fetchTrendingData", "hashtagsResponse", "topicsResponse", "Promise", "all", "get", "data", "error", "console", "formatNumber", "num", "toFixed", "toString", "getCategoryEmoji", "category", "emojis", "domestic", "foreign", "economic", "social", "environmental", "defense", "technology", "className", "children", "Array", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "j", "onClick", "slice", "topic", "index", "_topic$trending_score", "name", "post_count", "trending_score", "hashtag", "_hashtag$trending_sco", "length", "posts", "username", "role", "followers", "user", "char<PERSON>t", "title", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/components/TrendingSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst TrendingSidebar = () => {\n  const [trendingHashtags, setTrendingHashtags] = useState([]);\n  const [trendingTopics, setTrendingTopics] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('topics');\n\n  useEffect(() => {\n    fetchTrendingData();\n  }, []);\n\n  const fetchTrendingData = async () => {\n    try {\n      const [hashtagsResponse, topicsResponse] = await Promise.all([\n        axios.get('/api/feed/hashtags/trending'),\n        axios.get('/api/feed/topics/trending')\n      ]);\n\n      setTrendingHashtags(hashtagsResponse.data);\n      setTrendingTopics(topicsResponse.data);\n    } catch (error) {\n      console.error('Error fetching trending data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatNumber = (num) => {\n    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;\n    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;\n    return num.toString();\n  };\n\n  const getCategoryEmoji = (category) => {\n    const emojis = {\n      domestic: '🏠',\n      foreign: '🌍',\n      economic: '💰',\n      social: '👥',\n      environmental: '🌱',\n      defense: '🛡️',\n      technology: '💻'\n    };\n    return emojis[category] || '📋';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"bg-white rounded-lg shadow-sm p-6 animate-pulse\">\n            <div className=\"h-6 bg-gray-300 rounded w-3/4 mb-4\"></div>\n            <div className=\"space-y-3\">\n              {[...Array(5)].map((_, j) => (\n                <div key={j} className=\"h-4 bg-gray-300 rounded\"></div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Trending Section */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n        <div className=\"p-4 border-b border-gray-100\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <span className=\"mr-2\">🔥</span>\n            Trending Now\n          </h3>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"flex border-b border-gray-100\">\n          <button\n            onClick={() => setActiveTab('topics')}\n            className={`flex-1 px-4 py-3 text-sm font-medium ${\n              activeTab === 'topics'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Topics\n          </button>\n          <button\n            onClick={() => setActiveTab('hashtags')}\n            className={`flex-1 px-4 py-3 text-sm font-medium ${\n              activeTab === 'hashtags'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Hashtags\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4\">\n          {activeTab === 'topics' ? (\n            <div className=\"space-y-3\">\n              {trendingTopics.slice(0, 8).map((topic, index) => (\n                <div\n                  key={topic.name}\n                  className=\"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-sm font-medium text-gray-500\">\n                      {index + 1}\n                    </span>\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span>{getCategoryEmoji(topic.category)}</span>\n                        <h4 className=\"font-medium text-gray-900\">{topic.name}</h4>\n                      </div>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {formatNumber(topic.post_count)} posts\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {topic.trending_score?.toFixed(1) || '0.0'}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">score</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {trendingHashtags.slice(0, 10).map((hashtag, index) => (\n                <div\n                  key={hashtag.name}\n                  className=\"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-sm font-medium text-gray-500\">\n                      {index + 1}\n                    </span>\n                    <div>\n                      <h4 className=\"font-medium text-blue-600\">#{hashtag.name}</h4>\n                      <p className=\"text-xs text-gray-500\">\n                        {formatNumber(hashtag.post_count)} posts\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {hashtag.trending_score?.toFixed(1) || '0.0'}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">score</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {((activeTab === 'topics' && trendingTopics.length === 0) ||\n            (activeTab === 'hashtags' && trendingHashtags.length === 0)) && (\n            <div className=\"text-center py-8\">\n              <div className=\"text-4xl mb-2\">📊</div>\n              <p className=\"text-gray-500 text-sm\">\n                No trending {activeTab} yet\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <span className=\"mr-2\">📊</span>\n          Platform Stats\n        </h3>\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Active Discussions</span>\n            <span className=\"font-semibold text-gray-900\">1,234</span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Live Debates</span>\n            <span className=\"font-semibold text-green-600\">5</span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Online Users</span>\n            <span className=\"font-semibold text-blue-600\">892</span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Posts Today</span>\n            <span className=\"font-semibold text-gray-900\">456</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Suggested Topics */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <span className=\"mr-2\">💡</span>\n          Suggested Topics\n        </h3>\n        <div className=\"space-y-3\">\n          {[\n            { name: 'Election 2024', category: 'domestic', posts: 1250 },\n            { name: 'Climate Action', category: 'environmental', posts: 890 },\n            { name: 'Healthcare Debate', category: 'domestic', posts: 756 },\n            { name: 'Tech Regulation', category: 'technology', posts: 432 }\n          ].map((topic) => (\n            <div\n              key={topic.name}\n              className=\"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <span>{getCategoryEmoji(topic.category)}</span>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{topic.name}</h4>\n                  <p className=\"text-xs text-gray-500\">\n                    {formatNumber(topic.posts)} posts\n                  </p>\n                </div>\n              </div>\n              <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                Follow\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Who to Follow */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <span className=\"mr-2\">👥</span>\n          Who to Follow\n        </h3>\n        <div className=\"space-y-4\">\n          {[\n            { username: 'PoliticalAnalyst', role: 'verified', followers: '12.5K' },\n            { username: 'PolicyExpert', role: 'expert', followers: '8.9K' },\n            { username: 'CitizenVoice', role: 'user', followers: '3.2K' }\n          ].map((user) => (\n            <div key={user.username} className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold\">\n                  {user.username.charAt(0)}\n                </div>\n                <div>\n                  <div className=\"flex items-center space-x-1\">\n                    <h4 className=\"font-medium text-gray-900\">{user.username}</h4>\n                    {user.role === 'verified' && (\n                      <span className=\"text-blue-500\" title=\"Verified\">✓</span>\n                    )}\n                  </div>\n                  <p className=\"text-xs text-gray-500\">{user.followers} followers</p>\n                </div>\n              </div>\n              <button className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm hover:bg-blue-700\">\n                Follow\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrendingSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,QAAQ,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdc,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM,CAACC,gBAAgB,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3DjB,KAAK,CAACkB,GAAG,CAAC,6BAA6B,CAAC,EACxClB,KAAK,CAACkB,GAAG,CAAC,2BAA2B,CAAC,CACvC,CAAC;MAEFZ,mBAAmB,CAACQ,gBAAgB,CAACK,IAAI,CAAC;MAC1CX,iBAAiB,CAACO,cAAc,CAACI,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,YAAY,GAAIC,GAAG,IAAK;IAC5B,IAAIA,GAAG,IAAI,OAAO,EAAE,OAAO,GAAG,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3D,IAAID,GAAG,IAAI,IAAI,EAAE,OAAO,GAAG,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IACrD,OAAOD,GAAG,CAACE,QAAQ,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;IACd,CAAC;IACD,OAAOP,MAAM,CAACD,QAAQ,CAAC,IAAI,IAAI;EACjC,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKkC,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBvC,OAAA;QAAakC,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBACtEnC,OAAA;UAAKkC,SAAS,EAAC;QAAoC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D3C,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEM,CAAC,kBACtB5C,OAAA;YAAakC,SAAS,EAAC;UAAyB,GAAtCU,CAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA2C,CACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GANEJ,CAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAON,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACE3C,OAAA;IAAKkC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnC,OAAA;MAAKkC,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnFnC,OAAA;QAAKkC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CnC,OAAA;UAAIkC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACnEnC,OAAA;YAAMkC,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGN3C,OAAA;QAAKkC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CnC,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,QAAQ,CAAE;UACtCwB,SAAS,EAAE,wCACTzB,SAAS,KAAK,QAAQ,GAClB,qDAAqD,GACrD,mCAAmC,EACtC;UAAA0B,QAAA,EACJ;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,UAAU,CAAE;UACxCwB,SAAS,EAAE,wCACTzB,SAAS,KAAK,UAAU,GACpB,qDAAqD,GACrD,mCAAmC,EACtC;UAAA0B,QAAA,EACJ;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3C,OAAA;QAAKkC,SAAS,EAAC,KAAK;QAAAC,QAAA,GACjB1B,SAAS,KAAK,QAAQ,gBACrBT,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB9B,cAAc,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAC,CAACU,KAAK,EAAEC,KAAK;YAAA,IAAAC,qBAAA;YAAA,oBAC3CjD,OAAA;cAEEkC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9GnC,OAAA;gBAAKkC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnC,OAAA;kBAAMkC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAChDa,KAAK,GAAG;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACP3C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAKkC,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CnC,OAAA;sBAAAmC,QAAA,EAAOX,gBAAgB,CAACuB,KAAK,CAACtB,QAAQ;oBAAC;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/C3C,OAAA;sBAAIkC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEY,KAAK,CAACG;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACN3C,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GACtCf,YAAY,CAAC2B,KAAK,CAACI,UAAU,CAAC,EAAC,QAClC;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3C,OAAA;gBAAKkC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnC,OAAA;kBAAKkC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC/C,EAAAc,qBAAA,GAAAF,KAAK,CAACK,cAAc,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsB3B,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN3C,OAAA;kBAAKkC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA,GAtBDI,KAAK,CAACG,IAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBZ,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN3C,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBhC,gBAAgB,CAAC2C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACT,GAAG,CAAC,CAACgB,OAAO,EAAEL,KAAK;YAAA,IAAAM,qBAAA;YAAA,oBAChDtD,OAAA;cAEEkC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9GnC,OAAA;gBAAKkC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnC,OAAA;kBAAMkC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAChDa,KAAK,GAAG;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACP3C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAIkC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,GAAC,GAAC,EAACkB,OAAO,CAACH,IAAI;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9D3C,OAAA;oBAAGkC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACjCf,YAAY,CAACiC,OAAO,CAACF,UAAU,CAAC,EAAC,QACpC;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3C,OAAA;gBAAKkC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnC,OAAA;kBAAKkC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC/C,EAAAmB,qBAAA,GAAAD,OAAO,CAACD,cAAc,cAAAE,qBAAA,uBAAtBA,qBAAA,CAAwBhC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN3C,OAAA;kBAAKkC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA,GAnBDU,OAAO,CAACH,IAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBd,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA,CAAElC,SAAS,KAAK,QAAQ,IAAIJ,cAAc,CAACkD,MAAM,KAAK,CAAC,IACrD9C,SAAS,KAAK,UAAU,IAAIN,gBAAgB,CAACoD,MAAM,KAAK,CAAE,kBAC3DvD,OAAA;UAAKkC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BnC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC3C,OAAA;YAAGkC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,cACvB,EAAC1B,SAAS,EAAC,MACzB;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKkC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEnC,OAAA;QAAIkC,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACxEnC,OAAA;UAAMkC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kBAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3C,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAMkC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzD3C,OAAA;YAAMkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACN3C,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAMkC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnD3C,OAAA;YAAMkC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN3C,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAMkC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnD3C,OAAA;YAAMkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN3C,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAMkC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClD3C,OAAA;YAAMkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKkC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEnC,OAAA;QAAIkC,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACxEnC,OAAA;UAAMkC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,oBAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3C,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CACC;UAAEe,IAAI,EAAE,eAAe;UAAEzB,QAAQ,EAAE,UAAU;UAAE+B,KAAK,EAAE;QAAK,CAAC,EAC5D;UAAEN,IAAI,EAAE,gBAAgB;UAAEzB,QAAQ,EAAE,eAAe;UAAE+B,KAAK,EAAE;QAAI,CAAC,EACjE;UAAEN,IAAI,EAAE,mBAAmB;UAAEzB,QAAQ,EAAE,UAAU;UAAE+B,KAAK,EAAE;QAAI,CAAC,EAC/D;UAAEN,IAAI,EAAE,iBAAiB;UAAEzB,QAAQ,EAAE,YAAY;UAAE+B,KAAK,EAAE;QAAI,CAAC,CAChE,CAACnB,GAAG,CAAEU,KAAK,iBACV/C,OAAA;UAEEkC,SAAS,EAAC,oGAAoG;UAAAC,QAAA,gBAE9GnC,OAAA;YAAKkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CnC,OAAA;cAAAmC,QAAA,EAAOX,gBAAgB,CAACuB,KAAK,CAACtB,QAAQ;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C3C,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAIkC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEY,KAAK,CAACG;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3D3C,OAAA;gBAAGkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACjCf,YAAY,CAAC2B,KAAK,CAACS,KAAK,CAAC,EAAC,QAC7B;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3C,OAAA;YAAQkC,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAE1E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAdJI,KAAK,CAACG,IAAI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKkC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEnC,OAAA;QAAIkC,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACxEnC,OAAA;UAAMkC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,iBAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3C,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CACC;UAAEsB,QAAQ,EAAE,kBAAkB;UAAEC,IAAI,EAAE,UAAU;UAAEC,SAAS,EAAE;QAAQ,CAAC,EACtE;UAAEF,QAAQ,EAAE,cAAc;UAAEC,IAAI,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAO,CAAC,EAC/D;UAAEF,QAAQ,EAAE,cAAc;UAAEC,IAAI,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAO,CAAC,CAC9D,CAACtB,GAAG,CAAEuB,IAAI,iBACT5D,OAAA;UAAyBkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBACpEnC,OAAA;YAAKkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CnC,OAAA;cAAKkC,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAC1GyB,IAAI,CAACH,QAAQ,CAACI,MAAM,CAAC,CAAC;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACN3C,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAKkC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnC,OAAA;kBAAIkC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEyB,IAAI,CAACH;gBAAQ;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC7DiB,IAAI,CAACF,IAAI,KAAK,UAAU,iBACvB1D,OAAA;kBAAMkC,SAAS,EAAC,eAAe;kBAAC4B,KAAK,EAAC,UAAU;kBAAA3B,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACzD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN3C,OAAA;gBAAGkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEyB,IAAI,CAACD,SAAS,EAAC,YAAU;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3C,OAAA;YAAQkC,SAAS,EAAC,yEAAyE;YAAAC,QAAA,EAAC;UAE5F;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAjBDiB,IAAI,CAACH,QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBlB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CA1QID,eAAe;AAAA8D,EAAA,GAAf9D,eAAe;AA4QrB,eAAeA,eAAe;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}