const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const { verifyToken } = require('../middleware/auth');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'POLITICA_AUG',
  password: process.env.DB_PASSWORD || 'Rayvical',
  port: process.env.DB_PORT || 5432,
});

// Get comments for a post (threaded)
router.get('/post/:postId', async (req, res) => {
  try {
    const { postId } = req.params;
    const { page = 1, limit = 50, sort = 'best' } = req.query;
    const offset = (page - 1) * limit;
    const userId = req.user?.id;

    let orderClause = 'c.created_at ASC';
    if (sort === 'best') {
      orderClause = '(COALESCE(vote_counts.upvotes, 0) - COALESCE(vote_counts.downvotes, 0)) DESC, c.created_at ASC';
    } else if (sort === 'newest') {
      orderClause = 'c.created_at DESC';
    } else if (sort === 'oldest') {
      orderClause = 'c.created_at ASC';
    }

    const query = `
      WITH RECURSIVE comment_tree AS (
        -- Base case: top-level comments
        SELECT 
          c.*,
          u.username,
          u.email,
          u.role,
          u.verified_status,
          COALESCE(vote_counts.upvotes, 0) as upvotes,
          COALESCE(vote_counts.downvotes, 0) as downvotes,
          COALESCE(reaction_counts.total_reactions, 0) as total_reactions,
          user_vote.vote_type as user_vote,
          user_reactions.reactions as user_reactions,
          ARRAY[c.id] as path,
          0 as level
        FROM feed_comments c
        INNER JOIN users u ON c.user_id = u.id
        LEFT JOIN (
          SELECT
            comment_id,
            SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as upvotes,
            SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as downvotes
          FROM feed_comment_votes
          GROUP BY comment_id
        ) vote_counts ON c.id = vote_counts.comment_id
        LEFT JOIN (
          SELECT
            comment_id,
            COUNT(*) as total_reactions
          FROM feed_comment_reactions
          GROUP BY comment_id
        ) reaction_counts ON c.id = reaction_counts.comment_id
        LEFT JOIN feed_comment_votes user_vote ON c.id = user_vote.comment_id AND user_vote.user_id = $2
        LEFT JOIN (
          SELECT
            comment_id,
            ARRAY_AGG(reaction_type) as reactions
          FROM feed_comment_reactions
          WHERE user_id = $2
          GROUP BY comment_id
        ) user_reactions ON c.id = user_reactions.comment_id
        WHERE c.post_id = $1 AND c.parent_comment_id IS NULL AND c.deleted_at IS NULL
        
        UNION ALL
        
        -- Recursive case: child comments
        SELECT
          c.*,
          u.username,
          u.email,
          u.role,
          u.verified_status,
          COALESCE(vote_counts.upvotes, 0) as upvotes,
          COALESCE(vote_counts.downvotes, 0) as downvotes,
          COALESCE(reaction_counts.total_reactions, 0) as total_reactions,
          user_vote.vote_type as user_vote,
          user_reactions.reactions as user_reactions,
          ct.path || c.id,
          ct.level + 1
        FROM feed_comments c
        INNER JOIN users u ON c.user_id = u.id
        INNER JOIN comment_tree ct ON c.parent_comment_id = ct.id
        LEFT JOIN (
          SELECT
            comment_id,
            SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as upvotes,
            SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as downvotes
          FROM feed_comment_votes
          GROUP BY comment_id
        ) vote_counts ON c.id = vote_counts.comment_id
        LEFT JOIN (
          SELECT
            comment_id,
            COUNT(*) as total_reactions
          FROM feed_comment_reactions
          GROUP BY comment_id
        ) reaction_counts ON c.id = reaction_counts.comment_id
        LEFT JOIN feed_comment_votes user_vote ON c.id = user_vote.comment_id AND user_vote.user_id = $2
        LEFT JOIN (
          SELECT
            comment_id,
            ARRAY_AGG(reaction_type) as reactions
          FROM feed_comment_reactions
          WHERE user_id = $2
          GROUP BY comment_id
        ) user_reactions ON c.id = user_reactions.comment_id
        WHERE c.deleted_at IS NULL AND ct.level < 10 -- Limit nesting depth
      )
      SELECT * FROM comment_tree
      ORDER BY level, ${orderClause}
      LIMIT $3 OFFSET $4
    `;

    const result = await pool.query(query, [postId, userId, limit, offset]);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM feed_comments
      WHERE post_id = $1 AND deleted_at IS NULL
    `;
    const countResult = await pool.query(countQuery, [postId]);

    res.json({
      comments: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ error: 'Failed to fetch comments' });
  }
});

// Create a new comment
router.post('/', verifyToken, async (req, res) => {
  try {
    const { post_id, parent_comment_id, content } = req.body;
    const userId = req.user.id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Content is required' });
    }

    if (content.length > 1000) {
      return res.status(400).json({ error: 'Comment too long (max 1000 characters)' });
    }

    // Calculate depth if it's a reply
    let depth = 0;
    if (parent_comment_id) {
      const parentQuery = 'SELECT depth FROM feed_comments WHERE id = $1';
      const parentResult = await pool.query(parentQuery, [parent_comment_id]);

      if (parentResult.rows.length === 0) {
        return res.status(404).json({ error: 'Parent comment not found' });
      }

      depth = parentResult.rows[0].depth + 1;

      if (depth > 10) {
        return res.status(400).json({ error: 'Maximum nesting depth reached' });
      }
    }

    const query = `
      INSERT INTO feed_comments (post_id, user_id, parent_comment_id, content, depth)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const result = await pool.query(query, [post_id, userId, parent_comment_id, content, depth]);

    // Get the created comment with user info
    const commentQuery = `
      SELECT
        c.*,
        u.username,
        u.email,
        u.role,
        u.verified_status,
        0 as upvotes,
        0 as downvotes,
        0 as total_reactions
      FROM feed_comments c
      INNER JOIN users u ON c.user_id = u.id
      WHERE c.id = $1
    `;

    const commentResult = await pool.query(commentQuery, [result.rows[0].id]);

    res.status(201).json(commentResult.rows[0]);

  } catch (error) {
    console.error('Error creating comment:', error);
    res.status(500).json({ error: 'Failed to create comment' });
  }
});

// Vote on a comment
router.post('/:commentId/vote', verifyToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { vote_type } = req.body; // 1 for upvote, -1 for downvote, 0 to remove vote
    const userId = req.user.id;

    if (![1, -1, 0].includes(vote_type)) {
      return res.status(400).json({ error: 'Invalid vote type' });
    }

    if (vote_type === 0) {
      // Remove vote
      await pool.query('DELETE FROM feed_comment_votes WHERE comment_id = $1 AND user_id = $2', [commentId, userId]);
    } else {
      // Add or update vote
      await pool.query(`
        INSERT INTO feed_comment_votes (comment_id, user_id, vote_type)
        VALUES ($1, $2, $3)
        ON CONFLICT (comment_id, user_id)
        DO UPDATE SET vote_type = $3, updated_at = NOW()
      `, [commentId, userId, vote_type]);
    }

    // Get updated vote counts
    const voteCountQuery = `
      SELECT
        SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as upvotes,
        SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as downvotes
      FROM feed_comment_votes
      WHERE comment_id = $1
    `;

    const voteResult = await pool.query(voteCountQuery, [commentId]);

    res.json({
      upvotes: parseInt(voteResult.rows[0].upvotes) || 0,
      downvotes: parseInt(voteResult.rows[0].downvotes) || 0,
      user_vote: vote_type === 0 ? null : vote_type
    });

  } catch (error) {
    console.error('Error voting on comment:', error);
    res.status(500).json({ error: 'Failed to vote on comment' });
  }
});

// React to a comment
router.post('/:commentId/react', verifyToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { reaction_type } = req.body;
    const userId = req.user.id;

    const validReactions = ['like', 'love', 'laugh', 'angry', 'sad', 'support', 'oppose'];
    if (!validReactions.includes(reaction_type)) {
      return res.status(400).json({ error: 'Invalid reaction type' });
    }

    // Toggle reaction
    const existingReaction = await pool.query(
      'SELECT id FROM feed_comment_reactions WHERE comment_id = $1 AND user_id = $2 AND reaction_type = $3',
      [commentId, userId, reaction_type]
    );

    if (existingReaction.rows.length > 0) {
      // Remove reaction
      await pool.query(
        'DELETE FROM feed_comment_reactions WHERE comment_id = $1 AND user_id = $2 AND reaction_type = $3',
        [commentId, userId, reaction_type]
      );
    } else {
      // Add reaction
      await pool.query(
        'INSERT INTO feed_comment_reactions (comment_id, user_id, reaction_type) VALUES ($1, $2, $3)',
        [commentId, userId, reaction_type]
      );
    }

    // Get updated reaction counts
    const reactionCountQuery = `
      SELECT
        reaction_type,
        COUNT(*) as count
      FROM feed_comment_reactions
      WHERE comment_id = $1
      GROUP BY reaction_type
    `;

    const reactionResult = await pool.query(reactionCountQuery, [commentId]);

    res.json({
      reactions: reactionResult.rows.reduce((acc, row) => {
        acc[row.reaction_type] = parseInt(row.count);
        return acc;
      }, {})
    });

  } catch (error) {
    console.error('Error reacting to comment:', error);
    res.status(500).json({ error: 'Failed to react to comment' });
  }
});

module.exports = router;
