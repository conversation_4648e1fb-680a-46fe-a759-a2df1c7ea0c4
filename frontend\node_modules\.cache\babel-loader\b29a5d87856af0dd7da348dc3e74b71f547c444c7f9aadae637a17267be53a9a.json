{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport KnowledgeBase from './pages/KnowledgeBase';\nimport Discussions from './pages/Discussions';\nimport LiveDebates from './pages/LiveDebates';\nimport SocialFeed from './pages/SocialFeed';\nimport ResearchRepository from './pages/ResearchRepository';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Profile from './pages/Profile';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ApiProvider } from './contexts/ApiContext';\nimport { WebSocketProvider } from './contexts/WebSocketContext';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(ApiProvider, {\n      children: /*#__PURE__*/_jsxDEV(WebSocketProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"App\",\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n              className: \"main-content\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 28,\n                    columnNumber: 44\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/feed\",\n                  element: /*#__PURE__*/_jsxDEV(SocialFeed, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 29,\n                    columnNumber: 48\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/knowledge\",\n                  element: /*#__PURE__*/_jsxDEV(KnowledgeBase, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 30,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/discussions\",\n                  element: /*#__PURE__*/_jsxDEV(Discussions, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 31,\n                    columnNumber: 55\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/debates\",\n                  element: /*#__PURE__*/_jsxDEV(LiveDebates, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/research\",\n                  element: /*#__PURE__*/_jsxDEV(ResearchRepository, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 52\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 34,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/register\",\n                  element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 52\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/profile\",\n                  element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON>", "Home", "KnowledgeBase", "Discussions", "LiveDebates", "SocialFeed", "ResearchRepository", "<PERSON><PERSON>", "Register", "Profile", "<PERSON>th<PERSON><PERSON><PERSON>", "A<PERSON><PERSON><PERSON><PERSON>", "WebSocketProvider", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport KnowledgeBase from './pages/KnowledgeBase';\nimport Discussions from './pages/Discussions';\nimport LiveDebates from './pages/LiveDebates';\nimport SocialFeed from './pages/SocialFeed';\nimport ResearchRepository from './pages/ResearchRepository';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Profile from './pages/Profile';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ApiProvider } from './contexts/ApiContext';\nimport { WebSocketProvider } from './contexts/WebSocketContext';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <ApiProvider>\n        <WebSocketProvider>\n          <Router>\n            <div className=\"App\">\n              <Navbar />\n              <main className=\"main-content\">\n                <Routes>\n                  <Route path=\"/\" element={<Home />} />\n                  <Route path=\"/feed\" element={<SocialFeed />} />\n                  <Route path=\"/knowledge\" element={<KnowledgeBase />} />\n                  <Route path=\"/discussions\" element={<Discussions />} />\n                  <Route path=\"/debates\" element={<LiveDebates />} />\n                  <Route path=\"/research\" element={<ResearchRepository />} />\n                  <Route path=\"/login\" element={<Login />} />\n                  <Route path=\"/register\" element={<Register />} />\n                  <Route path=\"/profile\" element={<Profile />} />\n                </Routes>\n              </main>\n            </div>\n          </Router>\n        </WebSocketProvider>\n      </ApiProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACJ,YAAY;IAAAM,QAAA,eACXF,OAAA,CAACH,WAAW;MAAAK,QAAA,eACVF,OAAA,CAACF,iBAAiB;QAAAI,QAAA,eAChBF,OAAA,CAACjB,MAAM;UAAAmB,QAAA,eACLF,OAAA;YAAKG,SAAS,EAAC,KAAK;YAAAD,QAAA,gBAClBF,OAAA,CAACd,MAAM;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVP,OAAA;cAAMG,SAAS,EAAC,cAAc;cAAAD,QAAA,eAC5BF,OAAA,CAAChB,MAAM;gBAAAkB,QAAA,gBACLF,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAET,OAAA,CAACb,IAAI;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,OAAO;kBAACC,OAAO,eAAET,OAAA,CAACT,UAAU;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,YAAY;kBAACC,OAAO,eAAET,OAAA,CAACZ,aAAa;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvDP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,cAAc;kBAACC,OAAO,eAAET,OAAA,CAACX,WAAW;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvDP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAET,OAAA,CAACV,WAAW;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAET,OAAA,CAACR,kBAAkB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAET,OAAA,CAACP,KAAK;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3CP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAET,OAAA,CAACN,QAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDP,OAAA,CAACf,KAAK;kBAACuB,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAET,OAAA,CAACL,OAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACG,EAAA,GA3BQT,GAAG;AA6BZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}