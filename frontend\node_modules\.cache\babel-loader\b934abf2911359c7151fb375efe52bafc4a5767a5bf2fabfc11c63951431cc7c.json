{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Discussions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Discussions = () => {\n  _s();\n  const [threads, setThreads] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedThread, setSelectedThread] = useState(null);\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState('');\n  const [showNewThreadForm, setShowNewThreadForm] = useState(false);\n  const [newThread, setNewThread] = useState({\n    title: '',\n    description: '',\n    category: ''\n  });\n  const {\n    user,\n    token,\n    isAuthenticated\n  } = useAuth();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  useEffect(() => {\n    fetchThreads();\n  }, [fetchThreads]);\n  const fetchThreads = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/discussions`);\n      setThreads(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch discussions');\n      console.error('Error fetching discussions:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchComments = async threadId => {\n    try {\n      const response = await axios.get(`${API_URL}/api/discussions/${threadId}`);\n      setComments(response.data.data.comments || []);\n    } catch (err) {\n      console.error('Error fetching comments:', err);\n    }\n  };\n  const handleThreadClick = thread => {\n    setSelectedThread(thread);\n    fetchComments(thread.id);\n  };\n  const handleCreateThread = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a discussion');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/discussions`, newThread, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewThread({\n          title: '',\n          description: '',\n          category: ''\n        });\n        setShowNewThreadForm(false);\n        fetchThreads(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create discussion');\n      console.error('Error creating discussion:', err);\n    }\n  };\n  const handleAddComment = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to comment');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/comments`, {\n        discussion_id: selectedThread.id,\n        content: newComment\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewComment('');\n        fetchComments(selectedThread.id); // Refresh comments\n      }\n    } catch (err) {\n      setError('Failed to add comment');\n      console.error('Error adding comment:', err);\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading discussions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchThreads,\n          className: \"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If viewing a specific thread\n  if (selectedThread) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedThread(null),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Discussions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: selectedThread.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: selectedThread.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gray-100 px-2 py-1 rounded\",\n              children: selectedThread.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"by \", selectedThread.author_username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Created: \", formatDate(selectedThread.created_at)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Comments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 text-center py-8\",\n              children: \"No comments yet. Be the first to comment!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-l-4 border-blue-200 pl-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: comment.author_username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: formatDate(comment.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-700 whitespace-pre-wrap\",\n                  children: comment.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), comment.upvotes > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-sm text-gray-500\",\n                  children: [\"\\uD83D\\uDC4D \", comment.upvotes, \" upvotes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 25\n                }, this)]\n              }, comment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Add a Comment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleAddComment,\n            children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newComment,\n              onChange: e => setNewComment(e.target.value),\n              placeholder: \"Share your thoughts...\",\n              className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n              rows: \"4\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n                children: \"Post Comment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-100 rounded-lg p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: \"Please login to participate in the discussion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this);\n  }\n\n  // New Thread Form\n  if (showNewThreadForm) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNewThreadForm(false),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Discussions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Start New Discussion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateThread,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"title\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Discussion Title\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"title\",\n                value: newThread.title,\n                onChange: e => setNewThread({\n                  ...newThread,\n                  title: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter a clear, descriptive title\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category\",\n                value: newThread.category,\n                onChange: e => setNewThread({\n                  ...newThread,\n                  category: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Elections\",\n                  children: \"Elections\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Policy\",\n                  children: \"Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Campaign Finance\",\n                  children: \"Campaign Finance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Constitutional Law\",\n                  children: \"Constitutional Law\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"International Relations\",\n                  children: \"International Relations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Local Government\",\n                  children: \"Local Government\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"General Discussion\",\n                  children: \"General Discussion\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                value: newThread.description,\n                onChange: e => setNewThread({\n                  ...newThread,\n                  description: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"6\",\n                placeholder: \"Provide context and details for your discussion topic\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowNewThreadForm(false),\n                className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                children: \"Create Discussion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main discussions list view\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Discussion Forums\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Engage in thoughtful political discussions with the community\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Recent Discussions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowNewThreadForm(true),\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\",\n              children: \"Start New Discussion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/login\",\n              className: \"bg-gray-400 text-white px-4 py-2 rounded-md cursor-not-allowed\",\n              title: \"Login required\",\n              children: \"Login to Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divide-y divide-gray-200\",\n          children: threads.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-12 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-6xl mb-4\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No discussions yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: \"Be the first to start a political discussion!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowNewThreadForm(true),\n              className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n              children: \"Start First Discussion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this) : threads.map(thread => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 hover:bg-gray-50 cursor-pointer\",\n            onClick: () => handleThreadClick(thread),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: thread.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-3\",\n                  children: thread.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-gray-100 px-2 py-1 rounded\",\n                    children: thread.category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"by \", thread.author_username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Created: \", formatDate(thread.created_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this), thread.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [thread.comment_count, \" comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-gray-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this)\n          }, thread.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n};\n_s(Discussions, \"0nkyUyLMtVYE19fHiFjeMbSyYhQ=\", false, function () {\n  return [useAuth];\n});\n_c = Discussions;\nexport default Discussions;\nvar _c;\n$RefreshReg$(_c, \"Discussions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "axios", "jsxDEV", "_jsxDEV", "Discussions", "_s", "threads", "setThreads", "loading", "setLoading", "error", "setError", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedThread", "comments", "setComments", "newComment", "setNewComment", "showNewThreadForm", "setShowNewThreadForm", "newThread", "setNewThread", "title", "description", "category", "user", "token", "isAuthenticated", "API_URL", "process", "env", "REACT_APP_API_URL", "fetchThreads", "response", "get", "data", "err", "console", "fetchComments", "threadId", "handleThreadClick", "thread", "id", "handleCreateThread", "e", "preventDefault", "post", "headers", "success", "handleAddComment", "discussion_id", "content", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "author_username", "created_at", "length", "map", "comment", "upvotes", "onSubmit", "value", "onChange", "target", "placeholder", "rows", "required", "type", "href", "htmlFor", "comment_count", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Discussions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst Discussions = () => {\n  const [threads, setThreads] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedThread, setSelectedThread] = useState(null);\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState('');\n  const [showNewThreadForm, setShowNewThreadForm] = useState(false);\n  const [newThread, setNewThread] = useState({\n    title: '',\n    description: '',\n    category: ''\n  });\n  const { user, token, isAuthenticated } = useAuth();\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  useEffect(() => {\n    fetchThreads();\n  }, [fetchThreads]);\n\n  const fetchThreads = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/discussions`);\n      setThreads(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch discussions');\n      console.error('Error fetching discussions:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchComments = async (threadId) => {\n    try {\n      const response = await axios.get(`${API_URL}/api/discussions/${threadId}`);\n      setComments(response.data.data.comments || []);\n    } catch (err) {\n      console.error('Error fetching comments:', err);\n    }\n  };\n\n  const handleThreadClick = (thread) => {\n    setSelectedThread(thread);\n    fetchComments(thread.id);\n  };\n\n  const handleCreateThread = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a discussion');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/discussions`,\n        newThread,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewThread({ title: '', description: '', category: '' });\n        setShowNewThreadForm(false);\n        fetchThreads(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create discussion');\n      console.error('Error creating discussion:', err);\n    }\n  };\n\n  const handleAddComment = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to comment');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/comments`,\n        {\n          discussion_id: selectedThread.id,\n          content: newComment\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewComment('');\n        fetchComments(selectedThread.id); // Refresh comments\n      }\n    } catch (err) {\n      setError('Failed to add comment');\n      console.error('Error adding comment:', err);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading discussions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-xl mb-4\">⚠️</div>\n          <p className=\"text-gray-600\">{error}</p>\n          <button\n            onClick={fetchThreads}\n            className=\"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // If viewing a specific thread\n  if (selectedThread) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setSelectedThread(null)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Discussions\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{selectedThread.title}</h1>\n            <p className=\"text-gray-600 mb-4\">{selectedThread.description}</p>\n            <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n              <span className=\"bg-gray-100 px-2 py-1 rounded\">{selectedThread.category}</span>\n              <span>by {selectedThread.author_username}</span>\n              <span>Created: {formatDate(selectedThread.created_at)}</span>\n            </div>\n          </div>\n\n          {/* Comments Section */}\n          <div className=\"bg-white rounded-lg shadow-md mb-6\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">Comments</h2>\n            </div>\n            <div className=\"p-6\">\n              {comments.length === 0 ? (\n                <p className=\"text-gray-500 text-center py-8\">No comments yet. Be the first to comment!</p>\n              ) : (\n                <div className=\"space-y-6\">\n                  {comments.map((comment) => (\n                    <div key={comment.id} className=\"border-l-4 border-blue-200 pl-4\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <span className=\"font-medium text-gray-900\">{comment.author_username}</span>\n                        <span className=\"text-sm text-gray-500\">{formatDate(comment.created_at)}</span>\n                      </div>\n                      <p className=\"text-gray-700 whitespace-pre-wrap\">{comment.content}</p>\n                      {comment.upvotes > 0 && (\n                        <div className=\"mt-2 text-sm text-gray-500\">\n                          👍 {comment.upvotes} upvotes\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Add Comment Form */}\n          {isAuthenticated ? (\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Add a Comment</h3>\n              <form onSubmit={handleAddComment}>\n                <textarea\n                  value={newComment}\n                  onChange={(e) => setNewComment(e.target.value)}\n                  placeholder=\"Share your thoughts...\"\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"4\"\n                  required\n                />\n                <div className=\"mt-4 flex justify-end\">\n                  <button\n                    type=\"submit\"\n                    className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n                  >\n                    Post Comment\n                  </button>\n                </div>\n              </form>\n            </div>\n          ) : (\n            <div className=\"bg-gray-100 rounded-lg p-6 text-center\">\n              <p className=\"text-gray-600 mb-4\">Please login to participate in the discussion</p>\n              <a href=\"/login\" className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\">\n                Login\n              </a>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  // New Thread Form\n  if (showNewThreadForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setShowNewThreadForm(false)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Discussions\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Start New Discussion</h1>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <form onSubmit={handleCreateThread}>\n              <div className=\"mb-6\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Discussion Title\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  value={newThread.title}\n                  onChange={(e) => setNewThread({ ...newThread, title: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter a clear, descriptive title\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category\n                </label>\n                <select\n                  id=\"category\"\n                  value={newThread.category}\n                  onChange={(e) => setNewThread({ ...newThread, category: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">Select a category</option>\n                  <option value=\"Elections\">Elections</option>\n                  <option value=\"Policy\">Policy</option>\n                  <option value=\"Campaign Finance\">Campaign Finance</option>\n                  <option value=\"Constitutional Law\">Constitutional Law</option>\n                  <option value=\"International Relations\">International Relations</option>\n                  <option value=\"Local Government\">Local Government</option>\n                  <option value=\"General Discussion\">General Discussion</option>\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={newThread.description}\n                  onChange={(e) => setNewThread({ ...newThread, description: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"6\"\n                  placeholder=\"Provide context and details for your discussion topic\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowNewThreadForm(false)}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Create Discussion\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Main discussions list view\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Discussion Forums</h1>\n          <p className=\"text-lg text-gray-600\">\n            Engage in thoughtful political discussions with the community\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-md\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">Recent Discussions</h2>\n              {isAuthenticated ? (\n                <button\n                  onClick={() => setShowNewThreadForm(true)}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n                >\n                  Start New Discussion\n                </button>\n              ) : (\n                <a\n                  href=\"/login\"\n                  className=\"bg-gray-400 text-white px-4 py-2 rounded-md cursor-not-allowed\"\n                  title=\"Login required\"\n                >\n                  Login to Post\n                </a>\n              )}\n            </div>\n          </div>\n\n          <div className=\"divide-y divide-gray-200\">\n            {threads.length === 0 ? (\n              <div className=\"p-12 text-center\">\n                <div className=\"text-gray-400 text-6xl mb-4\">💬</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No discussions yet</h3>\n                <p className=\"text-gray-600 mb-4\">Be the first to start a political discussion!</p>\n                {isAuthenticated && (\n                  <button\n                    onClick={() => setShowNewThreadForm(true)}\n                    className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n                  >\n                    Start First Discussion\n                  </button>\n                )}\n              </div>\n            ) : (\n              threads.map((thread) => (\n                <div\n                  key={thread.id}\n                  className=\"p-6 hover:bg-gray-50 cursor-pointer\"\n                  onClick={() => handleThreadClick(thread)}\n                >\n                  <div className=\"flex justify-between items-start\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                        {thread.title}\n                      </h3>\n                      <p className=\"text-gray-600 mb-3\">{thread.description}</p>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                        <span className=\"bg-gray-100 px-2 py-1 rounded\">{thread.category}</span>\n                        <span>by {thread.author_username}</span>\n                        <span>Created: {formatDate(thread.created_at)}</span>\n                        {thread.comment_count > 0 && (\n                          <span>{thread.comment_count} comments</span>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Discussions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC;IACzCwB,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAG3B,OAAO,CAAC,CAAC;EAElD,MAAM4B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExEhC,SAAS,CAAC,MAAM;IACdiC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAGN,OAAO,kBAAkB,CAAC;MAC9DrB,UAAU,CAAC0B,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZzB,QAAQ,CAAC,6BAA6B,CAAC;MACvC0B,OAAO,CAAC3B,KAAK,CAAC,6BAA6B,EAAE0B,GAAG,CAAC;IACnD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,aAAa,GAAG,MAAOC,QAAQ,IAAK;IACxC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAGN,OAAO,oBAAoBW,QAAQ,EAAE,CAAC;MAC1ExB,WAAW,CAACkB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACrB,QAAQ,IAAI,EAAE,CAAC;IAChD,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,0BAA0B,EAAE0B,GAAG,CAAC;IAChD;EACF,CAAC;EAED,MAAMI,iBAAiB,GAAIC,MAAM,IAAK;IACpC5B,iBAAiB,CAAC4B,MAAM,CAAC;IACzBH,aAAa,CAACG,MAAM,CAACC,EAAE,CAAC;EAC1B,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAClB,eAAe,EAAE;MACpBhB,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEA,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMhC,KAAK,CAAC6C,IAAI,CAC/B,GAAGlB,OAAO,kBAAkB,EAC5BR,SAAS,EACT;QACE2B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIO,QAAQ,CAACE,IAAI,CAACa,OAAO,EAAE;QACzB3B,YAAY,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC,CAAC;QAC1DL,oBAAoB,CAAC,KAAK,CAAC;QAC3Ba,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZzB,QAAQ,CAAC,6BAA6B,CAAC;MACvC0B,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAE0B,GAAG,CAAC;IAClD;EACF,CAAC;EAED,MAAMa,gBAAgB,GAAG,MAAOL,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAClB,eAAe,EAAE;MACpBhB,QAAQ,CAAC,yBAAyB,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMhC,KAAK,CAAC6C,IAAI,CAC/B,GAAGlB,OAAO,eAAe,EACzB;QACEsB,aAAa,EAAEtC,cAAc,CAAC8B,EAAE;QAChCS,OAAO,EAAEnC;MACX,CAAC,EACD;QACE+B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIO,QAAQ,CAACE,IAAI,CAACa,OAAO,EAAE;QACzB/B,aAAa,CAAC,EAAE,CAAC;QACjBqB,aAAa,CAAC1B,cAAc,CAAC8B,EAAE,CAAC,CAAC,CAAC;MACpC;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZzB,QAAQ,CAAC,uBAAuB,CAAC;MACjC0B,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,EAAE0B,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMgB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACpE,CAAC;EAED,IAAIjD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKuD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvExD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAKuD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F5D,OAAA;UAAGuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrD,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKuD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvExD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAKuD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnD5D,OAAA;UAAGuD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEjD;QAAK;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxC5D,OAAA;UACE6D,OAAO,EAAEhC,YAAa;UACtB0B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAInD,cAAc,EAAE;IAClB,oBACET,OAAA;MAAKuD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCxD,OAAA;QAAKuD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DxD,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YACE6D,OAAO,EAAEA,CAAA,KAAMnD,iBAAiB,CAAC,IAAI,CAAE;YACvC6C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA;YAAIuD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAE/C,cAAc,CAACU;UAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjF5D,OAAA;YAAGuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAE/C,cAAc,CAACW;UAAW;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE5D,OAAA;YAAKuD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChExD,OAAA;cAAMuD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAE/C,cAAc,CAACY;YAAQ;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF5D,OAAA;cAAAwD,QAAA,GAAM,KAAG,EAAC/C,cAAc,CAACqD,eAAe;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD5D,OAAA;cAAAwD,QAAA,GAAM,WAAS,EAACP,UAAU,CAACxC,cAAc,CAACsD,UAAU,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAKuD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDxD,OAAA;YAAKuD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CxD,OAAA;cAAIuD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,KAAK;YAAAC,QAAA,EACjB7C,QAAQ,CAACqD,MAAM,KAAK,CAAC,gBACpBhE,OAAA;cAAGuD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAE3F5D,OAAA;cAAKuD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB7C,QAAQ,CAACsD,GAAG,CAAEC,OAAO,iBACpBlE,OAAA;gBAAsBuD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC/DxD,OAAA;kBAAKuD,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDxD,OAAA;oBAAMuD,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAEU,OAAO,CAACJ;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5E5D,OAAA;oBAAMuD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEP,UAAU,CAACiB,OAAO,CAACH,UAAU;kBAAC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACN5D,OAAA;kBAAGuD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEU,OAAO,CAAClB;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrEM,OAAO,CAACC,OAAO,GAAG,CAAC,iBAClBnE,OAAA;kBAAKuD,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,eACvC,EAACU,OAAO,CAACC,OAAO,EAAC,UACtB;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA,GAVOM,OAAO,CAAC3B,EAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLpC,eAAe,gBACdxB,OAAA;UAAKuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxD,OAAA;YAAIuD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3E5D,OAAA;YAAMoE,QAAQ,EAAEtB,gBAAiB;YAAAU,QAAA,gBAC/BxD,OAAA;cACEqE,KAAK,EAAExD,UAAW;cAClByD,QAAQ,EAAG7B,CAAC,IAAK3B,aAAa,CAAC2B,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAC,wBAAwB;cACpCjB,SAAS,EAAC,wFAAwF;cAClGkB,IAAI,EAAC,GAAG;cACRC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF5D,OAAA;cAAKuD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCxD,OAAA;gBACE2E,IAAI,EAAC,QAAQ;gBACbpB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAEN5D,OAAA;UAAKuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxD,OAAA;YAAGuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnF5D,OAAA;YAAG4E,IAAI,EAAC,QAAQ;YAACrB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE3F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI7C,iBAAiB,EAAE;IACrB,oBACEf,OAAA;MAAKuD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCxD,OAAA;QAAKuD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DxD,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YACE6D,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,KAAK,CAAE;YAC3CuC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA;YAAIuD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDxD,OAAA;YAAMoE,QAAQ,EAAE5B,kBAAmB;YAAAgB,QAAA,gBACjCxD,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAO6E,OAAO,EAAC,OAAO;gBAACtB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5D,OAAA;gBACE2E,IAAI,EAAC,MAAM;gBACXpC,EAAE,EAAC,OAAO;gBACV8B,KAAK,EAAEpD,SAAS,CAACE,KAAM;gBACvBmD,QAAQ,EAAG7B,CAAC,IAAKvB,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,KAAK,EAAEsB,CAAC,CAAC8B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEd,SAAS,EAAC,wFAAwF;gBAClGiB,WAAW,EAAC,kCAAkC;gBAC9CE,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAO6E,OAAO,EAAC,UAAU;gBAACtB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5D,OAAA;gBACEuC,EAAE,EAAC,UAAU;gBACb8B,KAAK,EAAEpD,SAAS,CAACI,QAAS;gBAC1BiD,QAAQ,EAAG7B,CAAC,IAAKvB,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEI,QAAQ,EAAEoB,CAAC,CAAC8B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC1Ed,SAAS,EAAC,wFAAwF;gBAClGmB,QAAQ;gBAAAlB,QAAA,gBAERxD,OAAA;kBAAQqE,KAAK,EAAC,EAAE;kBAAAb,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C5D,OAAA;kBAAQqE,KAAK,EAAC,WAAW;kBAAAb,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C5D,OAAA;kBAAQqE,KAAK,EAAC,QAAQ;kBAAAb,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC5D,OAAA;kBAAQqE,KAAK,EAAC,kBAAkB;kBAAAb,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1D5D,OAAA;kBAAQqE,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9D5D,OAAA;kBAAQqE,KAAK,EAAC,yBAAyB;kBAAAb,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxE5D,OAAA;kBAAQqE,KAAK,EAAC,kBAAkB;kBAAAb,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1D5D,OAAA;kBAAQqE,KAAK,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAO6E,OAAO,EAAC,aAAa;gBAACtB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5D,OAAA;gBACEuC,EAAE,EAAC,aAAa;gBAChB8B,KAAK,EAAEpD,SAAS,CAACG,WAAY;gBAC7BkD,QAAQ,EAAG7B,CAAC,IAAKvB,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,WAAW,EAAEqB,CAAC,CAAC8B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC7Ed,SAAS,EAAC,wFAAwF;gBAClGkB,IAAI,EAAC,GAAG;gBACRD,WAAW,EAAC,uDAAuD;gBACnEE,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5D,OAAA;cAAKuD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxD,OAAA;gBACE2E,IAAI,EAAC,QAAQ;gBACbd,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,KAAK,CAAE;gBAC3CuC,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5D,OAAA;gBACE2E,IAAI,EAAC,QAAQ;gBACbpB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCxD,OAAA;MAAKuD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DxD,OAAA;QAAKuD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E5D,OAAA;UAAGuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CxD,OAAA;UAAKuD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CxD,OAAA;YAAKuD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDxD,OAAA;cAAIuD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC1EpC,eAAe,gBACdxB,OAAA;cACE6D,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,IAAI,CAAE;cAC1CuC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET5D,OAAA;cACE4E,IAAI,EAAC,QAAQ;cACbrB,SAAS,EAAC,gEAAgE;cAC1EpC,KAAK,EAAC,gBAAgB;cAAAqC,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCrD,OAAO,CAAC6D,MAAM,KAAK,CAAC,gBACnBhE,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BxD,OAAA;cAAKuD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrD5D,OAAA;cAAIuD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF5D,OAAA;cAAGuD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAClFpC,eAAe,iBACdxB,OAAA;cACE6D,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,IAAI,CAAE;cAC1CuC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,GAENzD,OAAO,CAAC8D,GAAG,CAAE3B,MAAM,iBACjBtC,OAAA;YAEEuD,SAAS,EAAC,qCAAqC;YAC/CM,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACC,MAAM,CAAE;YAAAkB,QAAA,eAEzCxD,OAAA;cAAKuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CxD,OAAA;gBAAKuD,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxD,OAAA;kBAAIuD,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EACnDlB,MAAM,CAACnB;gBAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACL5D,OAAA;kBAAGuD,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAElB,MAAM,CAAClB;gBAAW;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1D5D,OAAA;kBAAKuD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAChExD,OAAA;oBAAMuD,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAElB,MAAM,CAACjB;kBAAQ;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxE5D,OAAA;oBAAAwD,QAAA,GAAM,KAAG,EAAClB,MAAM,CAACwB,eAAe;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxC5D,OAAA;oBAAAwD,QAAA,GAAM,WAAS,EAACP,UAAU,CAACX,MAAM,CAACyB,UAAU,CAAC;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACpDtB,MAAM,CAACwC,aAAa,GAAG,CAAC,iBACvB9E,OAAA;oBAAAwD,QAAA,GAAOlB,MAAM,CAACwC,aAAa,EAAC,WAAS;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC5C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5D,OAAA;gBAAKuD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBxD,OAAA;kBAAKuD,SAAS,EAAC,uBAAuB;kBAACwB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAzB,QAAA,eAC1FxD,OAAA;oBAAMkF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAxBDtB,MAAM,CAACC,EAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBX,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CArZID,WAAW;EAAA,QAa0BJ,OAAO;AAAA;AAAAyF,EAAA,GAb5CrF,WAAW;AAuZjB,eAAeA,WAAW;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}