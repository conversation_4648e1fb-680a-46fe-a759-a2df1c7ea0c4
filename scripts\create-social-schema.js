const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'POLITICA_AUG',
  password: process.env.DB_PASSWORD || 'Rayvical',
  port: process.env.DB_PORT || 5432,
});

async function createSocialSchema() {
  try {
    console.log('📊 Creating social feed schema...');
    
    const schemaPath = path.join(__dirname, '..', 'database', 'social_feed_schema.sql');
    const sql = fs.readFileSync(schemaPath, 'utf8');
    
    await pool.query(sql);
    
    console.log('✅ Social feed schema created successfully');
    console.log('📱 Tables created:');
    console.log('   - posts');
    console.log('   - post_reactions');
    console.log('   - post_votes');
    console.log('   - comments');
    console.log('   - comment_reactions');
    console.log('   - comment_votes');
    console.log('   - user_follows');
    console.log('   - hashtags');
    console.log('   - political_topics');
    console.log('   - post_shares');
    console.log('   - post_mentions');
    console.log('   - polls');
    console.log('   - poll_votes');
    console.log('   - notifications');
    
  } catch (error) {
    console.error('❌ Error creating social feed schema:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

createSocialSchema();
