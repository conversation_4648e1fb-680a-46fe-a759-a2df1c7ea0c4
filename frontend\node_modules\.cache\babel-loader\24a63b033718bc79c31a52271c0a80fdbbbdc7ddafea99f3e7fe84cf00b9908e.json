{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\components\\\\ProfessionalHeader.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfessionalHeader = ({\n  viewMode,\n  setViewMode,\n  isConnected,\n  user\n}) => {\n  _s();\n  var _user$username, _user$username2;\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const {\n    logout\n  } = useAuth();\n  const viewModes = [{\n    key: 'feed',\n    label: '📱 Feed',\n    icon: '🏠'\n  }, {\n    key: 'analytics',\n    label: '📊 Analytics',\n    icon: '📈'\n  }, {\n    key: 'scheduler',\n    label: '⏰ Scheduler',\n    icon: '📅'\n  }, {\n    key: 'events',\n    label: '🎪 Events',\n    icon: '🗓️'\n  }];\n  const notifications = [{\n    id: 1,\n    type: 'like',\n    message: '<PERSON> liked your post about climate policy',\n    time: '2m ago',\n    unread: true\n  }, {\n    id: 2,\n    type: 'comment',\n    message: '<PERSON> commented on your healthcare debate',\n    time: '5m ago',\n    unread: true\n  }, {\n    id: 3,\n    type: 'follow',\n    message: 'PolicyExpert started following you',\n    time: '1h ago',\n    unread: false\n  }, {\n    id: 4,\n    type: 'mention',\n    message: 'You were mentioned in a discussion',\n    time: '2h ago',\n    unread: false\n  }];\n  const unreadCount = notifications.filter(n => n.unread).length;\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white border-b border-secondary-200 sticky top-0 z-fixed\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-lg\",\n                children: \"P\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-secondary-900\",\n                children: \"Politica\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-secondary-500\",\n                children: \"Professional Political Platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"hidden md:flex items-center gap-1 bg-secondary-100 rounded-xl p-1\",\n            children: viewModes.map(mode => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode(mode.key),\n              className: `flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all ${viewMode === mode.key ? 'bg-white text-primary-600 shadow-sm' : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: mode.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden lg:inline\",\n                children: mode.label.split(' ')[1]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this)]\n            }, mode.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex items-center gap-2 px-3 py-1 rounded-full bg-secondary-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-2 h-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-secondary-600\",\n              children: isConnected ? 'Live' : 'Offline'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search politics...\",\n              className: \"form-input w-64 pl-10 pr-4 py-2 text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-ghost btn-sm\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-ghost btn-sm\",\n              children: \"\\uD83D\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowNotifications(!showNotifications),\n              className: \"btn btn-ghost btn-sm relative\",\n              children: [\"\\uD83D\\uDD14\", unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"absolute -top-1 -right-1 w-5 h-5 bg-error-500 text-white text-xs rounded-full flex items-center justify-center\",\n                children: unreadCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), showNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-secondary-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-secondary-900\",\n                    children: \"Notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-xs text-primary-600 hover:text-primary-700\",\n                    children: \"Mark all read\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-h-96 overflow-y-auto\",\n                children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 border-b border-secondary-50 hover:bg-secondary-25 cursor-pointer ${notification.unread ? 'bg-primary-25' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm\",\n                      children: [notification.type === 'like' && '👍', notification.type === 'comment' && '💬', notification.type === 'follow' && '👥', notification.type === 'mention' && '📢']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-secondary-900\",\n                        children: notification.message\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 134,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-secondary-500 mt-1\",\n                        children: notification.time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 135,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 27\n                    }, this), notification.unread && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 25\n                  }, this)\n                }, notification.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-t border-secondary-100\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full text-center text-sm text-primary-600 hover:text-primary-700\",\n                  children: \"View all notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUserMenu(!showUserMenu),\n              className: \"flex items-center gap-3 p-2 rounded-xl hover:bg-secondary-100 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm\",\n                children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-secondary-900\",\n                  children: (user === null || user === void 0 ? void 0 : user.username) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-secondary-500\",\n                  children: [(user === null || user === void 0 ? void 0 : user.role) === 'admin' && '👑 Admin', (user === null || user === void 0 ? void 0 : user.role) === 'moderator' && '🛡️ Moderator', (user === null || user === void 0 ? void 0 : user.verified_status) === 'verified' && '✅ Verified', !(user !== null && user !== void 0 && user.role) && !(user !== null && user !== void 0 && user.verified_status) && '👤 Member']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-secondary-400\",\n                children: \"\\u25BC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-secondary-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$username2 = user.username) === null || _user$username2 === void 0 ? void 0 : _user$username2.charAt(0).toUpperCase()) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-semibold text-secondary-900\",\n                      children: (user === null || user === void 0 ? void 0 : user.username) || 'User'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-secondary-500\",\n                      children: user === null || user === void 0 ? void 0 : user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDC64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2699\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCCA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83C\\uDFA8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Appearance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                  className: \"my-2 border-secondary-100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2753\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Help & Support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: logout,\n                  className: \"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-error-50 hover:text-error-600 text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDEAA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden border-t border-secondary-200 bg-secondary-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-around py-2\",\n        children: viewModes.map(mode => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode(mode.key),\n          className: `flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all ${viewMode === mode.key ? 'text-primary-600 bg-primary-50' : 'text-secondary-600'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: mode.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: mode.label.split(' ')[1]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)]\n        }, mode.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalHeader, \"FhXzlsxslCXkb1t2u99JFdyjd20=\", false, function () {\n  return [useAuth];\n});\n_c = ProfessionalHeader;\nexport default ProfessionalHeader;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalHeader\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "viewMode", "setViewMode", "isConnected", "user", "_s", "_user$username", "_user$username2", "showNotifications", "setShowNotifications", "showUserMenu", "setShowUserMenu", "logout", "viewModes", "key", "label", "icon", "notifications", "id", "type", "message", "time", "unread", "unreadCount", "filter", "n", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "mode", "onClick", "split", "placeholder", "notification", "username", "char<PERSON>t", "toUpperCase", "role", "verified_status", "email", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/components/ProfessionalHeader.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProfessionalHeader = ({ viewMode, setViewMode, isConnected, user }) => {\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const { logout } = useAuth();\n\n  const viewModes = [\n    { key: 'feed', label: '📱 Feed', icon: '🏠' },\n    { key: 'analytics', label: '📊 Analytics', icon: '📈' },\n    { key: 'scheduler', label: '⏰ Scheduler', icon: '📅' },\n    { key: 'events', label: '🎪 Events', icon: '🗓️' }\n  ];\n\n  const notifications = [\n    { id: 1, type: 'like', message: '<PERSON> liked your post about climate policy', time: '2m ago', unread: true },\n    { id: 2, type: 'comment', message: '<PERSON> commented on your healthcare debate', time: '5m ago', unread: true },\n    { id: 3, type: 'follow', message: '<PERSON><PERSON><PERSON><PERSON> started following you', time: '1h ago', unread: false },\n    { id: 4, type: 'mention', message: 'You were mentioned in a discussion', time: '2h ago', unread: false }\n  ];\n\n  const unreadCount = notifications.filter(n => n.unread).length;\n\n  return (\n    <header className=\"bg-white border-b border-secondary-200 sticky top-0 z-fixed\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          \n          {/* Logo and Brand */}\n          <div className=\"flex items-center gap-8\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">P</span>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-secondary-900\">Politica</h1>\n                <p className=\"text-xs text-secondary-500\">Professional Political Platform</p>\n              </div>\n            </div>\n\n            {/* View Mode Tabs */}\n            <nav className=\"hidden md:flex items-center gap-1 bg-secondary-100 rounded-xl p-1\">\n              {viewModes.map(mode => (\n                <button\n                  key={mode.key}\n                  onClick={() => setViewMode(mode.key)}\n                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all ${\n                    viewMode === mode.key\n                      ? 'bg-white text-primary-600 shadow-sm'\n                      : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'\n                  }`}\n                >\n                  <span>{mode.icon}</span>\n                  <span className=\"hidden lg:inline\">{mode.label.split(' ')[1]}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center gap-4\">\n            \n            {/* Connection Status */}\n            <div className=\"hidden sm:flex items-center gap-2 px-3 py-1 rounded-full bg-secondary-100\">\n              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`}></div>\n              <span className=\"text-xs font-medium text-secondary-600\">\n                {isConnected ? 'Live' : 'Offline'}\n              </span>\n            </div>\n\n            {/* Search */}\n            <div className=\"hidden md:block relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search politics...\"\n                className=\"form-input w-64 pl-10 pr-4 py-2 text-sm\"\n              />\n              <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400\">\n                🔍\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"flex items-center gap-2\">\n              <button className=\"btn btn-ghost btn-sm\">\n                💬\n              </button>\n              <button className=\"btn btn-ghost btn-sm\">\n                🔖\n              </button>\n            </div>\n\n            {/* Notifications */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowNotifications(!showNotifications)}\n                className=\"btn btn-ghost btn-sm relative\"\n              >\n                🔔\n                {unreadCount > 0 && (\n                  <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-error-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {unreadCount}\n                  </span>\n                )}\n              </button>\n\n              {showNotifications && (\n                <div className=\"absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up\">\n                  <div className=\"p-4 border-b border-secondary-100\">\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"font-semibold text-secondary-900\">Notifications</h3>\n                      <button className=\"text-xs text-primary-600 hover:text-primary-700\">\n                        Mark all read\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"max-h-96 overflow-y-auto\">\n                    {notifications.map(notification => (\n                      <div\n                        key={notification.id}\n                        className={`p-4 border-b border-secondary-50 hover:bg-secondary-25 cursor-pointer ${\n                          notification.unread ? 'bg-primary-25' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-start gap-3\">\n                          <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm\">\n                            {notification.type === 'like' && '👍'}\n                            {notification.type === 'comment' && '💬'}\n                            {notification.type === 'follow' && '👥'}\n                            {notification.type === 'mention' && '📢'}\n                          </div>\n                          <div className=\"flex-1\">\n                            <p className=\"text-sm text-secondary-900\">{notification.message}</p>\n                            <p className=\"text-xs text-secondary-500 mt-1\">{notification.time}</p>\n                          </div>\n                          {notification.unread && (\n                            <div className=\"w-2 h-2 bg-primary-500 rounded-full\"></div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"p-4 border-t border-secondary-100\">\n                    <button className=\"w-full text-center text-sm text-primary-600 hover:text-primary-700\">\n                      View all notifications\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* User Menu */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowUserMenu(!showUserMenu)}\n                className=\"flex items-center gap-3 p-2 rounded-xl hover:bg-secondary-100 transition-colors\"\n              >\n                <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm\">\n                  {user?.username?.charAt(0).toUpperCase() || 'U'}\n                </div>\n                <div className=\"hidden sm:block text-left\">\n                  <p className=\"text-sm font-medium text-secondary-900\">{user?.username || 'User'}</p>\n                  <p className=\"text-xs text-secondary-500\">\n                    {user?.role === 'admin' && '👑 Admin'}\n                    {user?.role === 'moderator' && '🛡️ Moderator'}\n                    {user?.verified_status === 'verified' && '✅ Verified'}\n                    {!user?.role && !user?.verified_status && '👤 Member'}\n                  </p>\n                </div>\n                <span className=\"text-secondary-400\">▼</span>\n              </button>\n\n              {showUserMenu && (\n                <div className=\"absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up\">\n                  <div className=\"p-4 border-b border-secondary-100\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold\">\n                        {user?.username?.charAt(0).toUpperCase() || 'U'}\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-secondary-900\">{user?.username || 'User'}</p>\n                        <p className=\"text-sm text-secondary-500\">{user?.email}</p>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"p-2\">\n                    <button className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\">\n                      <span>👤</span>\n                      <span className=\"text-sm\">Profile</span>\n                    </button>\n                    <button className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\">\n                      <span>⚙️</span>\n                      <span className=\"text-sm\">Settings</span>\n                    </button>\n                    <button className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\">\n                      <span>📊</span>\n                      <span className=\"text-sm\">Analytics</span>\n                    </button>\n                    <button className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\">\n                      <span>🎨</span>\n                      <span className=\"text-sm\">Appearance</span>\n                    </button>\n                    <hr className=\"my-2 border-secondary-100\" />\n                    <button className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left\">\n                      <span>❓</span>\n                      <span className=\"text-sm\">Help & Support</span>\n                    </button>\n                    <button \n                      onClick={logout}\n                      className=\"w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-error-50 hover:text-error-600 text-left\"\n                    >\n                      <span>🚪</span>\n                      <span className=\"text-sm\">Sign Out</span>\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile View Mode Selector */}\n      <div className=\"md:hidden border-t border-secondary-200 bg-secondary-50\">\n        <div className=\"flex items-center justify-around py-2\">\n          {viewModes.map(mode => (\n            <button\n              key={mode.key}\n              onClick={() => setViewMode(mode.key)}\n              className={`flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all ${\n                viewMode === mode.key\n                  ? 'text-primary-600 bg-primary-50'\n                  : 'text-secondary-600'\n              }`}\n            >\n              <span className=\"text-lg\">{mode.icon}</span>\n              <span>{mode.label.split(' ')[1]}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default ProfessionalHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,WAAW;EAAEC,WAAW;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,eAAA;EAC3E,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEgB;EAAO,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE5B,MAAMgB,SAAS,GAAG,CAChB;IAAEC,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC7C;IAAEF,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,EACvD;IAAEF,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACtD;IAAEF,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAM,CAAC,CACnD;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE,4CAA4C;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAK,CAAC,EAC5G;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,0CAA0C;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAK,CAAC,EAC7G;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE,oCAAoC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAM,CAAC,EACvG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,oCAAoC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAM,CAAC,CACzG;EAED,MAAMC,WAAW,GAAGN,aAAa,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,MAAM,CAAC,CAACI,MAAM;EAE9D,oBACE3B,OAAA;IAAQ4B,SAAS,EAAC,6DAA6D;IAAAC,QAAA,gBAC7E7B,OAAA;MAAK4B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC7B,OAAA;QAAK4B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAGrD7B,OAAA;UAAK4B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7B,OAAA;YAAK4B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC7B,OAAA;cAAK4B,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF7B,OAAA;gBAAM4B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNjC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAI4B,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEjC,OAAA;gBAAG4B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC/Ef,SAAS,CAACoB,GAAG,CAACC,IAAI,iBACjBnC,OAAA;cAEEoC,OAAO,EAAEA,CAAA,KAAMjC,WAAW,CAACgC,IAAI,CAACpB,GAAG,CAAE;cACrCa,SAAS,EAAE,mFACT1B,QAAQ,KAAKiC,IAAI,CAACpB,GAAG,GACjB,qCAAqC,GACrC,mEAAmE,EACtE;cAAAc,QAAA,gBAEH7B,OAAA;gBAAA6B,QAAA,EAAOM,IAAI,CAAClB;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBjC,OAAA;gBAAM4B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEM,IAAI,CAACnB,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAT/DE,IAAI,CAACpB,GAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAGtC7B,OAAA;YAAK4B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBACxF7B,OAAA;cAAK4B,SAAS,EAAE,wBAAwBxB,WAAW,GAAG,gBAAgB,GAAG,cAAc;YAAiB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/GjC,OAAA;cAAM4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACrDzB,WAAW,GAAG,MAAM,GAAG;YAAS;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC7B,OAAA;cACEoB,IAAI,EAAC,MAAM;cACXkB,WAAW,EAAC,oBAAoB;cAChCV,SAAS,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACFjC,OAAA;cAAK4B,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EAAC;YAEvF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC7B,OAAA;cAAQ4B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjC,OAAA;cAAQ4B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7B,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;cACxDmB,SAAS,EAAC,+BAA+B;cAAAC,QAAA,GAC1C,cAEC,EAACL,WAAW,GAAG,CAAC,iBACdxB,OAAA;gBAAM4B,SAAS,EAAC,gHAAgH;gBAAAC,QAAA,EAC7HL;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EAERxB,iBAAiB,iBAChBT,OAAA;cAAK4B,SAAS,EAAC,2HAA2H;cAAAC,QAAA,gBACxI7B,OAAA;gBAAK4B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD7B,OAAA;kBAAK4B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD7B,OAAA;oBAAI4B,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnEjC,OAAA;oBAAQ4B,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAAC;kBAEpE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjC,OAAA;gBAAK4B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACtCX,aAAa,CAACgB,GAAG,CAACK,YAAY,iBAC7BvC,OAAA;kBAEE4B,SAAS,EAAE,yEACTW,YAAY,CAAChB,MAAM,GAAG,eAAe,GAAG,EAAE,EACzC;kBAAAM,QAAA,eAEH7B,OAAA;oBAAK4B,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC7B,OAAA;sBAAK4B,SAAS,EAAC,8EAA8E;sBAAAC,QAAA,GAC1FU,YAAY,CAACnB,IAAI,KAAK,MAAM,IAAI,IAAI,EACpCmB,YAAY,CAACnB,IAAI,KAAK,SAAS,IAAI,IAAI,EACvCmB,YAAY,CAACnB,IAAI,KAAK,QAAQ,IAAI,IAAI,EACtCmB,YAAY,CAACnB,IAAI,KAAK,SAAS,IAAI,IAAI;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACNjC,OAAA;sBAAK4B,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrB7B,OAAA;wBAAG4B,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEU,YAAY,CAAClB;sBAAO;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpEjC,OAAA;wBAAG4B,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAEU,YAAY,CAACjB;sBAAI;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC,EACLM,YAAY,CAAChB,MAAM,iBAClBvB,OAAA;sBAAK4B,SAAS,EAAC;oBAAqC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC3D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC,GAnBDM,YAAY,CAACpB,EAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBjB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjC,OAAA;gBAAK4B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD7B,OAAA;kBAAQ4B,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,EAAC;gBAEvF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7B,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CiB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAE3F7B,OAAA;gBAAK4B,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,EACnH,CAAAxB,IAAI,aAAJA,IAAI,wBAAAE,cAAA,GAAJF,IAAI,CAAEmC,QAAQ,cAAAjC,cAAA,uBAAdA,cAAA,CAAgBkC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNjC,OAAA;gBAAK4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC7B,OAAA;kBAAG4B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAE,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,QAAQ,KAAI;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFjC,OAAA;kBAAG4B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACtC,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,IAAI,MAAK,OAAO,IAAI,UAAU,EACpC,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,IAAI,MAAK,WAAW,IAAI,eAAe,EAC7C,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,eAAe,MAAK,UAAU,IAAI,YAAY,EACpD,EAACvC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsC,IAAI,KAAI,EAACtC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,eAAe,KAAI,WAAW;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNjC,OAAA;gBAAM4B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,EAERtB,YAAY,iBACXX,OAAA;cAAK4B,SAAS,EAAC,2HAA2H;cAAAC,QAAA,gBACxI7B,OAAA;gBAAK4B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD7B,OAAA;kBAAK4B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC7B,OAAA;oBAAK4B,SAAS,EAAC,6FAA6F;oBAAAC,QAAA,EACzG,CAAAxB,IAAI,aAAJA,IAAI,wBAAAG,eAAA,GAAJH,IAAI,CAAEmC,QAAQ,cAAAhC,eAAA,uBAAdA,eAAA,CAAgBiC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACNjC,OAAA;oBAAA6B,QAAA,gBACE7B,OAAA;sBAAG4B,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,QAAQ,KAAI;oBAAM;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9EjC,OAAA;sBAAG4B,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAExB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC;oBAAK;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjC,OAAA;gBAAK4B,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB7B,OAAA;kBAAQ4B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBACtG7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACTjC,OAAA;kBAAQ4B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBACtG7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACTjC,OAAA;kBAAQ4B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBACtG7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACTjC,OAAA;kBAAQ4B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBACtG7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACTjC,OAAA;kBAAI4B,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CjC,OAAA;kBAAQ4B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBACtG7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACTjC,OAAA;kBACEoC,OAAO,EAAEvB,MAAO;kBAChBe,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfjC,OAAA;oBAAM4B,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE7B,OAAA;QAAK4B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDf,SAAS,CAACoB,GAAG,CAACC,IAAI,iBACjBnC,OAAA;UAEEoC,OAAO,EAAEA,CAAA,KAAMjC,WAAW,CAACgC,IAAI,CAACpB,GAAG,CAAE;UACrCa,SAAS,EAAE,4FACT1B,QAAQ,KAAKiC,IAAI,CAACpB,GAAG,GACjB,gCAAgC,GAChC,oBAAoB,EACvB;UAAAc,QAAA,gBAEH7B,OAAA;YAAM4B,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAEM,IAAI,CAAClB;UAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CjC,OAAA;YAAA6B,QAAA,EAAOM,IAAI,CAACnB,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;UAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATlCE,IAAI,CAACpB,GAAG;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUP,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC3B,EAAA,CAlPIL,kBAAkB;EAAA,QAGHH,OAAO;AAAA;AAAAgD,EAAA,GAHtB7C,kBAAkB;AAoPxB,eAAeA,kBAAkB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}