{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\components\\\\CommentsSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CommentsSection = ({\n  postId,\n  onClose\n}) => {\n  _s();\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [newComment, setNewComment] = useState('');\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [sortBy, setSortBy] = useState('best');\n  const [submitting, setSubmitting] = useState(false);\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    fetchComments();\n  }, [postId, sortBy]);\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/feed-comments/post/${postId}`, {\n        params: {\n          sort: sortBy,\n          limit: 100\n        }\n      });\n      setComments(response.data.comments);\n    } catch (error) {\n      console.error('Error fetching comments:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmitComment = async e => {\n    e.preventDefault();\n    if (!newComment.trim()) return;\n    setSubmitting(true);\n    try {\n      const response = await axios.post('/api/feed-comments', {\n        post_id: postId,\n        parent_comment_id: (replyingTo === null || replyingTo === void 0 ? void 0 : replyingTo.id) || null,\n        content: newComment.trim()\n      });\n\n      // Add new comment to the list\n      setComments(prev => [response.data, ...prev]);\n      setNewComment('');\n      setReplyingTo(null);\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleVoteComment = async (commentId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed-comments/${commentId}/vote`, {\n        vote_type: voteType\n      });\n      setComments(prev => prev.map(comment => comment.id === commentId ? {\n        ...comment,\n        upvotes: response.data.upvotes,\n        downvotes: response.data.downvotes,\n        user_vote: response.data.user_vote\n      } : comment));\n    } catch (error) {\n      console.error('Error voting on comment:', error);\n    }\n  };\n  const handleReactComment = async (commentId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed-comments/${commentId}/react`, {\n        reaction_type: reactionType\n      });\n      setComments(prev => prev.map(comment => comment.id === commentId ? {\n        ...comment,\n        reactions: response.data.reactions\n      } : comment));\n    } catch (error) {\n      console.error('Error reacting to comment:', error);\n    }\n  };\n  const formatTimeAgo = dateString => {\n    const now = new Date();\n    const commentDate = new Date(dateString);\n    const diffInSeconds = Math.floor((now - commentDate) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n  };\n  const renderComment = (comment, depth = 0) => {\n    var _comment$username;\n    const netVotes = (comment.upvotes || 0) - (comment.downvotes || 0);\n    const isReply = depth > 0;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''} ${depth > 5 ? 'ml-4' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold\",\n              children: ((_comment$username = comment.username) === null || _comment$username === void 0 ? void 0 : _comment$username.charAt(0).toUpperCase()) || 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900 text-sm\",\n                  children: comment.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), comment.verified_status === 'verified' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-500 text-xs\",\n                  title: \"Verified\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this), comment.role === 'admin' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-red-100 text-red-800 px-1 py-0.5 rounded text-xs\",\n                  children: \"Admin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: formatTimeAgo(comment.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 text-xs text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [netVotes, \" points\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-900 text-sm mb-3 whitespace-pre-wrap\",\n          children: comment.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleVoteComment(comment.id, comment.user_vote === 1 ? 0 : 1),\n              className: `p-1 rounded text-xs ${comment.user_vote === 1 ? 'text-green-600 bg-green-100' : 'text-gray-500 hover:text-green-600 hover:bg-green-50'}`,\n              children: [\"\\u2B06\\uFE0F \", comment.upvotes || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleVoteComment(comment.id, comment.user_vote === -1 ? 0 : -1),\n              className: `p-1 rounded text-xs ${comment.user_vote === -1 ? 'text-red-600 bg-red-100' : 'text-gray-500 hover:text-red-600 hover:bg-red-50'}`,\n              children: [\"\\u2B07\\uFE0F \", comment.downvotes || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setReplyingTo(comment),\n            className: \"text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50\",\n            children: \"\\uD83D\\uDCAC Reply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleReactComment(comment.id, 'like'),\n            className: \"text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50\",\n            children: \"\\uD83D\\uDC4D Like\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), (replyingTo === null || replyingTo === void 0 ? void 0 : replyingTo.id) === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 pt-3 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmitComment,\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newComment,\n              onChange: e => setNewComment(e.target.value),\n              placeholder: `Reply to ${comment.username}...`,\n              className: \"w-full p-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              rows: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: submitting || !newComment.trim(),\n                className: \"bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 disabled:opacity-50\",\n                children: submitting ? 'Posting...' : 'Reply'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setReplyingTo(null);\n                  setNewComment('');\n                },\n                className: \"text-gray-500 hover:text-gray-700 px-3 py-1 rounded text-xs\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), comments.filter(c => c.parent_comment_id === comment.id).map(childComment => renderComment(childComment, depth + 1))]\n    }, comment.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  };\n  const topLevelComments = comments.filter(comment => !comment.parent_comment_id);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border-t border-gray-100 bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-900\",\n            children: [\"Comments (\", comments.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"best\",\n              children: \"Best\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"newest\",\n              children: \"Newest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"oldest\",\n              children: \"Oldest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-500 hover:text-gray-700 p-1\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), !replyingTo && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmitComment,\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newComment,\n          onChange: e => setNewComment(e.target.value),\n          placeholder: \"Share your thoughts on this post...\",\n          className: \"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          rows: 3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: [newComment.length, \"/1000 characters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: submitting || !newComment.trim() || newComment.length > 1000,\n            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm\",\n            children: submitting ? 'Posting...' : 'Comment'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 max-h-96 overflow-y-auto\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gray-300 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-gray-300 rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this) : topLevelComments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-4xl mb-2\",\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No comments yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"Be the first to share your thoughts!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: topLevelComments.map(comment => renderComment(comment))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n};\n_s(CommentsSection, \"OKV708V4ytBMwzLKF6n4fRKHLRM=\", false, function () {\n  return [useAuth];\n});\n_c = CommentsSection;\nexport default CommentsSection;\nvar _c;\n$RefreshReg$(_c, \"CommentsSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "axios", "jsxDEV", "_jsxDEV", "CommentsSection", "postId", "onClose", "_s", "comments", "setComments", "loading", "setLoading", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "sortBy", "setSortBy", "submitting", "setSubmitting", "user", "fetchComments", "response", "get", "params", "sort", "limit", "data", "error", "console", "handleSubmitComment", "e", "preventDefault", "trim", "post", "post_id", "parent_comment_id", "id", "content", "prev", "handleVoteComment", "commentId", "voteType", "vote_type", "map", "comment", "upvotes", "downvotes", "user_vote", "handleReactComment", "reactionType", "reaction_type", "reactions", "formatTimeAgo", "dateString", "now", "Date", "commentDate", "diffInSeconds", "Math", "floor", "renderComment", "depth", "_comment$username", "netVotes", "isReply", "className", "children", "username", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "verified_status", "title", "role", "created_at", "onClick", "onSubmit", "value", "onChange", "target", "placeholder", "rows", "type", "disabled", "filter", "c", "childComment", "topLevelComments", "length", "Array", "_", "i", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/components/CommentsSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst CommentsSection = ({ postId, onClose }) => {\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [newComment, setNewComment] = useState('');\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [sortBy, setSortBy] = useState('best');\n  const [submitting, setSubmitting] = useState(false);\n\n  const { user } = useAuth();\n\n  useEffect(() => {\n    fetchComments();\n  }, [postId, sortBy]);\n\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/feed-comments/post/${postId}`, {\n        params: { sort: sortBy, limit: 100 }\n      });\n      setComments(response.data.comments);\n    } catch (error) {\n      console.error('Error fetching comments:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmitComment = async (e) => {\n    e.preventDefault();\n    \n    if (!newComment.trim()) return;\n\n    setSubmitting(true);\n    try {\n      const response = await axios.post('/api/feed-comments', {\n        post_id: postId,\n        parent_comment_id: replyingTo?.id || null,\n        content: newComment.trim()\n      });\n\n      // Add new comment to the list\n      setComments(prev => [response.data, ...prev]);\n      setNewComment('');\n      setReplyingTo(null);\n\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleVoteComment = async (commentId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed-comments/${commentId}/vote`, {\n        vote_type: voteType\n      });\n\n      setComments(prev => prev.map(comment => \n        comment.id === commentId \n          ? { \n              ...comment, \n              upvotes: response.data.upvotes,\n              downvotes: response.data.downvotes,\n              user_vote: response.data.user_vote\n            }\n          : comment\n      ));\n\n    } catch (error) {\n      console.error('Error voting on comment:', error);\n    }\n  };\n\n  const handleReactComment = async (commentId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed-comments/${commentId}/react`, {\n        reaction_type: reactionType\n      });\n\n      setComments(prev => prev.map(comment => \n        comment.id === commentId \n          ? { \n              ...comment, \n              reactions: response.data.reactions\n            }\n          : comment\n      ));\n\n    } catch (error) {\n      console.error('Error reacting to comment:', error);\n    }\n  };\n\n  const formatTimeAgo = (dateString) => {\n    const now = new Date();\n    const commentDate = new Date(dateString);\n    const diffInSeconds = Math.floor((now - commentDate) / 1000);\n\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n  };\n\n  const renderComment = (comment, depth = 0) => {\n    const netVotes = (comment.upvotes || 0) - (comment.downvotes || 0);\n    const isReply = depth > 0;\n\n    return (\n      <div\n        key={comment.id}\n        className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''} ${\n          depth > 5 ? 'ml-4' : ''\n        }`}\n      >\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-3\">\n          {/* Comment Header */}\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold\">\n                {comment.username?.charAt(0).toUpperCase() || 'U'}\n              </div>\n              <div>\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"font-medium text-gray-900 text-sm\">{comment.username}</span>\n                  {comment.verified_status === 'verified' && (\n                    <span className=\"text-blue-500 text-xs\" title=\"Verified\">✓</span>\n                  )}\n                  {comment.role === 'admin' && (\n                    <span className=\"bg-red-100 text-red-800 px-1 py-0.5 rounded text-xs\">Admin</span>\n                  )}\n                </div>\n                <span className=\"text-xs text-gray-500\">{formatTimeAgo(comment.created_at)}</span>\n              </div>\n            </div>\n            \n            {/* Comment Score */}\n            <div className=\"flex items-center space-x-1 text-xs text-gray-500\">\n              <span>{netVotes} points</span>\n            </div>\n          </div>\n\n          {/* Comment Content */}\n          <p className=\"text-gray-900 text-sm mb-3 whitespace-pre-wrap\">{comment.content}</p>\n\n          {/* Comment Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Voting */}\n            <div className=\"flex items-center space-x-1\">\n              <button\n                onClick={() => handleVoteComment(comment.id, comment.user_vote === 1 ? 0 : 1)}\n                className={`p-1 rounded text-xs ${\n                  comment.user_vote === 1\n                    ? 'text-green-600 bg-green-100'\n                    : 'text-gray-500 hover:text-green-600 hover:bg-green-50'\n                }`}\n              >\n                ⬆️ {comment.upvotes || 0}\n              </button>\n              <button\n                onClick={() => handleVoteComment(comment.id, comment.user_vote === -1 ? 0 : -1)}\n                className={`p-1 rounded text-xs ${\n                  comment.user_vote === -1\n                    ? 'text-red-600 bg-red-100'\n                    : 'text-gray-500 hover:text-red-600 hover:bg-red-50'\n                }`}\n              >\n                ⬇️ {comment.downvotes || 0}\n              </button>\n            </div>\n\n            {/* Reply */}\n            <button\n              onClick={() => setReplyingTo(comment)}\n              className=\"text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50\"\n            >\n              💬 Reply\n            </button>\n\n            {/* React */}\n            <button\n              onClick={() => handleReactComment(comment.id, 'like')}\n              className=\"text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50\"\n            >\n              👍 Like\n            </button>\n          </div>\n\n          {/* Reply Form */}\n          {replyingTo?.id === comment.id && (\n            <div className=\"mt-3 pt-3 border-t border-gray-200\">\n              <form onSubmit={handleSubmitComment} className=\"space-y-2\">\n                <textarea\n                  value={newComment}\n                  onChange={(e) => setNewComment(e.target.value)}\n                  placeholder={`Reply to ${comment.username}...`}\n                  className=\"w-full p-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  rows={2}\n                />\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    type=\"submit\"\n                    disabled={submitting || !newComment.trim()}\n                    className=\"bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 disabled:opacity-50\"\n                  >\n                    {submitting ? 'Posting...' : 'Reply'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setReplyingTo(null);\n                      setNewComment('');\n                    }}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-1 rounded text-xs\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          )}\n        </div>\n\n        {/* Render child comments */}\n        {comments\n          .filter(c => c.parent_comment_id === comment.id)\n          .map(childComment => renderComment(childComment, depth + 1))}\n      </div>\n    );\n  };\n\n  const topLevelComments = comments.filter(comment => !comment.parent_comment_id);\n\n  return (\n    <div className=\"border-t border-gray-100 bg-white\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-100\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <h3 className=\"font-semibold text-gray-900\">\n              Comments ({comments.length})\n            </h3>\n            \n            {/* Sort Options */}\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"best\">Best</option>\n              <option value=\"newest\">Newest</option>\n              <option value=\"oldest\">Oldest</option>\n            </select>\n          </div>\n          \n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 p-1\"\n          >\n            ✕\n          </button>\n        </div>\n      </div>\n\n      {/* New Comment Form */}\n      {!replyingTo && (\n        <div className=\"p-4 border-b border-gray-100\">\n          <form onSubmit={handleSubmitComment} className=\"space-y-3\">\n            <textarea\n              value={newComment}\n              onChange={(e) => setNewComment(e.target.value)}\n              placeholder=\"Share your thoughts on this post...\"\n              className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              rows={3}\n            />\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-xs text-gray-500\">\n                {newComment.length}/1000 characters\n              </span>\n              <button\n                type=\"submit\"\n                disabled={submitting || !newComment.trim() || newComment.length > 1000}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm\"\n              >\n                {submitting ? 'Posting...' : 'Comment'}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Comments List */}\n      <div className=\"p-4 max-h-96 overflow-y-auto\">\n        {loading ? (\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"animate-pulse\">\n                <div className=\"flex space-x-3\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full\"></div>\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                    <div className=\"h-3 bg-gray-300 rounded\"></div>\n                    <div className=\"h-3 bg-gray-300 rounded w-3/4\"></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : topLevelComments.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-2\">💬</div>\n            <p className=\"text-gray-500\">No comments yet</p>\n            <p className=\"text-gray-400 text-sm\">Be the first to share your thoughts!</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {topLevelComments.map(comment => renderComment(comment))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CommentsSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAEsB;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAE1BD,SAAS,CAAC,MAAM;IACdsB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAChB,MAAM,EAAEW,MAAM,CAAC,CAAC;EAEpB,MAAMK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,2BAA2BlB,MAAM,EAAE,EAAE;QACpEmB,MAAM,EAAE;UAAEC,IAAI,EAAET,MAAM;UAAEU,KAAK,EAAE;QAAI;MACrC,CAAC,CAAC;MACFjB,WAAW,CAACa,QAAQ,CAACK,IAAI,CAACnB,QAAQ,CAAC;IACrC,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,mBAAmB,GAAG,MAAOC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACpB,UAAU,CAACqB,IAAI,CAAC,CAAC,EAAE;IAExBd,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMrB,KAAK,CAACiC,IAAI,CAAC,oBAAoB,EAAE;QACtDC,OAAO,EAAE9B,MAAM;QACf+B,iBAAiB,EAAE,CAAAtB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuB,EAAE,KAAI,IAAI;QACzCC,OAAO,EAAE1B,UAAU,CAACqB,IAAI,CAAC;MAC3B,CAAC,CAAC;;MAEF;MACAxB,WAAW,CAAC8B,IAAI,IAAI,CAACjB,QAAQ,CAACK,IAAI,EAAE,GAAGY,IAAI,CAAC,CAAC;MAC7C1B,aAAa,CAAC,EAAE,CAAC;MACjBE,aAAa,CAAC,IAAI,CAAC;IAErB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRT,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,KAAK;IACvD,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMrB,KAAK,CAACiC,IAAI,CAAC,sBAAsBO,SAAS,OAAO,EAAE;QACxEE,SAAS,EAAED;MACb,CAAC,CAAC;MAEFjC,WAAW,CAAC8B,IAAI,IAAIA,IAAI,CAACK,GAAG,CAACC,OAAO,IAClCA,OAAO,CAACR,EAAE,KAAKI,SAAS,GACpB;QACE,GAAGI,OAAO;QACVC,OAAO,EAAExB,QAAQ,CAACK,IAAI,CAACmB,OAAO;QAC9BC,SAAS,EAAEzB,QAAQ,CAACK,IAAI,CAACoB,SAAS;QAClCC,SAAS,EAAE1B,QAAQ,CAACK,IAAI,CAACqB;MAC3B,CAAC,GACDH,OACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMqB,kBAAkB,GAAG,MAAAA,CAAOR,SAAS,EAAES,YAAY,KAAK;IAC5D,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMrB,KAAK,CAACiC,IAAI,CAAC,sBAAsBO,SAAS,QAAQ,EAAE;QACzEU,aAAa,EAAED;MACjB,CAAC,CAAC;MAEFzC,WAAW,CAAC8B,IAAI,IAAIA,IAAI,CAACK,GAAG,CAACC,OAAO,IAClCA,OAAO,CAACR,EAAE,KAAKI,SAAS,GACpB;QACE,GAAGI,OAAO;QACVO,SAAS,EAAE9B,QAAQ,CAACK,IAAI,CAACyB;MAC3B,CAAC,GACDP,OACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMyB,aAAa,GAAIC,UAAU,IAAK;IACpC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACxC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,WAAW,IAAI,IAAI,CAAC;IAE5D,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;IAC5E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC,OAAO;EACpD,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAChB,OAAO,EAAEiB,KAAK,GAAG,CAAC,KAAK;IAAA,IAAAC,iBAAA;IAC5C,MAAMC,QAAQ,GAAG,CAACnB,OAAO,CAACC,OAAO,IAAI,CAAC,KAAKD,OAAO,CAACE,SAAS,IAAI,CAAC,CAAC;IAClE,MAAMkB,OAAO,GAAGH,KAAK,GAAG,CAAC;IAEzB,oBACE3D,OAAA;MAEE+D,SAAS,EAAE,GAAGD,OAAO,GAAG,sCAAsC,GAAG,EAAE,IACjEH,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EACtB;MAAAK,QAAA,gBAEHhE,OAAA;QAAK+D,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAE7ChE,OAAA;UAAK+D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhE,OAAA;YAAK+D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChE,OAAA;cAAK+D,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAChH,EAAAJ,iBAAA,GAAAlB,OAAO,CAACuB,QAAQ,cAAAL,iBAAA,uBAAhBA,iBAAA,CAAkBM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNvE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAK+D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ChE,OAAA;kBAAM+D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEtB,OAAO,CAACuB;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC5E7B,OAAO,CAAC8B,eAAe,KAAK,UAAU,iBACrCxE,OAAA;kBAAM+D,SAAS,EAAC,uBAAuB;kBAACU,KAAK,EAAC,UAAU;kBAAAT,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACjE,EACA7B,OAAO,CAACgC,IAAI,KAAK,OAAO,iBACvB1E,OAAA;kBAAM+D,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAClF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvE,OAAA;gBAAM+D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEd,aAAa,CAACR,OAAO,CAACiC,UAAU;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvE,OAAA;YAAK+D,SAAS,EAAC,mDAAmD;YAAAC,QAAA,eAChEhE,OAAA;cAAAgE,QAAA,GAAOH,QAAQ,EAAC,SAAO;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvE,OAAA;UAAG+D,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAAEtB,OAAO,CAACP;QAAO;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGnFvE,OAAA;UAAK+D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1ChE,OAAA;YAAK+D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChE,OAAA;cACE4E,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACK,OAAO,CAACR,EAAE,EAAEQ,OAAO,CAACG,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;cAC9EkB,SAAS,EAAE,uBACTrB,OAAO,CAACG,SAAS,KAAK,CAAC,GACnB,6BAA6B,GAC7B,sDAAsD,EACzD;cAAAmB,QAAA,GACJ,eACI,EAACtB,OAAO,CAACC,OAAO,IAAI,CAAC;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACTvE,OAAA;cACE4E,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACK,OAAO,CAACR,EAAE,EAAEQ,OAAO,CAACG,SAAS,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAE;cAChFkB,SAAS,EAAE,uBACTrB,OAAO,CAACG,SAAS,KAAK,CAAC,CAAC,GACpB,yBAAyB,GACzB,kDAAkD,EACrD;cAAAmB,QAAA,GACJ,eACI,EAACtB,OAAO,CAACE,SAAS,IAAI,CAAC;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvE,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAMhE,aAAa,CAAC8B,OAAO,CAAE;YACtCqB,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTvE,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAACJ,OAAO,CAACR,EAAE,EAAE,MAAM,CAAE;YACtD6B,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL,CAAA5D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuB,EAAE,MAAKQ,OAAO,CAACR,EAAE,iBAC5BlC,OAAA;UAAK+D,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDhE,OAAA;YAAM6E,QAAQ,EAAElD,mBAAoB;YAACoC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxDhE,OAAA;cACE8E,KAAK,EAAErE,UAAW;cAClBsE,QAAQ,EAAGnD,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACoD,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAE,YAAYvC,OAAO,CAACuB,QAAQ,KAAM;cAC/CF,SAAS,EAAC,yHAAyH;cACnImB,IAAI,EAAE;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFvE,OAAA;cAAK+D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChE,OAAA;gBACEmF,IAAI,EAAC,QAAQ;gBACbC,QAAQ,EAAErE,UAAU,IAAI,CAACN,UAAU,CAACqB,IAAI,CAAC,CAAE;gBAC3CiC,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EAEjGjD,UAAU,GAAG,YAAY,GAAG;cAAO;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACTvE,OAAA;gBACEmF,IAAI,EAAC,QAAQ;gBACbP,OAAO,EAAEA,CAAA,KAAM;kBACbhE,aAAa,CAAC,IAAI,CAAC;kBACnBF,aAAa,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFqD,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,EACxE;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlE,QAAQ,CACNgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrD,iBAAiB,KAAKS,OAAO,CAACR,EAAE,CAAC,CAC/CO,GAAG,CAAC8C,YAAY,IAAI7B,aAAa,CAAC6B,YAAY,EAAE5B,KAAK,GAAG,CAAC,CAAC,CAAC;IAAA,GApHzDjB,OAAO,CAACR,EAAE;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAqHZ,CAAC;EAEV,CAAC;EAED,MAAMiB,gBAAgB,GAAGnF,QAAQ,CAACgF,MAAM,CAAC3C,OAAO,IAAI,CAACA,OAAO,CAACT,iBAAiB,CAAC;EAE/E,oBACEjC,OAAA;IAAK+D,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAEhDhE,OAAA;MAAK+D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3ChE,OAAA;QAAK+D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhE,OAAA;UAAK+D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChE,OAAA;YAAI+D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAAC,YAChC,EAAC3D,QAAQ,CAACoF,MAAM,EAAC,GAC7B;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLvE,OAAA;YACE8E,KAAK,EAAEjE,MAAO;YACdkE,QAAQ,EAAGnD,CAAC,IAAKd,SAAS,CAACc,CAAC,CAACoD,MAAM,CAACF,KAAK,CAAE;YAC3Cf,SAAS,EAAC,yGAAyG;YAAAC,QAAA,gBAEnHhE,OAAA;cAAQ8E,KAAK,EAAC,MAAM;cAAAd,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCvE,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAd,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCvE,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAd,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvE,OAAA;UACE4E,OAAO,EAAEzE,OAAQ;UACjB4D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAClD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAC5D,UAAU,iBACVX,OAAA;MAAK+D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3ChE,OAAA;QAAM6E,QAAQ,EAAElD,mBAAoB;QAACoC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxDhE,OAAA;UACE8E,KAAK,EAAErE,UAAW;UAClBsE,QAAQ,EAAGnD,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACoD,MAAM,CAACF,KAAK,CAAE;UAC/CG,WAAW,EAAC,qCAAqC;UACjDlB,SAAS,EAAC,iHAAiH;UAC3HmB,IAAI,EAAE;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFvE,OAAA;UAAK+D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDhE,OAAA;YAAM+D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCvD,UAAU,CAACgF,MAAM,EAAC,kBACrB;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPvE,OAAA;YACEmF,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAErE,UAAU,IAAI,CAACN,UAAU,CAACqB,IAAI,CAAC,CAAC,IAAIrB,UAAU,CAACgF,MAAM,GAAG,IAAK;YACvE1B,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAEpGjD,UAAU,GAAG,YAAY,GAAG;UAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAGDvE,OAAA;MAAK+D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,EAC1CzD,OAAO,gBACNP,OAAA;QAAK+D,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CAAC,GAAG0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACjD,GAAG,CAAC,CAACkD,CAAC,EAAEC,CAAC,kBACtB5F,OAAA;UAAa+D,SAAS,EAAC,eAAe;UAAAC,QAAA,eACpChE,OAAA;YAAK+D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhE,OAAA;cAAK+D,SAAS,EAAC;YAAkC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDvE,OAAA;cAAK+D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BhE,OAAA;gBAAK+D,SAAS,EAAC;cAA+B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDvE,OAAA;gBAAK+D,SAAS,EAAC;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CvE,OAAA;gBAAK+D,SAAS,EAAC;cAA+B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAREqB,CAAC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,GACJiB,gBAAgB,CAACC,MAAM,KAAK,CAAC,gBAC/BzF,OAAA;QAAK+D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhE,OAAA;UAAK+D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCvE,OAAA;UAAG+D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChDvE,OAAA;UAAG+D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAoC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,gBAENvE,OAAA;QAAK+D,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBwB,gBAAgB,CAAC/C,GAAG,CAACC,OAAO,IAAIgB,aAAa,CAAChB,OAAO,CAAC;MAAC;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CApUIH,eAAe;EAAA,QAQFJ,OAAO;AAAA;AAAAgG,EAAA,GARpB5F,eAAe;AAsUrB,eAAeA,eAAe;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}