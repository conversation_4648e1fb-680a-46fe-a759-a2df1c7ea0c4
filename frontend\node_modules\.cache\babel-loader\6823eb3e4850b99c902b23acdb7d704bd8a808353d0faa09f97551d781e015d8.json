{"ast": null, "code": "'use strict';\n\n// limit of Crypto.getRandomValues()\n// https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues\nvar MAX_BYTES = 65536;\n\n// Node supports requesting up to this number of bytes\n// https://github.com/nodejs/node/blob/master/lib/internal/crypto/random.js#L48\nvar MAX_UINT32 = 4294967295;\nfunction oldBrowser() {\n  throw new Error('Secure random number generation is not supported by this browser.\\nUse Chrome, Firefox or Internet Explorer 11');\n}\nvar Buffer = require('safe-buffer').Buffer;\nvar crypto = global.crypto || global.msCrypto;\nif (crypto && crypto.getRandomValues) {\n  module.exports = randomBytes;\n} else {\n  module.exports = oldBrowser;\n}\nfunction randomBytes(size, cb) {\n  // phantomjs needs to throw\n  if (size > MAX_UINT32) throw new RangeError('requested too many random bytes');\n  var bytes = Buffer.allocUnsafe(size);\n  if (size > 0) {\n    // getRandomValues fails on IE if size == 0\n    if (size > MAX_BYTES) {\n      // this is the max bytes crypto.getRandomValues\n      // can do at once see https://developer.mozilla.org/en-US/docs/Web/API/window.crypto.getRandomValues\n      for (var generated = 0; generated < size; generated += MAX_BYTES) {\n        // buffer.slice automatically checks if the end is past the end of\n        // the buffer so we don't have to here\n        crypto.getRandomValues(bytes.slice(generated, generated + MAX_BYTES));\n      }\n    } else {\n      crypto.getRandomValues(bytes);\n    }\n  }\n  if (typeof cb === 'function') {\n    return process.nextTick(function () {\n      cb(null, bytes);\n    });\n  }\n  return bytes;\n}", "map": {"version": 3, "names": ["MAX_BYTES", "MAX_UINT32", "old<PERSON><PERSON>er", "Error", "<PERSON><PERSON><PERSON>", "require", "crypto", "global", "msCrypto", "getRandomValues", "module", "exports", "randomBytes", "size", "cb", "RangeError", "bytes", "allocUnsafe", "generated", "slice", "process", "nextTick"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/randombytes/browser.js"], "sourcesContent": ["'use strict'\n\n// limit of Crypto.getRandomValues()\n// https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues\nvar MAX_BYTES = 65536\n\n// Node supports requesting up to this number of bytes\n// https://github.com/nodejs/node/blob/master/lib/internal/crypto/random.js#L48\nvar MAX_UINT32 = 4294967295\n\nfunction oldBrowser () {\n  throw new Error('Secure random number generation is not supported by this browser.\\nUse Chrome, Firefox or Internet Explorer 11')\n}\n\nvar Buffer = require('safe-buffer').Buffer\nvar crypto = global.crypto || global.msCrypto\n\nif (crypto && crypto.getRandomValues) {\n  module.exports = randomBytes\n} else {\n  module.exports = oldBrowser\n}\n\nfunction randomBytes (size, cb) {\n  // phantomjs needs to throw\n  if (size > MAX_UINT32) throw new RangeError('requested too many random bytes')\n\n  var bytes = Buffer.allocUnsafe(size)\n\n  if (size > 0) {  // getRandomValues fails on IE if size == 0\n    if (size > MAX_BYTES) { // this is the max bytes crypto.getRandomValues\n      // can do at once see https://developer.mozilla.org/en-US/docs/Web/API/window.crypto.getRandomValues\n      for (var generated = 0; generated < size; generated += MAX_BYTES) {\n        // buffer.slice automatically checks if the end is past the end of\n        // the buffer so we don't have to here\n        crypto.getRandomValues(bytes.slice(generated, generated + MAX_BYTES))\n      }\n    } else {\n      crypto.getRandomValues(bytes)\n    }\n  }\n\n  if (typeof cb === 'function') {\n    return process.nextTick(function () {\n      cb(null, bytes)\n    })\n  }\n\n  return bytes\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA,IAAIA,SAAS,GAAG,KAAK;;AAErB;AACA;AACA,IAAIC,UAAU,GAAG,UAAU;AAE3B,SAASC,UAAUA,CAAA,EAAI;EACrB,MAAM,IAAIC,KAAK,CAAC,gHAAgH,CAAC;AACnI;AAEA,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC,CAACD,MAAM;AAC1C,IAAIE,MAAM,GAAGC,MAAM,CAACD,MAAM,IAAIC,MAAM,CAACC,QAAQ;AAE7C,IAAIF,MAAM,IAAIA,MAAM,CAACG,eAAe,EAAE;EACpCC,MAAM,CAACC,OAAO,GAAGC,WAAW;AAC9B,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGT,UAAU;AAC7B;AAEA,SAASU,WAAWA,CAAEC,IAAI,EAAEC,EAAE,EAAE;EAC9B;EACA,IAAID,IAAI,GAAGZ,UAAU,EAAE,MAAM,IAAIc,UAAU,CAAC,iCAAiC,CAAC;EAE9E,IAAIC,KAAK,GAAGZ,MAAM,CAACa,WAAW,CAACJ,IAAI,CAAC;EAEpC,IAAIA,IAAI,GAAG,CAAC,EAAE;IAAG;IACf,IAAIA,IAAI,GAAGb,SAAS,EAAE;MAAE;MACtB;MACA,KAAK,IAAIkB,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGL,IAAI,EAAEK,SAAS,IAAIlB,SAAS,EAAE;QAChE;QACA;QACAM,MAAM,CAACG,eAAe,CAACO,KAAK,CAACG,KAAK,CAACD,SAAS,EAAEA,SAAS,GAAGlB,SAAS,CAAC,CAAC;MACvE;IACF,CAAC,MAAM;MACLM,MAAM,CAACG,eAAe,CAACO,KAAK,CAAC;IAC/B;EACF;EAEA,IAAI,OAAOF,EAAE,KAAK,UAAU,EAAE;IAC5B,OAAOM,OAAO,CAACC,QAAQ,CAAC,YAAY;MAClCP,EAAE,CAAC,IAAI,EAAEE,KAAK,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}