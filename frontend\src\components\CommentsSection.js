import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const CommentsSection = ({ postId, onClose }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const [sortBy, setSortBy] = useState('best');
  const [submitting, setSubmitting] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    fetchComments();
  }, [postId, sortBy]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/feed-comments/post/${postId}`, {
        params: { sort: sortBy, limit: 100 }
      });
      setComments(response.data.comments);
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    
    if (!newComment.trim()) return;

    setSubmitting(true);
    try {
      const response = await axios.post('/api/feed-comments', {
        post_id: postId,
        parent_comment_id: replyingTo?.id || null,
        content: newComment.trim()
      });

      // Add new comment to the list
      setComments(prev => [response.data, ...prev]);
      setNewComment('');
      setReplyingTo(null);

    } catch (error) {
      console.error('Error creating comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleVoteComment = async (commentId, voteType) => {
    try {
      const response = await axios.post(`/api/feed-comments/${commentId}/vote`, {
        vote_type: voteType
      });

      setComments(prev => prev.map(comment => 
        comment.id === commentId 
          ? { 
              ...comment, 
              upvotes: response.data.upvotes,
              downvotes: response.data.downvotes,
              user_vote: response.data.user_vote
            }
          : comment
      ));

    } catch (error) {
      console.error('Error voting on comment:', error);
    }
  };

  const handleReactComment = async (commentId, reactionType) => {
    try {
      const response = await axios.post(`/api/feed-comments/${commentId}/react`, {
        reaction_type: reactionType
      });

      setComments(prev => prev.map(comment => 
        comment.id === commentId 
          ? { 
              ...comment, 
              reactions: response.data.reactions
            }
          : comment
      ));

    } catch (error) {
      console.error('Error reacting to comment:', error);
    }
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const commentDate = new Date(dateString);
    const diffInSeconds = Math.floor((now - commentDate) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const renderComment = (comment, depth = 0) => {
    const netVotes = (comment.upvotes || 0) - (comment.downvotes || 0);
    const isReply = depth > 0;

    return (
      <div
        key={comment.id}
        className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''} ${
          depth > 5 ? 'ml-4' : ''
        }`}
      >
        <div className="bg-gray-50 rounded-lg p-4 mb-3">
          {/* Comment Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {comment.username?.charAt(0).toUpperCase() || 'U'}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 text-sm">{comment.username}</span>
                  {comment.verified_status === 'verified' && (
                    <span className="text-blue-500 text-xs" title="Verified">✓</span>
                  )}
                  {comment.role === 'admin' && (
                    <span className="bg-red-100 text-red-800 px-1 py-0.5 rounded text-xs">Admin</span>
                  )}
                </div>
                <span className="text-xs text-gray-500">{formatTimeAgo(comment.created_at)}</span>
              </div>
            </div>
            
            {/* Comment Score */}
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <span>{netVotes} points</span>
            </div>
          </div>

          {/* Comment Content */}
          <p className="text-gray-900 text-sm mb-3 whitespace-pre-wrap">{comment.content}</p>

          {/* Comment Actions */}
          <div className="flex items-center space-x-4">
            {/* Voting */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleVoteComment(comment.id, comment.user_vote === 1 ? 0 : 1)}
                className={`p-1 rounded text-xs ${
                  comment.user_vote === 1
                    ? 'text-green-600 bg-green-100'
                    : 'text-gray-500 hover:text-green-600 hover:bg-green-50'
                }`}
              >
                ⬆️ {comment.upvotes || 0}
              </button>
              <button
                onClick={() => handleVoteComment(comment.id, comment.user_vote === -1 ? 0 : -1)}
                className={`p-1 rounded text-xs ${
                  comment.user_vote === -1
                    ? 'text-red-600 bg-red-100'
                    : 'text-gray-500 hover:text-red-600 hover:bg-red-50'
                }`}
              >
                ⬇️ {comment.downvotes || 0}
              </button>
            </div>

            {/* Reply */}
            <button
              onClick={() => setReplyingTo(comment)}
              className="text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50"
            >
              💬 Reply
            </button>

            {/* React */}
            <button
              onClick={() => handleReactComment(comment.id, 'like')}
              className="text-xs text-gray-500 hover:text-blue-600 px-2 py-1 rounded hover:bg-blue-50"
            >
              👍 Like
            </button>
          </div>

          {/* Reply Form */}
          {replyingTo?.id === comment.id && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <form onSubmit={handleSubmitComment} className="space-y-2">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder={`Reply to ${comment.username}...`}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={2}
                />
                <div className="flex items-center space-x-2">
                  <button
                    type="submit"
                    disabled={submitting || !newComment.trim()}
                    className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 disabled:opacity-50"
                  >
                    {submitting ? 'Posting...' : 'Reply'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setReplyingTo(null);
                      setNewComment('');
                    }}
                    className="text-gray-500 hover:text-gray-700 px-3 py-1 rounded text-xs"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>

        {/* Render child comments */}
        {comments
          .filter(c => c.parent_comment_id === comment.id)
          .map(childComment => renderComment(childComment, depth + 1))}
      </div>
    );
  };

  const topLevelComments = comments.filter(comment => !comment.parent_comment_id);

  return (
    <div className="border-t border-gray-100 bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="font-semibold text-gray-900">
              Comments ({comments.length})
            </h3>
            
            {/* Sort Options */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="best">Best</option>
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
            </select>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            ✕
          </button>
        </div>
      </div>

      {/* New Comment Form */}
      {!replyingTo && (
        <div className="p-4 border-b border-gray-100">
          <form onSubmit={handleSubmitComment} className="space-y-3">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Share your thoughts on this post..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">
                {newComment.length}/1000 characters
              </span>
              <button
                type="submit"
                disabled={submitting || !newComment.trim() || newComment.length > 1000}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm"
              >
                {submitting ? 'Posting...' : 'Comment'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Comments List */}
      <div className="p-4 max-h-96 overflow-y-auto">
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-300 rounded"></div>
                    <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : topLevelComments.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">💬</div>
            <p className="text-gray-500">No comments yet</p>
            <p className="text-gray-400 text-sm">Be the first to share your thoughts!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {topLevelComments.map(comment => renderComment(comment))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentsSection;
