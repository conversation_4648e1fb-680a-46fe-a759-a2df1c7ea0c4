{"ast": null, "code": "'use strict';\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nvar codes = {};\nfunction createErrorType(code, message, Base) {\n  if (!Base) {\n    Base = Error;\n  }\n  function getMessage(arg1, arg2, arg3) {\n    if (typeof message === 'string') {\n      return message;\n    } else {\n      return message(arg1, arg2, arg3);\n    }\n  }\n  var NodeError = /*#__PURE__*/\n  function (_Base) {\n    _inheritsLoose(NodeError, _Base);\n    function NodeError(arg1, arg2, arg3) {\n      return _Base.call(this, getMessage(arg1, arg2, arg3)) || this;\n    }\n    return NodeError;\n  }(Base);\n  NodeError.prototype.name = Base.name;\n  NodeError.prototype.code = code;\n  codes[code] = NodeError;\n} // https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js\n\nfunction oneOf(expected, thing) {\n  if (Array.isArray(expected)) {\n    var len = expected.length;\n    expected = expected.map(function (i) {\n      return String(i);\n    });\n    if (len > 2) {\n      return \"one of \".concat(thing, \" \").concat(expected.slice(0, len - 1).join(', '), \", or \") + expected[len - 1];\n    } else if (len === 2) {\n      return \"one of \".concat(thing, \" \").concat(expected[0], \" or \").concat(expected[1]);\n    } else {\n      return \"of \".concat(thing, \" \").concat(expected[0]);\n    }\n  } else {\n    return \"of \".concat(thing, \" \").concat(String(expected));\n  }\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith\n\nfunction startsWith(str, search, pos) {\n  return str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith\n\nfunction endsWith(str, search, this_len) {\n  if (this_len === undefined || this_len > str.length) {\n    this_len = str.length;\n  }\n  return str.substring(this_len - search.length, this_len) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes\n\nfunction includes(str, search, start) {\n  if (typeof start !== 'number') {\n    start = 0;\n  }\n  if (start + search.length > str.length) {\n    return false;\n  } else {\n    return str.indexOf(search, start) !== -1;\n  }\n}\ncreateErrorType('ERR_INVALID_OPT_VALUE', function (name, value) {\n  return 'The value \"' + value + '\" is invalid for option \"' + name + '\"';\n}, TypeError);\ncreateErrorType('ERR_INVALID_ARG_TYPE', function (name, expected, actual) {\n  // determiner: 'must be' or 'must not be'\n  var determiner;\n  if (typeof expected === 'string' && startsWith(expected, 'not ')) {\n    determiner = 'must not be';\n    expected = expected.replace(/^not /, '');\n  } else {\n    determiner = 'must be';\n  }\n  var msg;\n  if (endsWith(name, ' argument')) {\n    // For cases like 'first argument'\n    msg = \"The \".concat(name, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  } else {\n    var type = includes(name, '.') ? 'property' : 'argument';\n    msg = \"The \\\"\".concat(name, \"\\\" \").concat(type, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  }\n  msg += \". Received type \".concat(typeof actual);\n  return msg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');\ncreateErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {\n  return 'The ' + name + ' method is not implemented';\n});\ncreateErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');\ncreateErrorType('ERR_STREAM_DESTROYED', function (name) {\n  return 'Cannot call ' + name + ' after a stream was destroyed';\n});\ncreateErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');\ncreateErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');\ncreateErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');\ncreateErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);\ncreateErrorType('ERR_UNKNOWN_ENCODING', function (arg) {\n  return 'Unknown encoding: ' + arg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');\nmodule.exports.codes = codes;", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "Object", "create", "constructor", "__proto__", "codes", "createErrorType", "code", "message", "Base", "Error", "getMessage", "arg1", "arg2", "arg3", "NodeError", "_Base", "call", "name", "oneOf", "expected", "thing", "Array", "isArray", "len", "length", "map", "i", "String", "concat", "slice", "join", "startsWith", "str", "search", "pos", "substr", "endsWith", "this_len", "undefined", "substring", "includes", "start", "indexOf", "value", "TypeError", "actual", "determiner", "replace", "msg", "type", "arg", "module", "exports"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/readable-stream/errors-browser.js"], "sourcesContent": ["'use strict';\n\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; subClass.__proto__ = superClass; }\n\nvar codes = {};\n\nfunction createErrorType(code, message, Base) {\n  if (!Base) {\n    Base = Error;\n  }\n\n  function getMessage(arg1, arg2, arg3) {\n    if (typeof message === 'string') {\n      return message;\n    } else {\n      return message(arg1, arg2, arg3);\n    }\n  }\n\n  var NodeError =\n  /*#__PURE__*/\n  function (_Base) {\n    _inheritsLoose(NodeError, _Base);\n\n    function NodeError(arg1, arg2, arg3) {\n      return _Base.call(this, getMessage(arg1, arg2, arg3)) || this;\n    }\n\n    return NodeError;\n  }(Base);\n\n  NodeError.prototype.name = Base.name;\n  NodeError.prototype.code = code;\n  codes[code] = NodeError;\n} // https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js\n\n\nfunction oneOf(expected, thing) {\n  if (Array.isArray(expected)) {\n    var len = expected.length;\n    expected = expected.map(function (i) {\n      return String(i);\n    });\n\n    if (len > 2) {\n      return \"one of \".concat(thing, \" \").concat(expected.slice(0, len - 1).join(', '), \", or \") + expected[len - 1];\n    } else if (len === 2) {\n      return \"one of \".concat(thing, \" \").concat(expected[0], \" or \").concat(expected[1]);\n    } else {\n      return \"of \".concat(thing, \" \").concat(expected[0]);\n    }\n  } else {\n    return \"of \".concat(thing, \" \").concat(String(expected));\n  }\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith\n\n\nfunction startsWith(str, search, pos) {\n  return str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith\n\n\nfunction endsWith(str, search, this_len) {\n  if (this_len === undefined || this_len > str.length) {\n    this_len = str.length;\n  }\n\n  return str.substring(this_len - search.length, this_len) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes\n\n\nfunction includes(str, search, start) {\n  if (typeof start !== 'number') {\n    start = 0;\n  }\n\n  if (start + search.length > str.length) {\n    return false;\n  } else {\n    return str.indexOf(search, start) !== -1;\n  }\n}\n\ncreateErrorType('ERR_INVALID_OPT_VALUE', function (name, value) {\n  return 'The value \"' + value + '\" is invalid for option \"' + name + '\"';\n}, TypeError);\ncreateErrorType('ERR_INVALID_ARG_TYPE', function (name, expected, actual) {\n  // determiner: 'must be' or 'must not be'\n  var determiner;\n\n  if (typeof expected === 'string' && startsWith(expected, 'not ')) {\n    determiner = 'must not be';\n    expected = expected.replace(/^not /, '');\n  } else {\n    determiner = 'must be';\n  }\n\n  var msg;\n\n  if (endsWith(name, ' argument')) {\n    // For cases like 'first argument'\n    msg = \"The \".concat(name, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  } else {\n    var type = includes(name, '.') ? 'property' : 'argument';\n    msg = \"The \\\"\".concat(name, \"\\\" \").concat(type, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  }\n\n  msg += \". Received type \".concat(typeof actual);\n  return msg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');\ncreateErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {\n  return 'The ' + name + ' method is not implemented';\n});\ncreateErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');\ncreateErrorType('ERR_STREAM_DESTROYED', function (name) {\n  return 'Cannot call ' + name + ' after a stream was destroyed';\n});\ncreateErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');\ncreateErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');\ncreateErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');\ncreateErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);\ncreateErrorType('ERR_UNKNOWN_ENCODING', function (arg) {\n  return 'Unknown encoding: ' + arg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');\nmodule.exports.codes = codes;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAED,QAAQ,CAACE,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,UAAU,CAACC,SAAS,CAAC;EAAEF,QAAQ,CAACE,SAAS,CAACG,WAAW,GAAGL,QAAQ;EAAEA,QAAQ,CAACM,SAAS,GAAGL,UAAU;AAAE;AAEtL,IAAIM,KAAK,GAAG,CAAC,CAAC;AAEd,SAASC,eAAeA,CAACC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC5C,IAAI,CAACA,IAAI,EAAE;IACTA,IAAI,GAAGC,KAAK;EACd;EAEA,SAASC,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACpC,IAAI,OAAON,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOA,OAAO;IAChB,CAAC,MAAM;MACL,OAAOA,OAAO,CAACI,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAClC;EACF;EAEA,IAAIC,SAAS,GACb;EACA,UAAUC,KAAK,EAAE;IACfnB,cAAc,CAACkB,SAAS,EAAEC,KAAK,CAAC;IAEhC,SAASD,SAASA,CAACH,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MACnC,OAAOE,KAAK,CAACC,IAAI,CAAC,IAAI,EAAEN,UAAU,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC,IAAI,IAAI;IAC/D;IAEA,OAAOC,SAAS;EAClB,CAAC,CAACN,IAAI,CAAC;EAEPM,SAAS,CAACf,SAAS,CAACkB,IAAI,GAAGT,IAAI,CAACS,IAAI;EACpCH,SAAS,CAACf,SAAS,CAACO,IAAI,GAAGA,IAAI;EAC/BF,KAAK,CAACE,IAAI,CAAC,GAAGQ,SAAS;AACzB,CAAC,CAAC;;AAGF,SAASI,KAAKA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC9B,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IAC3B,IAAII,GAAG,GAAGJ,QAAQ,CAACK,MAAM;IACzBL,QAAQ,GAAGA,QAAQ,CAACM,GAAG,CAAC,UAAUC,CAAC,EAAE;MACnC,OAAOC,MAAM,CAACD,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAIH,GAAG,GAAG,CAAC,EAAE;MACX,OAAO,SAAS,CAACK,MAAM,CAACR,KAAK,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACT,QAAQ,CAACU,KAAK,CAAC,CAAC,EAAEN,GAAG,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,GAAGX,QAAQ,CAACI,GAAG,GAAG,CAAC,CAAC;IAChH,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,SAAS,CAACK,MAAM,CAACR,KAAK,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACT,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAACS,MAAM,CAACT,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC,MAAM;MACL,OAAO,KAAK,CAACS,MAAM,CAACR,KAAK,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACT,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrD;EACF,CAAC,MAAM;IACL,OAAO,KAAK,CAACS,MAAM,CAACR,KAAK,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACD,MAAM,CAACR,QAAQ,CAAC,CAAC;EAC1D;AACF,CAAC,CAAC;;AAGF,SAASY,UAAUA,CAACC,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAE;EACpC,OAAOF,GAAG,CAACG,MAAM,CAAC,CAACD,GAAG,IAAIA,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAACA,GAAG,EAAED,MAAM,CAACT,MAAM,CAAC,KAAKS,MAAM;AACzE,CAAC,CAAC;;AAGF,SAASG,QAAQA,CAACJ,GAAG,EAAEC,MAAM,EAAEI,QAAQ,EAAE;EACvC,IAAIA,QAAQ,KAAKC,SAAS,IAAID,QAAQ,GAAGL,GAAG,CAACR,MAAM,EAAE;IACnDa,QAAQ,GAAGL,GAAG,CAACR,MAAM;EACvB;EAEA,OAAOQ,GAAG,CAACO,SAAS,CAACF,QAAQ,GAAGJ,MAAM,CAACT,MAAM,EAAEa,QAAQ,CAAC,KAAKJ,MAAM;AACrE,CAAC,CAAC;;AAGF,SAASO,QAAQA,CAACR,GAAG,EAAEC,MAAM,EAAEQ,KAAK,EAAE;EACpC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAG,CAAC;EACX;EAEA,IAAIA,KAAK,GAAGR,MAAM,CAACT,MAAM,GAAGQ,GAAG,CAACR,MAAM,EAAE;IACtC,OAAO,KAAK;EACd,CAAC,MAAM;IACL,OAAOQ,GAAG,CAACU,OAAO,CAACT,MAAM,EAAEQ,KAAK,CAAC,KAAK,CAAC,CAAC;EAC1C;AACF;AAEApC,eAAe,CAAC,uBAAuB,EAAE,UAAUY,IAAI,EAAE0B,KAAK,EAAE;EAC9D,OAAO,aAAa,GAAGA,KAAK,GAAG,2BAA2B,GAAG1B,IAAI,GAAG,GAAG;AACzE,CAAC,EAAE2B,SAAS,CAAC;AACbvC,eAAe,CAAC,sBAAsB,EAAE,UAAUY,IAAI,EAAEE,QAAQ,EAAE0B,MAAM,EAAE;EACxE;EACA,IAAIC,UAAU;EAEd,IAAI,OAAO3B,QAAQ,KAAK,QAAQ,IAAIY,UAAU,CAACZ,QAAQ,EAAE,MAAM,CAAC,EAAE;IAChE2B,UAAU,GAAG,aAAa;IAC1B3B,QAAQ,GAAGA,QAAQ,CAAC4B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EAC1C,CAAC,MAAM;IACLD,UAAU,GAAG,SAAS;EACxB;EAEA,IAAIE,GAAG;EAEP,IAAIZ,QAAQ,CAACnB,IAAI,EAAE,WAAW,CAAC,EAAE;IAC/B;IACA+B,GAAG,GAAG,MAAM,CAACpB,MAAM,CAACX,IAAI,EAAE,GAAG,CAAC,CAACW,MAAM,CAACkB,UAAU,EAAE,GAAG,CAAC,CAAClB,MAAM,CAACV,KAAK,CAACC,QAAQ,EAAE,MAAM,CAAC,CAAC;EACxF,CAAC,MAAM;IACL,IAAI8B,IAAI,GAAGT,QAAQ,CAACvB,IAAI,EAAE,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;IACxD+B,GAAG,GAAG,QAAQ,CAACpB,MAAM,CAACX,IAAI,EAAE,KAAK,CAAC,CAACW,MAAM,CAACqB,IAAI,EAAE,GAAG,CAAC,CAACrB,MAAM,CAACkB,UAAU,EAAE,GAAG,CAAC,CAAClB,MAAM,CAACV,KAAK,CAACC,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC9G;EAEA6B,GAAG,IAAI,kBAAkB,CAACpB,MAAM,CAAC,OAAOiB,MAAM,CAAC;EAC/C,OAAOG,GAAG;AACZ,CAAC,EAAEJ,SAAS,CAAC;AACbvC,eAAe,CAAC,2BAA2B,EAAE,yBAAyB,CAAC;AACvEA,eAAe,CAAC,4BAA4B,EAAE,UAAUY,IAAI,EAAE;EAC5D,OAAO,MAAM,GAAGA,IAAI,GAAG,4BAA4B;AACrD,CAAC,CAAC;AACFZ,eAAe,CAAC,4BAA4B,EAAE,iBAAiB,CAAC;AAChEA,eAAe,CAAC,sBAAsB,EAAE,UAAUY,IAAI,EAAE;EACtD,OAAO,cAAc,GAAGA,IAAI,GAAG,+BAA+B;AAChE,CAAC,CAAC;AACFZ,eAAe,CAAC,uBAAuB,EAAE,gCAAgC,CAAC;AAC1EA,eAAe,CAAC,wBAAwB,EAAE,2BAA2B,CAAC;AACtEA,eAAe,CAAC,4BAA4B,EAAE,iBAAiB,CAAC;AAChEA,eAAe,CAAC,wBAAwB,EAAE,qCAAqC,EAAEuC,SAAS,CAAC;AAC3FvC,eAAe,CAAC,sBAAsB,EAAE,UAAU6C,GAAG,EAAE;EACrD,OAAO,oBAAoB,GAAGA,GAAG;AACnC,CAAC,EAAEN,SAAS,CAAC;AACbvC,eAAe,CAAC,oCAAoC,EAAE,kCAAkC,CAAC;AACzF8C,MAAM,CAACC,OAAO,CAAChD,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}