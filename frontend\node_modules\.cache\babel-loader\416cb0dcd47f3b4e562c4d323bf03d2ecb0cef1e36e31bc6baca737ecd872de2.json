{"ast": null, "code": "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\nfunction getLens(b64) {\n  var len = b64.length;\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0;\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n  return arr;\n}\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join('');\n}\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n  return parts.join('');\n}", "map": {"version": 3, "names": ["exports", "byteLength", "toByteArray", "fromByteArray", "lookup", "revLookup", "Arr", "Uint8Array", "Array", "code", "i", "len", "length", "charCodeAt", "getLens", "b64", "Error", "validLen", "indexOf", "placeHoldersLen", "lens", "_byteLength", "tmp", "arr", "curByte", "tripletToBase64", "num", "encodeChunk", "uint8", "start", "end", "output", "push", "join", "extraBytes", "parts", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "len2"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/base64-js/index.js"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAGA,UAAU;AAC/BD,OAAO,CAACE,WAAW,GAAGA,WAAW;AACjCF,OAAO,CAACG,aAAa,GAAGA,aAAa;AAErC,IAAIC,MAAM,GAAG,EAAE;AACf,IAAIC,SAAS,GAAG,EAAE;AAClB,IAAIC,GAAG,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGC,KAAK;AAEhE,IAAIC,IAAI,GAAG,kEAAkE;AAC7E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,IAAI,CAACG,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAE,EAAED,CAAC,EAAE;EAC/CN,MAAM,CAACM,CAAC,CAAC,GAAGD,IAAI,CAACC,CAAC,CAAC;EACnBL,SAAS,CAACI,IAAI,CAACI,UAAU,CAACH,CAAC,CAAC,CAAC,GAAGA,CAAC;AACnC;;AAEA;AACA;AACAL,SAAS,CAAC,GAAG,CAACQ,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AACjCR,SAAS,CAAC,GAAG,CAACQ,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AAEjC,SAASC,OAAOA,CAAEC,GAAG,EAAE;EACrB,IAAIJ,GAAG,GAAGI,GAAG,CAACH,MAAM;EAEpB,IAAID,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;IACf,MAAM,IAAIK,KAAK,CAAC,gDAAgD,CAAC;EACnE;;EAEA;EACA;EACA,IAAIC,QAAQ,GAAGF,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC;EAC/B,IAAID,QAAQ,KAAK,CAAC,CAAC,EAAEA,QAAQ,GAAGN,GAAG;EAEnC,IAAIQ,eAAe,GAAGF,QAAQ,KAAKN,GAAG,GAClC,CAAC,GACD,CAAC,GAAIM,QAAQ,GAAG,CAAE;EAEtB,OAAO,CAACA,QAAQ,EAAEE,eAAe,CAAC;AACpC;;AAEA;AACA,SAASlB,UAAUA,CAAEc,GAAG,EAAE;EACxB,IAAIK,IAAI,GAAGN,OAAO,CAACC,GAAG,CAAC;EACvB,IAAIE,QAAQ,GAAGG,IAAI,CAAC,CAAC,CAAC;EACtB,IAAID,eAAe,GAAGC,IAAI,CAAC,CAAC,CAAC;EAC7B,OAAQ,CAACH,QAAQ,GAAGE,eAAe,IAAI,CAAC,GAAG,CAAC,GAAIA,eAAe;AACjE;AAEA,SAASE,WAAWA,CAAEN,GAAG,EAAEE,QAAQ,EAAEE,eAAe,EAAE;EACpD,OAAQ,CAACF,QAAQ,GAAGE,eAAe,IAAI,CAAC,GAAG,CAAC,GAAIA,eAAe;AACjE;AAEA,SAASjB,WAAWA,CAAEa,GAAG,EAAE;EACzB,IAAIO,GAAG;EACP,IAAIF,IAAI,GAAGN,OAAO,CAACC,GAAG,CAAC;EACvB,IAAIE,QAAQ,GAAGG,IAAI,CAAC,CAAC,CAAC;EACtB,IAAID,eAAe,GAAGC,IAAI,CAAC,CAAC,CAAC;EAE7B,IAAIG,GAAG,GAAG,IAAIjB,GAAG,CAACe,WAAW,CAACN,GAAG,EAAEE,QAAQ,EAAEE,eAAe,CAAC,CAAC;EAE9D,IAAIK,OAAO,GAAG,CAAC;;EAEf;EACA,IAAIb,GAAG,GAAGQ,eAAe,GAAG,CAAC,GACzBF,QAAQ,GAAG,CAAC,GACZA,QAAQ;EAEZ,IAAIP,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3BY,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,CAAC,CAAC,IAAI,EAAE,GAClCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAG,GACvCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE,GACvCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC;IAClCa,GAAG,CAACC,OAAO,EAAE,CAAC,GAAIF,GAAG,IAAI,EAAE,GAAI,IAAI;IACnCC,GAAG,CAACC,OAAO,EAAE,CAAC,GAAIF,GAAG,IAAI,CAAC,GAAI,IAAI;IAClCC,GAAG,CAACC,OAAO,EAAE,CAAC,GAAGF,GAAG,GAAG,IAAI;EAC7B;EAEA,IAAIH,eAAe,KAAK,CAAC,EAAE;IACzBG,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,CAAC,CAAC,IAAI,CAAC,GACjCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE;IACzCa,GAAG,CAACC,OAAO,EAAE,CAAC,GAAGF,GAAG,GAAG,IAAI;EAC7B;EAEA,IAAIH,eAAe,KAAK,CAAC,EAAE;IACzBG,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,CAAC,CAAC,IAAI,EAAE,GAClCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE,GACtCL,SAAS,CAACU,GAAG,CAACF,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE;IACzCa,GAAG,CAACC,OAAO,EAAE,CAAC,GAAIF,GAAG,IAAI,CAAC,GAAI,IAAI;IAClCC,GAAG,CAACC,OAAO,EAAE,CAAC,GAAGF,GAAG,GAAG,IAAI;EAC7B;EAEA,OAAOC,GAAG;AACZ;AAEA,SAASE,eAAeA,CAAEC,GAAG,EAAE;EAC7B,OAAOtB,MAAM,CAACsB,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,GAC7BtB,MAAM,CAACsB,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,GACxBtB,MAAM,CAACsB,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GACvBtB,MAAM,CAACsB,GAAG,GAAG,IAAI,CAAC;AACtB;AAEA,SAASC,WAAWA,CAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACvC,IAAIR,GAAG;EACP,IAAIS,MAAM,GAAG,EAAE;EACf,KAAK,IAAIrB,CAAC,GAAGmB,KAAK,EAAEnB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,IAAI,CAAC,EAAE;IACnCY,GAAG,GACD,CAAEM,KAAK,CAAClB,CAAC,CAAC,IAAI,EAAE,GAAI,QAAQ,KAC1BkB,KAAK,CAAClB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAI,MAAM,CAAC,IAC7BkB,KAAK,CAAClB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACvBqB,MAAM,CAACC,IAAI,CAACP,eAAe,CAACH,GAAG,CAAC,CAAC;EACnC;EACA,OAAOS,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;AACxB;AAEA,SAAS9B,aAAaA,CAAEyB,KAAK,EAAE;EAC7B,IAAIN,GAAG;EACP,IAAIX,GAAG,GAAGiB,KAAK,CAAChB,MAAM;EACtB,IAAIsB,UAAU,GAAGvB,GAAG,GAAG,CAAC,EAAC;EACzB,IAAIwB,KAAK,GAAG,EAAE;EACd,IAAIC,cAAc,GAAG,KAAK,EAAC;;EAE3B;EACA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAE2B,IAAI,GAAG1B,GAAG,GAAGuB,UAAU,EAAExB,CAAC,GAAG2B,IAAI,EAAE3B,CAAC,IAAI0B,cAAc,EAAE;IACtED,KAAK,CAACH,IAAI,CAACL,WAAW,CAACC,KAAK,EAAElB,CAAC,EAAGA,CAAC,GAAG0B,cAAc,GAAIC,IAAI,GAAGA,IAAI,GAAI3B,CAAC,GAAG0B,cAAe,CAAC,CAAC;EAC9F;;EAEA;EACA,IAAIF,UAAU,KAAK,CAAC,EAAE;IACpBZ,GAAG,GAAGM,KAAK,CAACjB,GAAG,GAAG,CAAC,CAAC;IACpBwB,KAAK,CAACH,IAAI,CACR5B,MAAM,CAACkB,GAAG,IAAI,CAAC,CAAC,GAChBlB,MAAM,CAAEkB,GAAG,IAAI,CAAC,GAAI,IAAI,CAAC,GACzB,IACF,CAAC;EACH,CAAC,MAAM,IAAIY,UAAU,KAAK,CAAC,EAAE;IAC3BZ,GAAG,GAAG,CAACM,KAAK,CAACjB,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIiB,KAAK,CAACjB,GAAG,GAAG,CAAC,CAAC;IAC5CwB,KAAK,CAACH,IAAI,CACR5B,MAAM,CAACkB,GAAG,IAAI,EAAE,CAAC,GACjBlB,MAAM,CAAEkB,GAAG,IAAI,CAAC,GAAI,IAAI,CAAC,GACzBlB,MAAM,CAAEkB,GAAG,IAAI,CAAC,GAAI,IAAI,CAAC,GACzB,GACF,CAAC;EACH;EAEA,OAAOa,KAAK,CAACF,IAAI,CAAC,EAAE,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}