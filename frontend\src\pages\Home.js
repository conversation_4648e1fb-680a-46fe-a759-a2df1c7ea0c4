import React from 'react';
import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="container">
      <div className="hero">
        <h1>Welcome to Politica</h1>
        <p>Your comprehensive platform for political education, discussion, and research</p>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <Link to="/knowledge" className="btn btn-primary">
            Explore Knowledge Base
          </Link>
          <Link to="/discussions" className="btn btn-secondary">
            Join Discussions
          </Link>
        </div>
      </div>

      <div className="features">
        <div className="feature-card">
          <h3>Knowledge Base</h3>
          <p>Multi-level political education content from basic to advanced concepts.</p>
          <Link to="/knowledge" style={{ color: '#3b82f6', fontWeight: '500' }}>
            Learn More →
          </Link>
        </div>

        <div className="feature-card">
          <h3>Discussion Forums</h3>
          <p>Engage in thoughtful political discussions with moderated threads.</p>
          <Link to="/discussions" style={{ color: '#3b82f6', fontWeight: '500' }}>
            Join Discussion →
          </Link>
        </div>

        <div className="feature-card">
          <h3>Live Debates</h3>
          <p>Participate in scheduled live video debates on current political topics.</p>
          <Link to="/debates" style={{ color: '#3b82f6', fontWeight: '500' }}>
            Watch Debates →
          </Link>
        </div>

        <div className="feature-card">
          <h3>Research Repository</h3>
          <p>Access and submit peer-reviewed political research papers.</p>
          <Link to="/research" style={{ color: '#3b82f6', fontWeight: '500' }}>
            Browse Research →
          </Link>
        </div>
      </div>

      <div style={{ textAlign: 'center', padding: '4rem 0', backgroundColor: '#374151', color: 'white', margin: '2rem -1rem 0' }}>
        <h2>Ready to Get Started?</h2>
        <p>Join our community of informed citizens and contribute to meaningful political discourse.</p>
        <Link to="/register" className="btn btn-primary">
          Create Account
        </Link>
      </div>
    </div>
  );
};

export default Home;
