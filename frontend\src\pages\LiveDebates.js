import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useWebSocket } from '../contexts/WebSocketContext';
import DebateChat from '../components/DebateChat';
import axios from 'axios';

const LiveDebates = () => {
  const [debates, setDebates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedDebate, setSelectedDebate] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [activeChatDebateId, setActiveChatDebateId] = useState(null);
  const [newDebate, setNewDebate] = useState({
    title: '',
    description: '',
    topic: '',
    scheduled_at: ''
  });
  const { user, token, isAuthenticated, isVerified } = useAuth();
  const {
    isConnected,
    activeDebates,
    joinDebate,
    leaveDebate,
    sendDebateMessage,
    getDebateParticipantCount,
    getDebateMessages
  } = useWebSocket();

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

  const fetchDebates = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/debates`);
      setDebates(response.data.data || []);
    } catch (err) {
      setError('Failed to fetch debates');
      console.error('Error fetching debates:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    fetchDebates();
  }, [fetchDebates]);

  const handleCreateDebate = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError('Please login to create a debate');
      return;
    }

    try {
      const response = await axios.post(
        `${API_URL}/api/debates`,
        newDebate,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        setNewDebate({ title: '', description: '', topic: '', scheduled_at: '' });
        setShowCreateForm(false);
        fetchDebates(); // Refresh the list
      }
    } catch (err) {
      setError('Failed to create debate');
      console.error('Error creating debate:', err);
    }
  };

  const handleJoinDebate = async (debateId) => {
    if (!isAuthenticated) {
      setError('Please login to join a debate');
      return;
    }

    try {
      const response = await axios.post(
        `${API_URL}/api/debates/${debateId}/join`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        // Join WebSocket room for real-time features
        joinDebate(debateId);
        fetchDebates(); // Refresh to show updated participant list
      }
    } catch (err) {
      setError('Failed to join debate');
      console.error('Error joining debate:', err);
    }
  };

  const handleJoinLiveDebate = (debateId) => {
    if (!isAuthenticated) {
      setError('Please login to join a live debate');
      return;
    }

    // Join WebSocket room for real-time features
    joinDebate(debateId);

    // Open chat for live debate
    setActiveChatDebateId(debateId);
  };

  const handleLeaveDebate = (debateId) => {
    leaveDebate(debateId);
    if (activeChatDebateId === debateId) {
      setActiveChatDebateId(null);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'live':
        return 'bg-red-100 text-red-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'live':
        return '🔴';
      case 'scheduled':
        return '📅';
      case 'completed':
        return '✅';
      default:
        return '📅';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading debates...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchDebates}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Create debate form
  if (showCreateForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setShowCreateForm(false)}
              className="text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Debates
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Schedule New Debate</h1>
            <p className="text-gray-600">Create a new live debate for the community.</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <form onSubmit={handleCreateDebate}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Debate Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={newDebate.title}
                  onChange={(e) => setNewDebate({ ...newDebate, title: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter a compelling debate title"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-2">
                  Topic/Category *
                </label>
                <select
                  id="topic"
                  value={newDebate.topic}
                  onChange={(e) => setNewDebate({ ...newDebate, topic: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a topic</option>
                  <option value="Healthcare Policy">Healthcare Policy</option>
                  <option value="Environmental Policy">Environmental Policy</option>
                  <option value="Economic Policy">Economic Policy</option>
                  <option value="Education Policy">Education Policy</option>
                  <option value="Foreign Policy">Foreign Policy</option>
                  <option value="Social Issues">Social Issues</option>
                  <option value="Constitutional Law">Constitutional Law</option>
                  <option value="Electoral Reform">Electoral Reform</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  id="description"
                  value={newDebate.description}
                  onChange={(e) => setNewDebate({ ...newDebate, description: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="4"
                  placeholder="Describe the debate topic and format"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="scheduled_at" className="block text-sm font-medium text-gray-700 mb-2">
                  Scheduled Date & Time *
                </label>
                <input
                  type="datetime-local"
                  id="scheduled_at"
                  value={newDebate.scheduled_at}
                  onChange={(e) => setNewDebate({ ...newDebate, scheduled_at: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Schedule Debate
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Live Debates</h1>
            <p className="text-lg text-gray-600">
              Watch and participate in live political debates
            </p>
          </div>
          {isAuthenticated && isVerified && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
            >
              Schedule Debate
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* All Debates */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">All Debates</h2>
            <div className="space-y-4">
              {debates.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-gray-400 text-4xl mb-4">🎙️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No debates scheduled</h3>
                  <p className="text-gray-600 mb-4">Be the first to schedule a political debate!</p>
                  {isAuthenticated && isVerified && (
                    <button
                      onClick={() => setShowCreateForm(true)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                      Schedule First Debate
                    </button>
                  )}
                </div>
              ) : (
                debates.map((debate) => (
                  <div key={debate.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{debate.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(debate.status)}`}>
                        {getStatusIcon(debate.status)} {debate.status}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">{debate.description}</p>
                    <div className="text-sm text-gray-500 space-y-1">
                      <p><strong>Topic:</strong> {debate.topic}</p>
                      <p><strong>Scheduled:</strong> {formatDate(debate.scheduled_at)}</p>
                      <p><strong>Moderator:</strong> {debate.moderator_username}</p>
                      <p>
                        <strong>Participants:</strong> {debate.participant_count} registered
                        {getDebateParticipantCount(debate.id) > 0 && (
                          <span style={{ color: '#10b981', marginLeft: '0.5rem' }}>
                            • {getDebateParticipantCount(debate.id)} live now
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="mt-4 flex space-x-2 flex-wrap gap-2">
                      {debate.status === 'live' && (
                        <>
                          <button
                            onClick={() => handleJoinLiveDebate(debate.id)}
                            className="bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700"
                          >
                            🔴 Join Live
                          </button>
                          <button
                            onClick={() => setActiveChatDebateId(activeChatDebateId === debate.id ? null : debate.id)}
                            className={`px-4 py-2 rounded-md text-sm border ${
                              activeChatDebateId === debate.id
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            💬 Chat {getDebateMessages(debate.id).length > 0 && `(${getDebateMessages(debate.id).length})`}
                          </button>
                        </>
                      )}
                      {debate.status === 'scheduled' && isAuthenticated && (
                        <button
                          onClick={() => handleJoinDebate(debate.id)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
                        >
                          Join Debate
                        </button>
                      )}
                      <button
                        onClick={() => setSelectedDebate(debate)}
                        className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Live Now / Featured */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Live Now</h2>
            {debates.filter(d => d.status === 'live').length > 0 ? (
              <div className="space-y-4">
                {debates.filter(d => d.status === 'live').map((debate) => (
                  <div key={debate.id} className="border-2 border-red-200 rounded-lg p-4 bg-red-50">
                    <div className="flex items-center mb-2">
                      <span className="text-red-600 text-lg mr-2">🔴</span>
                      <h3 className="text-lg font-medium text-gray-900">{debate.title}</h3>
                      <span className="ml-auto bg-red-600 text-white px-2 py-1 rounded text-xs">LIVE</span>
                    </div>
                    <p className="text-gray-600 mb-3">{debate.description}</p>
                    <button className="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                      🔴 Join Live Debate
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📺</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Live Debates</h3>
                <p className="text-gray-600 mb-4">
                  There are no debates currently live. Check back later or browse scheduled debates.
                </p>
                {!isAuthenticated && (
                  <a href="/login" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Login to Participate
                  </a>
                )}
              </div>
            )}
          </div>
        </div>

        {/* How It Works */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">How Live Debates Work</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Join the Stream</h3>
              <p className="text-sm text-gray-600">
                Click to join the live video stream when a debate is active
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">2</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Participate in Chat</h3>
              <p className="text-sm text-gray-600">
                Engage with other viewers through the live chat feature
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">3</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Vote in Polls</h3>
              <p className="text-sm text-gray-600">
                Participate in real-time polls during the debate
              </p>
            </div>
          </div>
        </div>

        {/* Requirements Notice */}
        {isAuthenticated && !isVerified && (
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="text-yellow-600 text-xl mr-3">⚠️</div>
              <div>
                <h3 className="text-lg font-medium text-yellow-800 mb-2">Verification Required</h3>
                <p className="text-yellow-700">
                  To schedule debates and participate as a moderator, you need to be a verified user.
                  <a href="/profile" className="underline ml-1">Request verification here</a>.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Real-time Debate Chat */}
        <DebateChat
          debateId={activeChatDebateId}
          isVisible={!!activeChatDebateId}
          onClose={() => {
            if (activeChatDebateId) {
              handleLeaveDebate(activeChatDebateId);
            }
          }}
        />
      </div>
    </div>
  );
};

export default LiveDebates;
