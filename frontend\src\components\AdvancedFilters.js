import React, { useState } from 'react';

const AdvancedFilters = ({ 
  selectedTopics, 
  setSelectedTopics, 
  dateRange, 
  setDateRange, 
  sortBy, 
  setSortBy,
  onApplyFilters,
  onClearFilters 
}) => {
  const [showTopics, setShowTopics] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const politicalTopics = [
    { id: 'climate', name: 'Climate Change', icon: '🌱', color: 'bg-green-100 text-green-800' },
    { id: 'healthcare', name: 'Healthcare', icon: '🏥', color: 'bg-blue-100 text-blue-800' },
    { id: 'economy', name: 'Economy', icon: '💰', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'immigration', name: 'Immigration', icon: '🌍', color: 'bg-purple-100 text-purple-800' },
    { id: 'education', name: 'Education', icon: '🎓', color: 'bg-indigo-100 text-indigo-800' },
    { id: 'defense', name: 'Defense', icon: '🛡️', color: 'bg-red-100 text-red-800' },
    { id: 'technology', name: 'Technology', icon: '💻', color: 'bg-cyan-100 text-cyan-800' },
    { id: 'energy', name: 'Energy', icon: '⚡', color: 'bg-orange-100 text-orange-800' },
    { id: 'justice', name: 'Criminal Justice', icon: '⚖️', color: 'bg-gray-100 text-gray-800' },
    { id: 'foreign', name: 'Foreign Policy', icon: '🌐', color: 'bg-teal-100 text-teal-800' }
  ];

  const dateRanges = [
    { value: 'all', label: 'All Time' },
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last Week' },
    { value: '30d', label: 'Last Month' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' }
  ];

  const sortOptions = [
    { value: 'recent', label: 'Most Recent', icon: '🕒' },
    { value: 'popular', label: 'Most Popular', icon: '🔥' },
    { value: 'engagement', label: 'Most Engaging', icon: '💬' },
    { value: 'controversial', label: 'Most Controversial', icon: '⚡' },
    { value: 'trending', label: 'Trending Now', icon: '📈' }
  ];

  const toggleTopic = (topicId) => {
    setSelectedTopics(prev => 
      prev.includes(topicId) 
        ? prev.filter(id => id !== topicId)
        : [...prev, topicId]
    );
  };

  const handleClearAll = () => {
    setSelectedTopics([]);
    setDateRange('all');
    setSortBy('recent');
    onClearFilters();
  };

  return (
    <div className="card p-6 mb-6 animate-slide-up">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-secondary-900 flex items-center gap-2">
          🔍 Advanced Filters
        </h3>
        <div className="flex items-center gap-2">
          <button
            onClick={handleClearAll}
            className="btn btn-ghost btn-sm"
          >
            Clear All
          </button>
          <button
            onClick={onApplyFilters}
            className="btn btn-primary btn-sm"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        {/* Sort Options */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-3">
            Sort By
          </label>
          <div className="space-y-2">
            {sortOptions.map(option => (
              <label
                key={option.value}
                className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all ${
                  sortBy === option.value
                    ? 'bg-primary-50 border-2 border-primary-200'
                    : 'bg-secondary-50 border-2 border-transparent hover:bg-secondary-100'
                }`}
              >
                <input
                  type="radio"
                  name="sortBy"
                  value={option.value}
                  checked={sortBy === option.value}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="sr-only"
                />
                <span className="text-lg">{option.icon}</span>
                <span className="text-sm font-medium text-secondary-900">
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-3">
            Time Period
          </label>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="form-select w-full"
          >
            {dateRanges.map(range => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>

          {/* Custom Date Range */}
          <div className="mt-4 space-y-2">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-primary-600 hover:text-primary-700 flex items-center gap-1"
            >
              📅 Custom Date Range
              <span className={`transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>
            
            {showAdvanced && (
              <div className="space-y-3 p-4 bg-secondary-50 rounded-lg animate-slide-up">
                <div>
                  <label className="block text-xs font-medium text-secondary-600 mb-1">
                    From
                  </label>
                  <input
                    type="date"
                    className="form-input w-full text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-secondary-600 mb-1">
                    To
                  </label>
                  <input
                    type="date"
                    className="form-input w-full text-sm"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Political Topics */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-secondary-700">
              Political Topics
            </label>
            <button
              onClick={() => setShowTopics(!showTopics)}
              className="text-sm text-primary-600 hover:text-primary-700"
            >
              {showTopics ? 'Show Less' : 'Show All'}
            </button>
          </div>

          <div className="space-y-2">
            {/* Selected Topics */}
            {selectedTopics.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {selectedTopics.map(topicId => {
                  const topic = politicalTopics.find(t => t.id === topicId);
                  return topic ? (
                    <span
                      key={topicId}
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${topic.color}`}
                    >
                      <span>{topic.icon}</span>
                      <span>{topic.name}</span>
                      <button
                        onClick={() => toggleTopic(topicId)}
                        className="ml-1 hover:bg-black hover:bg-opacity-10 rounded-full p-0.5"
                      >
                        ×
                      </button>
                    </span>
                  ) : null;
                })}
              </div>
            )}

            {/* Topic Grid */}
            <div className={`grid grid-cols-2 gap-2 ${!showTopics ? 'max-h-32 overflow-hidden' : ''}`}>
              {politicalTopics.map(topic => (
                <label
                  key={topic.id}
                  className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-all ${
                    selectedTopics.includes(topic.id)
                      ? 'bg-primary-50 border border-primary-200'
                      : 'bg-secondary-50 hover:bg-secondary-100 border border-transparent'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={selectedTopics.includes(topic.id)}
                    onChange={() => toggleTopic(topic.id)}
                    className="sr-only"
                  />
                  <span className="text-sm">{topic.icon}</span>
                  <span className="text-xs font-medium text-secondary-900 truncate">
                    {topic.name}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Filters */}
      <div className="mt-6 pt-6 border-t border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          
          {/* Content Type */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Content Type
            </label>
            <select className="form-select w-full text-sm">
              <option value="all">All Types</option>
              <option value="text">Text Posts</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="poll">Polls</option>
              <option value="article">Articles</option>
            </select>
          </div>

          {/* Engagement Level */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Engagement
            </label>
            <select className="form-select w-full text-sm">
              <option value="all">All Levels</option>
              <option value="high">High Engagement</option>
              <option value="medium">Medium Engagement</option>
              <option value="low">Low Engagement</option>
            </select>
          </div>

          {/* Verification Status */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Author Type
            </label>
            <select className="form-select w-full text-sm">
              <option value="all">All Authors</option>
              <option value="verified">Verified Only</option>
              <option value="officials">Government Officials</option>
              <option value="media">Media Organizations</option>
              <option value="experts">Policy Experts</option>
            </select>
          </div>

          {/* Language */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Language
            </label>
            <select className="form-select w-full text-sm">
              <option value="all">All Languages</option>
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>
        </div>
      </div>

      {/* Filter Summary */}
      {(selectedTopics.length > 0 || dateRange !== 'all' || sortBy !== 'recent') && (
        <div className="mt-6 p-4 bg-primary-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-primary-700">
                Active Filters:
              </span>
              <span className="text-sm text-primary-600">
                {selectedTopics.length > 0 && `${selectedTopics.length} topics`}
                {dateRange !== 'all' && `, ${dateRanges.find(r => r.value === dateRange)?.label}`}
                {sortBy !== 'recent' && `, sorted by ${sortOptions.find(s => s.value === sortBy)?.label}`}
              </span>
            </div>
            <button
              onClick={handleClearAll}
              className="text-sm text-primary-600 hover:text-primary-700 underline"
            >
              Clear All
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilters;
