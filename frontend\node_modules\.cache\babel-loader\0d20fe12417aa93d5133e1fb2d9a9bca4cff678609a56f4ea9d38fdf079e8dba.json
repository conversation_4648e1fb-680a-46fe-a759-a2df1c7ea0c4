{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _verificationRequests;\n  const {\n    user,\n    token,\n    isAuthenticated,\n    updateProfile: updateAuthProfile\n  } = useAuth();\n  const [profile, setProfile] = useState({\n    username: '',\n    bio: '',\n    role: 'regular',\n    verified_status: false\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState('');\n  const [verificationRequests, setVerificationRequests] = useState([]);\n  const [showVerificationForm, setShowVerificationForm] = useState(false);\n  const [verificationData, setVerificationData] = useState({\n    credentials: '',\n    expertise_area: '',\n    reason: ''\n  });\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  useEffect(() => {\n    if (isAuthenticated) {\n      getProfile();\n      fetchVerificationRequests();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n  const getProfile = async () => {\n    try {\n      setLoading(true);\n      if (user) {\n        setProfile({\n          username: user.username || '',\n          bio: user.bio || '',\n          role: user.role || 'regular',\n          verified_status: user.verified_status || false\n        });\n      }\n    } catch (error) {\n      console.error('Error loading profile:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchVerificationRequests = async () => {\n    try {\n      const response = await axios.get(`${API_URL}/api/verification/my-requests`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setVerificationRequests(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching verification requests:', error);\n    }\n  };\n  const updateProfile = async e => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n    try {\n      const result = await updateAuthProfile({\n        username: profile.username,\n        bio: profile.bio\n      });\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n      } else {\n        setMessage('Error updating profile: ' + result.error);\n      }\n    } catch (error) {\n      setMessage('Error updating profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const submitVerificationRequest = async e => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n    try {\n      const response = await axios.post(`${API_URL}/api/verification/request`, verificationData, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setMessage('Verification request submitted successfully!');\n        setShowVerificationForm(false);\n        setVerificationData({\n          credentials: '',\n          expertise_area: '',\n          reason: ''\n        });\n        fetchVerificationRequests(); // Refresh requests\n      }\n    } catch (error) {\n      setMessage('Error submitting verification request');\n      console.error('Error:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading profile...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Please log in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Verification form\n  if (showVerificationForm) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowVerificationForm(false),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Request Verification\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Submit your credentials for verification to gain access to advanced features.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: submitVerificationRequest,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"expertise_area\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Area of Expertise *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"expertise_area\",\n                value: verificationData.expertise_area,\n                onChange: e => setVerificationData({\n                  ...verificationData,\n                  expertise_area: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select your area of expertise\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Political Science\",\n                  children: \"Political Science\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Public Policy\",\n                  children: \"Public Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"International Relations\",\n                  children: \"International Relations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Constitutional Law\",\n                  children: \"Constitutional Law\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Economics\",\n                  children: \"Economics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Journalism\",\n                  children: \"Journalism\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Government Official\",\n                  children: \"Government Official\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Academic Researcher\",\n                  children: \"Academic Researcher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"credentials\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Credentials *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"credentials\",\n                value: verificationData.credentials,\n                onChange: e => setVerificationData({\n                  ...verificationData,\n                  credentials: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"4\",\n                placeholder: \"List your relevant credentials, degrees, certifications, or professional experience\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"reason\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Reason for Verification *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"reason\",\n                value: verificationData.reason,\n                onChange: e => setVerificationData({\n                  ...verificationData,\n                  reason: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"4\",\n                placeholder: \"Explain why you want to be verified and how you plan to contribute to the platform\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowVerificationForm(false),\n                className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: saving,\n                className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                children: saving ? 'Submitting...' : 'Submit Request'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Manage your account settings and preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Profile Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mb-4 p-3 rounded-md ${message.includes('Error') ? 'bg-red-50 text-red-600 border border-red-200' : 'bg-green-50 text-green-600 border border-green-200'}`,\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: updateProfile,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  value: (user === null || user === void 0 ? void 0 : user.email) || '',\n                  disabled: true,\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-500\",\n                  children: \"Email cannot be changed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"username\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  value: profile.username,\n                  onChange: e => setProfile({\n                    ...profile,\n                    username: e.target.value\n                  }),\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"bio\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Bio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"bio\",\n                  rows: 4,\n                  value: profile.bio,\n                  onChange: e => setProfile({\n                    ...profile,\n                    bio: e.target.value\n                  }),\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                  placeholder: \"Tell us about yourself...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: saving,\n                  className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\",\n                  children: saving ? 'Saving...' : 'Save Changes'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Account Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${profile.role === 'admin' ? 'bg-purple-100 text-purple-800' : profile.verified_status ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: profile.role === 'admin' ? 'Admin' : profile.verified_status ? 'Verified' : 'Regular'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Verification Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${profile.verified_status ? 'bg-green-100 text-green-800' : verificationRequests.length > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: profile.verified_status ? 'Verified ✓' : verificationRequests.length > 0 ? 'Pending Review' : 'Not Verified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Member Since\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-900\",\n                  children: user !== null && user !== void 0 && user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), !profile.verified_status && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: [verificationRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowVerificationForm(true),\n                className: \"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 text-sm\",\n                children: \"Request Verification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-yellow-600 mb-2\",\n                  children: \"Verification request submitted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Status: \", ((_verificationRequests = verificationRequests[0]) === null || _verificationRequests === void 0 ? void 0 : _verificationRequests.status) || 'Pending']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-xs text-gray-500\",\n                children: \"Verified users can create articles, submit research, and schedule debates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), verificationRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Verification Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: verificationRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: request.expertise_area\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${request.status === 'approved' ? 'bg-green-100 text-green-800' : request.status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                    children: request.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Submitted: \", new Date(request.created_at).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this), request.admin_notes && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Admin Notes:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 27\n                  }, this), \" \", request.admin_notes]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 25\n                }, this)]\n              }, request.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Platform Benefits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-500 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Read knowledge base articles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-500 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Participate in discussions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-500 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Watch live debates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`,\n                  children: profile.verified_status ? '✓' : '○'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: profile.verified_status ? 'text-gray-600' : 'text-gray-400',\n                  children: \"Create and edit articles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`,\n                  children: profile.verified_status ? '✓' : '○'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: profile.verified_status ? 'text-gray-600' : 'text-gray-400',\n                  children: \"Submit research papers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`,\n                  children: profile.verified_status ? '✓' : '○'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: profile.verified_status ? 'text-gray-600' : 'text-gray-400',\n                  children: \"Schedule live debates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"IlfmPaZFJSSNfpD/TIabNLOixOs=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "axios", "jsxDEV", "_jsxDEV", "Profile", "_s", "_verificationRequests", "user", "token", "isAuthenticated", "updateProfile", "updateAuthProfile", "profile", "setProfile", "username", "bio", "role", "verified_status", "loading", "setLoading", "saving", "setSaving", "message", "setMessage", "verificationRequests", "setVerificationRequests", "showVerificationForm", "setShowVerificationForm", "verificationData", "setVerificationData", "credentials", "expertise_area", "reason", "API_URL", "process", "env", "REACT_APP_API_URL", "getProfile", "fetchVerificationRequests", "error", "console", "response", "get", "headers", "data", "e", "preventDefault", "result", "success", "submitVerificationRequest", "post", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "onSubmit", "htmlFor", "id", "value", "onChange", "target", "required", "rows", "placeholder", "type", "disabled", "includes", "email", "length", "created_at", "Date", "toLocaleDateString", "status", "map", "request", "admin_notes", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst Profile = () => {\n  const { user, token, isAuthenticated, updateProfile: updateAuthProfile } = useAuth();\n  const [profile, setProfile] = useState({\n    username: '',\n    bio: '',\n    role: 'regular',\n    verified_status: false\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState('');\n  const [verificationRequests, setVerificationRequests] = useState([]);\n  const [showVerificationForm, setShowVerificationForm] = useState(false);\n  const [verificationData, setVerificationData] = useState({\n    credentials: '',\n    expertise_area: '',\n    reason: ''\n  });\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      getProfile();\n      fetchVerificationRequests();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n\n  const getProfile = async () => {\n    try {\n      setLoading(true);\n      if (user) {\n        setProfile({\n          username: user.username || '',\n          bio: user.bio || '',\n          role: user.role || 'regular',\n          verified_status: user.verified_status || false\n        });\n      }\n    } catch (error) {\n      console.error('Error loading profile:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchVerificationRequests = async () => {\n    try {\n      const response = await axios.get(`${API_URL}/api/verification/my-requests`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setVerificationRequests(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching verification requests:', error);\n    }\n  };\n\n  const updateProfile = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n\n    try {\n      const result = await updateAuthProfile({\n        username: profile.username,\n        bio: profile.bio\n      });\n\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n      } else {\n        setMessage('Error updating profile: ' + result.error);\n      }\n    } catch (error) {\n      setMessage('Error updating profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const submitVerificationRequest = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/verification/request`,\n        verificationData,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setMessage('Verification request submitted successfully!');\n        setShowVerificationForm(false);\n        setVerificationData({ credentials: '', expertise_area: '', reason: '' });\n        fetchVerificationRequests(); // Refresh requests\n      }\n    } catch (error) {\n      setMessage('Error submitting verification request');\n      console.error('Error:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading profile...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Access Denied</h2>\n          <p className=\"text-gray-600 mb-4\">Please log in to view your profile.</p>\n          <a href=\"/login\" className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\">\n            Login\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  // Verification form\n  if (showVerificationForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setShowVerificationForm(false)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Profile\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Request Verification</h1>\n            <p className=\"text-gray-600\">Submit your credentials for verification to gain access to advanced features.</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <form onSubmit={submitVerificationRequest}>\n              <div className=\"mb-6\">\n                <label htmlFor=\"expertise_area\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Area of Expertise *\n                </label>\n                <select\n                  id=\"expertise_area\"\n                  value={verificationData.expertise_area}\n                  onChange={(e) => setVerificationData({ ...verificationData, expertise_area: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">Select your area of expertise</option>\n                  <option value=\"Political Science\">Political Science</option>\n                  <option value=\"Public Policy\">Public Policy</option>\n                  <option value=\"International Relations\">International Relations</option>\n                  <option value=\"Constitutional Law\">Constitutional Law</option>\n                  <option value=\"Economics\">Economics</option>\n                  <option value=\"Journalism\">Journalism</option>\n                  <option value=\"Government Official\">Government Official</option>\n                  <option value=\"Academic Researcher\">Academic Researcher</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"credentials\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Credentials *\n                </label>\n                <textarea\n                  id=\"credentials\"\n                  value={verificationData.credentials}\n                  onChange={(e) => setVerificationData({ ...verificationData, credentials: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"4\"\n                  placeholder=\"List your relevant credentials, degrees, certifications, or professional experience\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"reason\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Reason for Verification *\n                </label>\n                <textarea\n                  id=\"reason\"\n                  value={verificationData.reason}\n                  onChange={(e) => setVerificationData({ ...verificationData, reason: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"4\"\n                  placeholder=\"Explain why you want to be verified and how you plan to contribute to the platform\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowVerificationForm(false)}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={saving}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n                >\n                  {saving ? 'Submitting...' : 'Submit Request'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Profile</h1>\n          <p className=\"text-lg text-gray-600\">\n            Manage your account settings and preferences\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Profile Info */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Profile Information</h2>\n\n              {message && (\n                <div className={`mb-4 p-3 rounded-md ${\n                  message.includes('Error')\n                    ? 'bg-red-50 text-red-600 border border-red-200'\n                    : 'bg-green-50 text-green-600 border border-green-200'\n                }`}>\n                  {message}\n                </div>\n              )}\n\n              <form onSubmit={updateProfile} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={user?.email || ''}\n                    disabled\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                  />\n                  <p className=\"mt-1 text-sm text-gray-500\">Email cannot be changed</p>\n                </div>\n\n                <div>\n                  <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                    Username\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    value={profile.username}\n                    onChange={(e) => setProfile({ ...profile, username: e.target.value })}\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700\">\n                    Bio\n                  </label>\n                  <textarea\n                    id=\"bio\"\n                    rows={4}\n                    value={profile.bio}\n                    onChange={(e) => setProfile({ ...profile, bio: e.target.value })}\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Tell us about yourself...\"\n                  />\n                </div>\n\n                <div>\n                  <button\n                    type=\"submit\"\n                    disabled={saving}\n                    className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n                  >\n                    {saving ? 'Saving...' : 'Save Changes'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Account Status */}\n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Account Status</h3>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Role</span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    profile.role === 'admin' ? 'bg-purple-100 text-purple-800' :\n                    profile.verified_status ? 'bg-green-100 text-green-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {profile.role === 'admin' ? 'Admin' : profile.verified_status ? 'Verified' : 'Regular'}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Verification Status</span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    profile.verified_status\n                      ? 'bg-green-100 text-green-800'\n                      : verificationRequests.length > 0\n                      ? 'bg-yellow-100 text-yellow-800'\n                      : 'bg-gray-100 text-gray-800'\n                  }`}>\n                    {profile.verified_status ? 'Verified ✓' :\n                     verificationRequests.length > 0 ? 'Pending Review' : 'Not Verified'}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Member Since</span>\n                  <span className=\"text-sm text-gray-900\">\n                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}\n                  </span>\n                </div>\n              </div>\n\n              {!profile.verified_status && (\n                <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                  {verificationRequests.length === 0 ? (\n                    <button\n                      onClick={() => setShowVerificationForm(true)}\n                      className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 text-sm\"\n                    >\n                      Request Verification\n                    </button>\n                  ) : (\n                    <div className=\"text-center\">\n                      <p className=\"text-sm text-yellow-600 mb-2\">\n                        Verification request submitted\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        Status: {verificationRequests[0]?.status || 'Pending'}\n                      </p>\n                    </div>\n                  )}\n                  <p className=\"mt-2 text-xs text-gray-500\">\n                    Verified users can create articles, submit research, and schedule debates\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* Verification Requests */}\n            {verificationRequests.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Verification Requests</h3>\n                <div className=\"space-y-3\">\n                  {verificationRequests.map((request) => (\n                    <div key={request.id} className=\"border border-gray-200 rounded-lg p-3\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <span className=\"text-sm font-medium text-gray-900\">\n                          {request.expertise_area}\n                        </span>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                          request.status === 'approved' ? 'bg-green-100 text-green-800' :\n                          request.status === 'rejected' ? 'bg-red-100 text-red-800' :\n                          'bg-yellow-100 text-yellow-800'\n                        }`}>\n                          {request.status}\n                        </span>\n                      </div>\n                      <p className=\"text-xs text-gray-500\">\n                        Submitted: {new Date(request.created_at).toLocaleDateString()}\n                      </p>\n                      {request.admin_notes && (\n                        <p className=\"text-xs text-gray-600 mt-1\">\n                          <strong>Admin Notes:</strong> {request.admin_notes}\n                        </p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Platform Benefits</h3>\n\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-500 mr-2\">✓</span>\n                  <span className=\"text-gray-600\">Read knowledge base articles</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-500 mr-2\">✓</span>\n                  <span className=\"text-gray-600\">Participate in discussions</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-500 mr-2\">✓</span>\n                  <span className=\"text-gray-600\">Watch live debates</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>\n                    {profile.verified_status ? '✓' : '○'}\n                  </span>\n                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>\n                    Create and edit articles\n                  </span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>\n                    {profile.verified_status ? '✓' : '○'}\n                  </span>\n                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>\n                    Submit research papers\n                  </span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>\n                    {profile.verified_status ? '✓' : '○'}\n                  </span>\n                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>\n                    Schedule live debates\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAkB,CAAC,GAAGX,OAAO,CAAC,CAAC;EACpF,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC;IACrCiB,QAAQ,EAAE,EAAE;IACZC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,SAAS;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC6B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC;IACvDiC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExEtC,SAAS,CAAC,MAAM;IACd,IAAIW,eAAe,EAAE;MACnB4B,UAAU,CAAC,CAAC;MACZC,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,eAAe,CAAC,CAAC;EAErB,MAAM4B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIZ,IAAI,EAAE;QACRM,UAAU,CAAC;UACTC,QAAQ,EAAEP,IAAI,CAACO,QAAQ,IAAI,EAAE;UAC7BC,GAAG,EAAER,IAAI,CAACQ,GAAG,IAAI,EAAE;UACnBC,IAAI,EAAET,IAAI,CAACS,IAAI,IAAI,SAAS;UAC5BC,eAAe,EAAEV,IAAI,CAACU,eAAe,IAAI;QAC3C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,GAAGT,OAAO,+BAA+B,EAAE;QAC1EU,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnC,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFiB,uBAAuB,CAACgB,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,MAAM7B,aAAa,GAAG,MAAOmC,CAAC,IAAK;IACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzB,SAAS,CAAC,IAAI,CAAC;IACfE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMwB,MAAM,GAAG,MAAMpC,iBAAiB,CAAC;QACrCG,QAAQ,EAAEF,OAAO,CAACE,QAAQ;QAC1BC,GAAG,EAAEH,OAAO,CAACG;MACf,CAAC,CAAC;MAEF,IAAIgC,MAAM,CAACC,OAAO,EAAE;QAClBzB,UAAU,CAAC,+BAA+B,CAAC;MAC7C,CAAC,MAAM;QACLA,UAAU,CAAC,0BAA0B,GAAGwB,MAAM,CAACR,KAAK,CAAC;MACvD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdhB,UAAU,CAAC,wBAAwB,CAAC;IACtC,CAAC,SAAS;MACRF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM4B,yBAAyB,GAAG,MAAOJ,CAAC,IAAK;IAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzB,SAAS,CAAC,IAAI,CAAC;IACfE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMxC,KAAK,CAACiD,IAAI,CAC/B,GAAGjB,OAAO,2BAA2B,EACrCL,gBAAgB,EAChB;QACEe,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnC,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIiC,QAAQ,CAACG,IAAI,CAACI,OAAO,EAAE;QACzBzB,UAAU,CAAC,8CAA8C,CAAC;QAC1DI,uBAAuB,CAAC,KAAK,CAAC;QAC9BE,mBAAmB,CAAC;UAAEC,WAAW,EAAE,EAAE;UAAEC,cAAc,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAC,CAAC;QACxEM,yBAAyB,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,UAAU,CAAC,uCAAuC,CAAC;MACnDiB,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC,CAAC,SAAS;MACRlB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKgD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAKgD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FrD,OAAA;UAAGgD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC/C,eAAe,EAAE;IACpB,oBACEN,OAAA;MAAKgD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAIgD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxErD,OAAA;UAAGgD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzErD,OAAA;UAAGsD,IAAI,EAAC,QAAQ;UAACN,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAE3F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI9B,oBAAoB,EAAE;IACxB,oBACEvB,OAAA;MAAKgD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCjD,OAAA;QAAKgD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DjD,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjD,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAM/B,uBAAuB,CAAC,KAAK,CAAE;YAC9CwB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YAAIgD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ErD,OAAA;YAAGgD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA6E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDjD,OAAA;YAAMwD,QAAQ,EAAEV,yBAA0B;YAAAG,QAAA,gBACxCjD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjD,OAAA;gBAAOyD,OAAO,EAAC,gBAAgB;gBAACT,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEzF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACE0D,EAAE,EAAC,gBAAgB;gBACnBC,KAAK,EAAElC,gBAAgB,CAACG,cAAe;gBACvCgC,QAAQ,EAAGlB,CAAC,IAAKhB,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEG,cAAc,EAAEc,CAAC,CAACmB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC9FX,SAAS,EAAC,wFAAwF;gBAClGc,QAAQ;gBAAAb,QAAA,gBAERjD,OAAA;kBAAQ2D,KAAK,EAAC,EAAE;kBAAAV,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDrD,OAAA;kBAAQ2D,KAAK,EAAC,mBAAmB;kBAAAV,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DrD,OAAA;kBAAQ2D,KAAK,EAAC,eAAe;kBAAAV,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDrD,OAAA;kBAAQ2D,KAAK,EAAC,yBAAyB;kBAAAV,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxErD,OAAA;kBAAQ2D,KAAK,EAAC,oBAAoB;kBAAAV,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DrD,OAAA;kBAAQ2D,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CrD,OAAA;kBAAQ2D,KAAK,EAAC,YAAY;kBAAAV,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CrD,OAAA;kBAAQ2D,KAAK,EAAC,qBAAqB;kBAAAV,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChErD,OAAA;kBAAQ2D,KAAK,EAAC,qBAAqB;kBAAAV,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChErD,OAAA;kBAAQ2D,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjD,OAAA;gBAAOyD,OAAO,EAAC,aAAa;gBAACT,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACE0D,EAAE,EAAC,aAAa;gBAChBC,KAAK,EAAElC,gBAAgB,CAACE,WAAY;gBACpCiC,QAAQ,EAAGlB,CAAC,IAAKhB,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEE,WAAW,EAAEe,CAAC,CAACmB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC3FX,SAAS,EAAC,wFAAwF;gBAClGe,IAAI,EAAC,GAAG;gBACRC,WAAW,EAAC,qFAAqF;gBACjGF,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjD,OAAA;gBAAOyD,OAAO,EAAC,QAAQ;gBAACT,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEjF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACE0D,EAAE,EAAC,QAAQ;gBACXC,KAAK,EAAElC,gBAAgB,CAACI,MAAO;gBAC/B+B,QAAQ,EAAGlB,CAAC,IAAKhB,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEI,MAAM,EAAEa,CAAC,CAACmB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACtFX,SAAS,EAAC,wFAAwF;gBAClGe,IAAI,EAAC,GAAG;gBACRC,WAAW,EAAC,oFAAoF;gBAChGF,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCjD,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbV,OAAO,EAAEA,CAAA,KAAM/B,uBAAuB,CAAC,KAAK,CAAE;gBAC9CwB,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrD,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbC,QAAQ,EAAEjD,MAAO;gBACjB+B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAE5FhC,MAAM,GAAG,eAAe,GAAG;cAAgB;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKgD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCjD,OAAA;MAAKgD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DjD,OAAA;QAAKgD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjD,OAAA;UAAIgD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClErD,OAAA;UAAGgD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrD,OAAA;QAAKgD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDjD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjD,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEhFlC,OAAO,iBACNnB,OAAA;cAAKgD,SAAS,EAAE,uBACd7B,OAAO,CAACgD,QAAQ,CAAC,OAAO,CAAC,GACrB,8CAA8C,GAC9C,oDAAoD,EACvD;cAAAlB,QAAA,EACA9B;YAAO;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAEDrD,OAAA;cAAMwD,QAAQ,EAAEjD,aAAc;cAACyC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAClDjD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOyD,OAAO,EAAC,OAAO;kBAACT,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACEiE,IAAI,EAAC,OAAO;kBACZP,EAAE,EAAC,OAAO;kBACVC,KAAK,EAAE,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,KAAK,KAAI,EAAG;kBACzBF,QAAQ;kBACRlB,SAAS,EAAC;gBAAwF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACFrD,OAAA;kBAAGgD,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eAENrD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOyD,OAAO,EAAC,UAAU;kBAACT,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE9E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACEiE,IAAI,EAAC,MAAM;kBACXP,EAAE,EAAC,UAAU;kBACbC,KAAK,EAAElD,OAAO,CAACE,QAAS;kBACxBiD,QAAQ,EAAGlB,CAAC,IAAKhC,UAAU,CAAC;oBAAE,GAAGD,OAAO;oBAAEE,QAAQ,EAAE+B,CAAC,CAACmB,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACtEX,SAAS,EAAC;gBAA4H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOyD,OAAO,EAAC,KAAK;kBAACT,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEzE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACE0D,EAAE,EAAC,KAAK;kBACRK,IAAI,EAAE,CAAE;kBACRJ,KAAK,EAAElD,OAAO,CAACG,GAAI;kBACnBgD,QAAQ,EAAGlB,CAAC,IAAKhC,UAAU,CAAC;oBAAE,GAAGD,OAAO;oBAAEG,GAAG,EAAE8B,CAAC,CAACmB,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACjEX,SAAS,EAAC,4HAA4H;kBACtIgB,WAAW,EAAC;gBAA2B;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAAiD,QAAA,eACEjD,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACbC,QAAQ,EAAEjD,MAAO;kBACjB+B,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAE3KhC,MAAM,GAAG,WAAW,GAAG;gBAAc;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrD,OAAA;UAAKgD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjD,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5ErD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjD,OAAA;gBAAKgD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDjD,OAAA;kBAAMgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDrD,OAAA;kBAAMgD,SAAS,EAAE,8CACfvC,OAAO,CAACI,IAAI,KAAK,OAAO,GAAG,+BAA+B,GAC1DJ,OAAO,CAACK,eAAe,GAAG,6BAA6B,GACvD,2BAA2B,EAC1B;kBAAAmC,QAAA,EACAxC,OAAO,CAACI,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGJ,OAAO,CAACK,eAAe,GAAG,UAAU,GAAG;gBAAS;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrD,OAAA;gBAAKgD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDjD,OAAA;kBAAMgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClErD,OAAA;kBAAMgD,SAAS,EAAE,8CACfvC,OAAO,CAACK,eAAe,GACnB,6BAA6B,GAC7BO,oBAAoB,CAACgD,MAAM,GAAG,CAAC,GAC/B,+BAA+B,GAC/B,2BAA2B,EAC9B;kBAAApB,QAAA,EACAxC,OAAO,CAACK,eAAe,GAAG,YAAY,GACtCO,oBAAoB,CAACgD,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;gBAAc;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrD,OAAA;gBAAKgD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDjD,OAAA;kBAAMgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3DrD,OAAA;kBAAMgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpC7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkE,UAAU,GAAG,IAAIC,IAAI,CAACnE,IAAI,CAACkE,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL,CAAC5C,OAAO,CAACK,eAAe,iBACvBd,OAAA;cAAKgD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAChD5B,oBAAoB,CAACgD,MAAM,KAAK,CAAC,gBAChCrE,OAAA;gBACEuD,OAAO,EAAEA,CAAA,KAAM/B,uBAAuB,CAAC,IAAI,CAAE;gBAC7CwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAC3F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAETrD,OAAA;gBAAKgD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjD,OAAA;kBAAGgD,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJrD,OAAA;kBAAGgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,UAC3B,EAAC,EAAA9C,qBAAA,GAAAkB,oBAAoB,CAAC,CAAC,CAAC,cAAAlB,qBAAA,uBAAvBA,qBAAA,CAAyBsE,MAAM,KAAI,SAAS;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,eACDrD,OAAA;gBAAGgD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLhC,oBAAoB,CAACgD,MAAM,GAAG,CAAC,iBAC9BrE,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjD,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFrD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB5B,oBAAoB,CAACqD,GAAG,CAAEC,OAAO,iBAChC3E,OAAA;gBAAsBgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACrEjD,OAAA;kBAAKgD,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDjD,OAAA;oBAAMgD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAChD0B,OAAO,CAAC/C;kBAAc;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACPrD,OAAA;oBAAMgD,SAAS,EAAE,8CACf2B,OAAO,CAACF,MAAM,KAAK,UAAU,GAAG,6BAA6B,GAC7DE,OAAO,CAACF,MAAM,KAAK,UAAU,GAAG,yBAAyB,GACzD,+BAA+B,EAC9B;oBAAAxB,QAAA,EACA0B,OAAO,CAACF;kBAAM;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrD,OAAA;kBAAGgD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,aACxB,EAAC,IAAIsB,IAAI,CAACI,OAAO,CAACL,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,EACHsB,OAAO,CAACC,WAAW,iBAClB5E,OAAA;kBAAGgD,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACvCjD,OAAA;oBAAAiD,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACsB,OAAO,CAACC,WAAW;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CACJ;cAAA,GApBOsB,OAAO,CAACjB,EAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDrD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjD,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE/ErD,OAAA;cAAKgD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAMgD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNrD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAMgD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNrD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAMgD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNrD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAE,QAAQvC,OAAO,CAACK,eAAe,GAAG,gBAAgB,GAAG,eAAe,EAAG;kBAAAmC,QAAA,EACrFxC,OAAO,CAACK,eAAe,GAAG,GAAG,GAAG;gBAAG;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACPrD,OAAA;kBAAMgD,SAAS,EAAEvC,OAAO,CAACK,eAAe,GAAG,eAAe,GAAG,eAAgB;kBAAAmC,QAAA,EAAC;gBAE9E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAE,QAAQvC,OAAO,CAACK,eAAe,GAAG,gBAAgB,GAAG,eAAe,EAAG;kBAAAmC,QAAA,EACrFxC,OAAO,CAACK,eAAe,GAAG,GAAG,GAAG;gBAAG;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACPrD,OAAA;kBAAMgD,SAAS,EAAEvC,OAAO,CAACK,eAAe,GAAG,eAAe,GAAG,eAAgB;kBAAAmC,QAAA,EAAC;gBAE9E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrD,OAAA;gBAAKgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjD,OAAA;kBAAMgD,SAAS,EAAE,QAAQvC,OAAO,CAACK,eAAe,GAAG,gBAAgB,GAAG,eAAe,EAAG;kBAAAmC,QAAA,EACrFxC,OAAO,CAACK,eAAe,GAAG,GAAG,GAAG;gBAAG;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACPrD,OAAA;kBAAMgD,SAAS,EAAEvC,OAAO,CAACK,eAAe,GAAG,eAAe,GAAG,eAAgB;kBAAAmC,QAAA,EAAC;gBAE9E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CA9cID,OAAO;EAAA,QACgEJ,OAAO;AAAA;AAAAgF,EAAA,GAD9E5E,OAAO;AAgdb,eAAeA,OAAO;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}