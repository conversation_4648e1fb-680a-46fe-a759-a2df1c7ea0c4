{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\contexts\\\\WebSocketContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState, useRef } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketContext = /*#__PURE__*/createContext();\nexport const useWebSocket = () => {\n  _s();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s(useWebSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [socket, setSocket] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [activeDebates, setActiveDebates] = useState({});\n  const [debateMessages, setDebateMessages] = useState({});\n  const [notifications, setNotifications] = useState([]);\n  const [typingUsers, setTypingUsers] = useState({});\n  const socketRef = useRef(null);\n  const typingTimeoutRef = useRef({});\n  useEffect(() => {\n    if (user && token) {\n      // Initialize socket connection\n      const newSocket = io(process.env.REACT_APP_API_URL || 'http://localhost:3000', {\n        autoConnect: true,\n        reconnection: true,\n        reconnectionAttempts: 5,\n        reconnectionDelay: 1000\n      });\n      socketRef.current = newSocket;\n      setSocket(newSocket);\n\n      // Connection event handlers\n      newSocket.on('connect', () => {\n        console.log('🔌 Connected to WebSocket server');\n        setIsConnected(true);\n\n        // Authenticate with the server\n        newSocket.emit('authenticate', token);\n      });\n      newSocket.on('disconnect', () => {\n        console.log('🔌 Disconnected from WebSocket server');\n        setIsConnected(false);\n      });\n      newSocket.on('authenticated', data => {\n        if (data.success) {\n          console.log('✅ WebSocket authentication successful');\n        } else {\n          console.error('❌ WebSocket authentication failed:', data.error);\n        }\n      });\n\n      // Debate event handlers\n      newSocket.on('user-joined-debate', data => {\n        console.log(`👥 User ${data.username} joined debate`);\n        addNotification({\n          type: 'info',\n          message: `${data.username} joined the debate`,\n          timestamp: data.timestamp\n        });\n      });\n      newSocket.on('user-left-debate', data => {\n        console.log(`👋 User ${data.username} left debate`);\n        addNotification({\n          type: 'info',\n          message: `${data.username} left the debate`,\n          timestamp: data.timestamp\n        });\n      });\n      newSocket.on('participant-count-updated', data => {\n        setActiveDebates(prev => ({\n          ...prev,\n          [data.debateId]: data.count\n        }));\n      });\n      newSocket.on('debate-chat-message', message => {\n        setDebateMessages(prev => ({\n          ...prev,\n          [message.debateId]: [...(prev[message.debateId] || []), message]\n        }));\n      });\n      newSocket.on('debate-status-updated', data => {\n        addNotification({\n          type: 'info',\n          message: `Debate status updated to: ${data.status}`,\n          timestamp: data.timestamp\n        });\n      });\n\n      // Discussion event handlers\n      newSocket.on('new-comment', data => {\n        addNotification({\n          type: 'comment',\n          message: `${data.username} added a new comment`,\n          threadId: data.threadId,\n          timestamp: data.timestamp\n        });\n      });\n      newSocket.on('user-typing', data => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [data.threadId]: {\n            ...prev[data.threadId],\n            [data.userId]: data.username\n          }\n        }));\n\n        // Clear typing indicator after 3 seconds\n        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {\n          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);\n        }\n        typingTimeoutRef.current[`${data.threadId}-${data.userId}`] = setTimeout(() => {\n          setTypingUsers(prev => {\n            const newState = {\n              ...prev\n            };\n            if (newState[data.threadId]) {\n              delete newState[data.threadId][data.userId];\n              if (Object.keys(newState[data.threadId]).length === 0) {\n                delete newState[data.threadId];\n              }\n            }\n            return newState;\n          });\n        }, 3000);\n      });\n      newSocket.on('user-stopped-typing', data => {\n        setTypingUsers(prev => {\n          const newState = {\n            ...prev\n          };\n          if (newState[data.threadId]) {\n            delete newState[data.threadId][data.userId];\n            if (Object.keys(newState[data.threadId]).length === 0) {\n              delete newState[data.threadId];\n            }\n          }\n          return newState;\n        });\n        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {\n          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);\n          delete typingTimeoutRef.current[`${data.threadId}-${data.userId}`];\n        }\n      });\n\n      // General notification handler\n      newSocket.on('notification', notification => {\n        addNotification(notification);\n      });\n      newSocket.on('error', error => {\n        console.error('WebSocket error:', error);\n        addNotification({\n          type: 'error',\n          message: error.message || 'WebSocket error occurred',\n          timestamp: new Date().toISOString()\n        });\n      });\n      return () => {\n        newSocket.disconnect();\n        setSocket(null);\n        setIsConnected(false);\n\n        // Clear all timeouts\n        Object.values(typingTimeoutRef.current).forEach(timeout => {\n          clearTimeout(timeout);\n        });\n        typingTimeoutRef.current = {};\n      };\n    }\n  }, [user, token]);\n  const addNotification = notification => {\n    const newNotification = {\n      id: Date.now().toString(),\n      ...notification\n    };\n    setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep last 50 notifications\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  };\n  const removeNotification = id => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  // Debate functions\n  const joinDebate = debateId => {\n    if (socket && isConnected) {\n      socket.emit('join-debate', debateId);\n    }\n  };\n  const leaveDebate = debateId => {\n    if (socket && isConnected) {\n      socket.emit('leave-debate', debateId);\n    }\n  };\n  const sendDebateMessage = (debateId, message) => {\n    if (socket && isConnected) {\n      socket.emit('debate-chat-message', {\n        debateId,\n        message\n      });\n    }\n  };\n  const voteInPoll = (debateId, pollId, option) => {\n    if (socket && isConnected) {\n      socket.emit('debate-poll-vote', {\n        debateId,\n        pollId,\n        option\n      });\n    }\n  };\n\n  // Discussion functions\n  const joinDiscussion = threadId => {\n    if (socket && isConnected) {\n      socket.emit('join-discussion', threadId);\n    }\n  };\n  const leaveDiscussion = threadId => {\n    if (socket && isConnected) {\n      socket.emit('leave-discussion', threadId);\n    }\n  };\n  const notifyNewComment = (threadId, commentId, content) => {\n    if (socket && isConnected) {\n      socket.emit('new-comment', {\n        threadId,\n        commentId,\n        content\n      });\n    }\n  };\n  const startTyping = threadId => {\n    if (socket && isConnected) {\n      socket.emit('typing-start', {\n        threadId\n      });\n    }\n  };\n  const stopTyping = threadId => {\n    if (socket && isConnected) {\n      socket.emit('typing-stop', {\n        threadId\n      });\n    }\n  };\n  const value = {\n    socket,\n    isConnected,\n    activeDebates,\n    debateMessages,\n    notifications,\n    typingUsers,\n    // Actions\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    voteInPoll,\n    joinDiscussion,\n    leaveDiscussion,\n    notifyNewComment,\n    startTyping,\n    stopTyping,\n    removeNotification,\n    // Utilities\n    getDebateParticipantCount: debateId => activeDebates[debateId] || 0,\n    getDebateMessages: debateId => debateMessages[debateId] || [],\n    getTypingUsers: threadId => typingUsers[threadId] || {}\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s2(WebSocketProvider, \"KR3tFTFjGQpTXYTB+sz+LgMwVH0=\", false, function () {\n  return [useAuth];\n});\n_c = WebSocketProvider;\nexport default WebSocketContext;\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useRef", "io", "useAuth", "jsxDEV", "_jsxDEV", "WebSocketContext", "useWebSocket", "_s", "context", "Error", "WebSocketProvider", "children", "_s2", "user", "token", "socket", "setSocket", "isConnected", "setIsConnected", "activeDebates", "setActiveDebates", "debateMessages", "setDebateMessages", "notifications", "setNotifications", "typingUsers", "setTypingUsers", "socketRef", "typingTimeoutRef", "newSocket", "process", "env", "REACT_APP_API_URL", "autoConnect", "reconnection", "reconnectionAttempts", "reconnectionDelay", "current", "on", "console", "log", "emit", "data", "success", "error", "username", "addNotification", "type", "message", "timestamp", "prev", "debateId", "count", "status", "threadId", "userId", "clearTimeout", "setTimeout", "newState", "Object", "keys", "length", "notification", "Date", "toISOString", "disconnect", "values", "for<PERSON>ach", "timeout", "newNotification", "id", "now", "toString", "slice", "removeNotification", "filter", "n", "joinDebate", "leaveDebate", "sendDebateMessage", "voteInPoll", "pollId", "option", "joinDiscussion", "leaveDiscussion", "notify<PERSON>ew<PERSON>omment", "commentId", "content", "startTyping", "stopTyping", "value", "getDebateParticipantCount", "getDebateMessages", "getTypingUsers", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/contexts/WebSocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, useRef } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\n\nconst WebSocketContext = createContext();\n\nexport const useWebSocket = () => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n\nexport const WebSocketProvider = ({ children }) => {\n  const { user, token } = useAuth();\n  const [socket, setSocket] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [activeDebates, setActiveDebates] = useState({});\n  const [debateMessages, setDebateMessages] = useState({});\n  const [notifications, setNotifications] = useState([]);\n  const [typingUsers, setTypingUsers] = useState({});\n  \n  const socketRef = useRef(null);\n  const typingTimeoutRef = useRef({});\n\n  useEffect(() => {\n    if (user && token) {\n      // Initialize socket connection\n      const newSocket = io(process.env.REACT_APP_API_URL || 'http://localhost:3000', {\n        autoConnect: true,\n        reconnection: true,\n        reconnectionAttempts: 5,\n        reconnectionDelay: 1000,\n      });\n\n      socketRef.current = newSocket;\n      setSocket(newSocket);\n\n      // Connection event handlers\n      newSocket.on('connect', () => {\n        console.log('🔌 Connected to WebSocket server');\n        setIsConnected(true);\n        \n        // Authenticate with the server\n        newSocket.emit('authenticate', token);\n      });\n\n      newSocket.on('disconnect', () => {\n        console.log('🔌 Disconnected from WebSocket server');\n        setIsConnected(false);\n      });\n\n      newSocket.on('authenticated', (data) => {\n        if (data.success) {\n          console.log('✅ WebSocket authentication successful');\n        } else {\n          console.error('❌ WebSocket authentication failed:', data.error);\n        }\n      });\n\n      // Debate event handlers\n      newSocket.on('user-joined-debate', (data) => {\n        console.log(`👥 User ${data.username} joined debate`);\n        addNotification({\n          type: 'info',\n          message: `${data.username} joined the debate`,\n          timestamp: data.timestamp\n        });\n      });\n\n      newSocket.on('user-left-debate', (data) => {\n        console.log(`👋 User ${data.username} left debate`);\n        addNotification({\n          type: 'info',\n          message: `${data.username} left the debate`,\n          timestamp: data.timestamp\n        });\n      });\n\n      newSocket.on('participant-count-updated', (data) => {\n        setActiveDebates(prev => ({\n          ...prev,\n          [data.debateId]: data.count\n        }));\n      });\n\n      newSocket.on('debate-chat-message', (message) => {\n        setDebateMessages(prev => ({\n          ...prev,\n          [message.debateId]: [\n            ...(prev[message.debateId] || []),\n            message\n          ]\n        }));\n      });\n\n      newSocket.on('debate-status-updated', (data) => {\n        addNotification({\n          type: 'info',\n          message: `Debate status updated to: ${data.status}`,\n          timestamp: data.timestamp\n        });\n      });\n\n      // Discussion event handlers\n      newSocket.on('new-comment', (data) => {\n        addNotification({\n          type: 'comment',\n          message: `${data.username} added a new comment`,\n          threadId: data.threadId,\n          timestamp: data.timestamp\n        });\n      });\n\n      newSocket.on('user-typing', (data) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [data.threadId]: {\n            ...prev[data.threadId],\n            [data.userId]: data.username\n          }\n        }));\n\n        // Clear typing indicator after 3 seconds\n        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {\n          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);\n        }\n        \n        typingTimeoutRef.current[`${data.threadId}-${data.userId}`] = setTimeout(() => {\n          setTypingUsers(prev => {\n            const newState = { ...prev };\n            if (newState[data.threadId]) {\n              delete newState[data.threadId][data.userId];\n              if (Object.keys(newState[data.threadId]).length === 0) {\n                delete newState[data.threadId];\n              }\n            }\n            return newState;\n          });\n        }, 3000);\n      });\n\n      newSocket.on('user-stopped-typing', (data) => {\n        setTypingUsers(prev => {\n          const newState = { ...prev };\n          if (newState[data.threadId]) {\n            delete newState[data.threadId][data.userId];\n            if (Object.keys(newState[data.threadId]).length === 0) {\n              delete newState[data.threadId];\n            }\n          }\n          return newState;\n        });\n\n        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {\n          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);\n          delete typingTimeoutRef.current[`${data.threadId}-${data.userId}`];\n        }\n      });\n\n      // General notification handler\n      newSocket.on('notification', (notification) => {\n        addNotification(notification);\n      });\n\n      newSocket.on('error', (error) => {\n        console.error('WebSocket error:', error);\n        addNotification({\n          type: 'error',\n          message: error.message || 'WebSocket error occurred',\n          timestamp: new Date().toISOString()\n        });\n      });\n\n      return () => {\n        newSocket.disconnect();\n        setSocket(null);\n        setIsConnected(false);\n        \n        // Clear all timeouts\n        Object.values(typingTimeoutRef.current).forEach(timeout => {\n          clearTimeout(timeout);\n        });\n        typingTimeoutRef.current = {};\n      };\n    }\n  }, [user, token]);\n\n  const addNotification = (notification) => {\n    const newNotification = {\n      id: Date.now().toString(),\n      ...notification\n    };\n    \n    setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep last 50 notifications\n    \n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  };\n\n  const removeNotification = (id) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  // Debate functions\n  const joinDebate = (debateId) => {\n    if (socket && isConnected) {\n      socket.emit('join-debate', debateId);\n    }\n  };\n\n  const leaveDebate = (debateId) => {\n    if (socket && isConnected) {\n      socket.emit('leave-debate', debateId);\n    }\n  };\n\n  const sendDebateMessage = (debateId, message) => {\n    if (socket && isConnected) {\n      socket.emit('debate-chat-message', { debateId, message });\n    }\n  };\n\n  const voteInPoll = (debateId, pollId, option) => {\n    if (socket && isConnected) {\n      socket.emit('debate-poll-vote', { debateId, pollId, option });\n    }\n  };\n\n  // Discussion functions\n  const joinDiscussion = (threadId) => {\n    if (socket && isConnected) {\n      socket.emit('join-discussion', threadId);\n    }\n  };\n\n  const leaveDiscussion = (threadId) => {\n    if (socket && isConnected) {\n      socket.emit('leave-discussion', threadId);\n    }\n  };\n\n  const notifyNewComment = (threadId, commentId, content) => {\n    if (socket && isConnected) {\n      socket.emit('new-comment', { threadId, commentId, content });\n    }\n  };\n\n  const startTyping = (threadId) => {\n    if (socket && isConnected) {\n      socket.emit('typing-start', { threadId });\n    }\n  };\n\n  const stopTyping = (threadId) => {\n    if (socket && isConnected) {\n      socket.emit('typing-stop', { threadId });\n    }\n  };\n\n  const value = {\n    socket,\n    isConnected,\n    activeDebates,\n    debateMessages,\n    notifications,\n    typingUsers,\n    \n    // Actions\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    voteInPoll,\n    joinDiscussion,\n    leaveDiscussion,\n    notifyNewComment,\n    startTyping,\n    stopTyping,\n    removeNotification,\n    \n    // Utilities\n    getDebateParticipantCount: (debateId) => activeDebates[debateId] || 0,\n    getDebateMessages: (debateId) => debateMessages[debateId] || [],\n    getTypingUsers: (threadId) => typingUsers[threadId] || {}\n  };\n\n  return (\n    <WebSocketContext.Provider value={value}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\nexport default WebSocketContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACrF,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,gBAAgB,gBAAGT,aAAa,CAAC,CAAC;AAExC,OAAO,MAAMU,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,OAAO,GAAGX,UAAU,CAACQ,gBAAgB,CAAC;EAC5C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,YAAY;AAQzB,OAAO,MAAMI,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACjD,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACjC,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAElD,MAAM4B,SAAS,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4B,gBAAgB,GAAG5B,MAAM,CAAC,CAAC,CAAC,CAAC;EAEnCF,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,IAAIC,KAAK,EAAE;MACjB;MACA,MAAMe,SAAS,GAAG5B,EAAE,CAAC6B,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QAC7EC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,CAAC;QACvBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEFT,SAAS,CAACU,OAAO,GAAGR,SAAS;MAC7Bb,SAAS,CAACa,SAAS,CAAC;;MAEpB;MACAA,SAAS,CAACS,EAAE,CAAC,SAAS,EAAE,MAAM;QAC5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CtB,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACAW,SAAS,CAACY,IAAI,CAAC,cAAc,EAAE3B,KAAK,CAAC;MACvC,CAAC,CAAC;MAEFe,SAAS,CAACS,EAAE,CAAC,YAAY,EAAE,MAAM;QAC/BC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDtB,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,CAAC;MAEFW,SAAS,CAACS,EAAE,CAAC,eAAe,EAAGI,IAAI,IAAK;QACtC,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACtD,CAAC,MAAM;UACLD,OAAO,CAACK,KAAK,CAAC,oCAAoC,EAAEF,IAAI,CAACE,KAAK,CAAC;QACjE;MACF,CAAC,CAAC;;MAEF;MACAf,SAAS,CAACS,EAAE,CAAC,oBAAoB,EAAGI,IAAI,IAAK;QAC3CH,OAAO,CAACC,GAAG,CAAC,WAAWE,IAAI,CAACG,QAAQ,gBAAgB,CAAC;QACrDC,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,GAAGN,IAAI,CAACG,QAAQ,oBAAoB;UAC7CI,SAAS,EAAEP,IAAI,CAACO;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpB,SAAS,CAACS,EAAE,CAAC,kBAAkB,EAAGI,IAAI,IAAK;QACzCH,OAAO,CAACC,GAAG,CAAC,WAAWE,IAAI,CAACG,QAAQ,cAAc,CAAC;QACnDC,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,GAAGN,IAAI,CAACG,QAAQ,kBAAkB;UAC3CI,SAAS,EAAEP,IAAI,CAACO;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpB,SAAS,CAACS,EAAE,CAAC,2BAA2B,EAAGI,IAAI,IAAK;QAClDtB,gBAAgB,CAAC8B,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACR,IAAI,CAACS,QAAQ,GAAGT,IAAI,CAACU;QACxB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFvB,SAAS,CAACS,EAAE,CAAC,qBAAqB,EAAGU,OAAO,IAAK;QAC/C1B,iBAAiB,CAAC4B,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP,CAACF,OAAO,CAACG,QAAQ,GAAG,CAClB,IAAID,IAAI,CAACF,OAAO,CAACG,QAAQ,CAAC,IAAI,EAAE,CAAC,EACjCH,OAAO;QAEX,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFnB,SAAS,CAACS,EAAE,CAAC,uBAAuB,EAAGI,IAAI,IAAK;QAC9CI,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,6BAA6BN,IAAI,CAACW,MAAM,EAAE;UACnDJ,SAAS,EAAEP,IAAI,CAACO;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACApB,SAAS,CAACS,EAAE,CAAC,aAAa,EAAGI,IAAI,IAAK;QACpCI,eAAe,CAAC;UACdC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,GAAGN,IAAI,CAACG,QAAQ,sBAAsB;UAC/CS,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;UACvBL,SAAS,EAAEP,IAAI,CAACO;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpB,SAAS,CAACS,EAAE,CAAC,aAAa,EAAGI,IAAI,IAAK;QACpChB,cAAc,CAACwB,IAAI,KAAK;UACtB,GAAGA,IAAI;UACP,CAACR,IAAI,CAACY,QAAQ,GAAG;YACf,GAAGJ,IAAI,CAACR,IAAI,CAACY,QAAQ,CAAC;YACtB,CAACZ,IAAI,CAACa,MAAM,GAAGb,IAAI,CAACG;UACtB;QACF,CAAC,CAAC,CAAC;;QAEH;QACA,IAAIjB,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC,EAAE;UAC/DC,YAAY,CAAC5B,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC,CAAC;QAC3E;QAEA3B,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC,GAAGE,UAAU,CAAC,MAAM;UAC7E/B,cAAc,CAACwB,IAAI,IAAI;YACrB,MAAMQ,QAAQ,GAAG;cAAE,GAAGR;YAAK,CAAC;YAC5B,IAAIQ,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,EAAE;cAC3B,OAAOI,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,CAACZ,IAAI,CAACa,MAAM,CAAC;cAC3C,IAAII,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,CAAC,CAACO,MAAM,KAAK,CAAC,EAAE;gBACrD,OAAOH,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC;cAChC;YACF;YACA,OAAOI,QAAQ;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;MAEF7B,SAAS,CAACS,EAAE,CAAC,qBAAqB,EAAGI,IAAI,IAAK;QAC5ChB,cAAc,CAACwB,IAAI,IAAI;UACrB,MAAMQ,QAAQ,GAAG;YAAE,GAAGR;UAAK,CAAC;UAC5B,IAAIQ,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,EAAE;YAC3B,OAAOI,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,CAACZ,IAAI,CAACa,MAAM,CAAC;YAC3C,IAAII,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC,CAAC,CAACO,MAAM,KAAK,CAAC,EAAE;cACrD,OAAOH,QAAQ,CAAChB,IAAI,CAACY,QAAQ,CAAC;YAChC;UACF;UACA,OAAOI,QAAQ;QACjB,CAAC,CAAC;QAEF,IAAI9B,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC,EAAE;UAC/DC,YAAY,CAAC5B,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC,CAAC;UACzE,OAAO3B,gBAAgB,CAACS,OAAO,CAAC,GAAGK,IAAI,CAACY,QAAQ,IAAIZ,IAAI,CAACa,MAAM,EAAE,CAAC;QACpE;MACF,CAAC,CAAC;;MAEF;MACA1B,SAAS,CAACS,EAAE,CAAC,cAAc,EAAGwB,YAAY,IAAK;QAC7ChB,eAAe,CAACgB,YAAY,CAAC;MAC/B,CAAC,CAAC;MAEFjC,SAAS,CAACS,EAAE,CAAC,OAAO,EAAGM,KAAK,IAAK;QAC/BL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCE,eAAe,CAAC;UACdC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAEJ,KAAK,CAACI,OAAO,IAAI,0BAA0B;UACpDC,SAAS,EAAE,IAAIc,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAO,MAAM;QACXnC,SAAS,CAACoC,UAAU,CAAC,CAAC;QACtBjD,SAAS,CAAC,IAAI,CAAC;QACfE,cAAc,CAAC,KAAK,CAAC;;QAErB;QACAyC,MAAM,CAACO,MAAM,CAACtC,gBAAgB,CAACS,OAAO,CAAC,CAAC8B,OAAO,CAACC,OAAO,IAAI;UACzDZ,YAAY,CAACY,OAAO,CAAC;QACvB,CAAC,CAAC;QACFxC,gBAAgB,CAACS,OAAO,GAAG,CAAC,CAAC;MAC/B,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,IAAI,EAAEC,KAAK,CAAC,CAAC;EAEjB,MAAMgC,eAAe,GAAIgB,YAAY,IAAK;IACxC,MAAMO,eAAe,GAAG;MACtBC,EAAE,EAAEP,IAAI,CAACQ,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB,GAAGV;IACL,CAAC;IAEDtC,gBAAgB,CAAC0B,IAAI,IAAI,CAACmB,eAAe,EAAE,GAAGnB,IAAI,CAAC,CAACuB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnE;IACA,IAAIX,YAAY,CAACf,IAAI,KAAK,OAAO,EAAE;MACjCU,UAAU,CAAC,MAAM;QACfiB,kBAAkB,CAACL,eAAe,CAACC,EAAE,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAIJ,EAAE,IAAK;IACjC9C,gBAAgB,CAAC0B,IAAI,IAAIA,IAAI,CAACyB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKA,EAAE,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAMO,UAAU,GAAI1B,QAAQ,IAAK;IAC/B,IAAIpC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,aAAa,EAAEU,QAAQ,CAAC;IACtC;EACF,CAAC;EAED,MAAM2B,WAAW,GAAI3B,QAAQ,IAAK;IAChC,IAAIpC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,cAAc,EAAEU,QAAQ,CAAC;IACvC;EACF,CAAC;EAED,MAAM4B,iBAAiB,GAAGA,CAAC5B,QAAQ,EAAEH,OAAO,KAAK;IAC/C,IAAIjC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,qBAAqB,EAAE;QAAEU,QAAQ;QAAEH;MAAQ,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMgC,UAAU,GAAGA,CAAC7B,QAAQ,EAAE8B,MAAM,EAAEC,MAAM,KAAK;IAC/C,IAAInE,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,kBAAkB,EAAE;QAAEU,QAAQ;QAAE8B,MAAM;QAAEC;MAAO,CAAC,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAI7B,QAAQ,IAAK;IACnC,IAAIvC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,iBAAiB,EAAEa,QAAQ,CAAC;IAC1C;EACF,CAAC;EAED,MAAM8B,eAAe,GAAI9B,QAAQ,IAAK;IACpC,IAAIvC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,kBAAkB,EAAEa,QAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAM+B,gBAAgB,GAAGA,CAAC/B,QAAQ,EAAEgC,SAAS,EAAEC,OAAO,KAAK;IACzD,IAAIxE,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,aAAa,EAAE;QAAEa,QAAQ;QAAEgC,SAAS;QAAEC;MAAQ,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,MAAMC,WAAW,GAAIlC,QAAQ,IAAK;IAChC,IAAIvC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,cAAc,EAAE;QAAEa;MAAS,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMmC,UAAU,GAAInC,QAAQ,IAAK;IAC/B,IAAIvC,MAAM,IAAIE,WAAW,EAAE;MACzBF,MAAM,CAAC0B,IAAI,CAAC,aAAa,EAAE;QAAEa;MAAS,CAAC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMoC,KAAK,GAAG;IACZ3E,MAAM;IACNE,WAAW;IACXE,aAAa;IACbE,cAAc;IACdE,aAAa;IACbE,WAAW;IAEX;IACAoD,UAAU;IACVC,WAAW;IACXC,iBAAiB;IACjBC,UAAU;IACVG,cAAc;IACdC,eAAe;IACfC,gBAAgB;IAChBG,WAAW;IACXC,UAAU;IACVf,kBAAkB;IAElB;IACAiB,yBAAyB,EAAGxC,QAAQ,IAAKhC,aAAa,CAACgC,QAAQ,CAAC,IAAI,CAAC;IACrEyC,iBAAiB,EAAGzC,QAAQ,IAAK9B,cAAc,CAAC8B,QAAQ,CAAC,IAAI,EAAE;IAC/D0C,cAAc,EAAGvC,QAAQ,IAAK7B,WAAW,CAAC6B,QAAQ,CAAC,IAAI,CAAC;EAC1D,CAAC;EAED,oBACElD,OAAA,CAACC,gBAAgB,CAACyF,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAA/E,QAAA,EACrCA;EAAQ;IAAAoF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAACtF,GAAA,CA1RWF,iBAAiB;EAAA,QACJR,OAAO;AAAA;AAAAiG,EAAA,GADpBzF,iBAAiB;AA4R9B,eAAeL,gBAAgB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}