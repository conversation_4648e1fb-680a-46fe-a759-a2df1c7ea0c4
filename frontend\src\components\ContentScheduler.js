import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const ContentScheduler = () => {
  const [scheduledPosts, setScheduledPosts] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [postContent, setPostContent] = useState('');
  const [postType, setPostType] = useState('text');
  const [hashtags, setHashtags] = useState('');
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(false);

  const { user } = useAuth();

  // Mock scheduled posts data
  useEffect(() => {
    setScheduledPosts([
      {
        id: 1,
        content: "Excited to share my thoughts on the upcoming climate summit. The decisions made there will shape our planet's future for generations to come. #ClimateAction #Sustainability",
        scheduledFor: '2024-06-04T10:00:00Z',
        status: 'scheduled',
        postType: 'text',
        hashtags: ['ClimateAction', 'Sustainability'],
        topics: ['Climate Change'],
        estimatedReach: 1250
      },
      {
        id: 2,
        content: "Healthcare reform discussion happening live at 3 PM EST. Join us to discuss universal healthcare options and their impact on American families.",
        scheduledFor: '2024-06-04T15:00:00Z',
        status: 'scheduled',
        postType: 'text',
        hashtags: ['Healthcare', 'Reform'],
        topics: ['Healthcare'],
        estimatedReach: 890
      },
      {
        id: 3,
        content: "Weekly economic update: inflation trends and their impact on working families. Data-driven analysis with actionable insights.",
        scheduledFor: '2024-06-05T09:00:00Z',
        status: 'scheduled',
        postType: 'article',
        hashtags: ['Economy', 'Inflation'],
        topics: ['Economic Policy'],
        estimatedReach: 2100
      }
    ]);
  }, []);

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      relative: getRelativeTime(date)
    };
  };

  const getRelativeTime = (date) => {
    const now = new Date();
    const diff = date - now;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) return `in ${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0) return `in ${hours} hour${hours > 1 ? 's' : ''}`;
    return 'soon';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'published': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSchedulePost = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newPost = {
        id: Date.now(),
        content: postContent,
        scheduledFor: `${selectedDate}T${selectedTime}:00Z`,
        status: 'scheduled',
        postType,
        hashtags: hashtags.split(',').map(h => h.trim()).filter(h => h),
        topics,
        estimatedReach: Math.floor(Math.random() * 2000) + 500
      };

      setScheduledPosts(prev => [...prev, newPost]);
      
      // Reset form
      setPostContent('');
      setSelectedDate('');
      setSelectedTime('');
      setHashtags('');
      setTopics([]);
      setShowCreateForm(false);
      
    } catch (error) {
      console.error('Error scheduling post:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteScheduledPost = (postId) => {
    setScheduledPosts(prev => prev.filter(post => post.id !== postId));
  };

  const duplicatePost = (post) => {
    const duplicated = {
      ...post,
      id: Date.now(),
      content: post.content + ' (Copy)',
      scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    setScheduledPosts(prev => [...prev, duplicated]);
  };

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-secondary-900 flex items-center gap-2">
              ⏰ Content Scheduler
            </h2>
            <p className="text-secondary-600 mt-1">
              Schedule your political content for optimal engagement
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn btn-primary"
          >
            📅 Schedule New Post
          </button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-primary-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-primary-600">{scheduledPosts.length}</div>
            <div className="text-sm text-primary-700">Scheduled Posts</div>
          </div>
          <div className="bg-success-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-success-600">
              {scheduledPosts.filter(p => p.status === 'scheduled').length}
            </div>
            <div className="text-sm text-success-700">Pending</div>
          </div>
          <div className="bg-warning-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-warning-600">
              {scheduledPosts.reduce((sum, post) => sum + post.estimatedReach, 0).toLocaleString()}
            </div>
            <div className="text-sm text-warning-700">Est. Total Reach</div>
          </div>
          <div className="bg-secondary-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-secondary-600">85%</div>
            <div className="text-sm text-secondary-700">Success Rate</div>
          </div>
        </div>
      </div>

      {/* Create New Scheduled Post Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-secondary-900">Schedule New Post</h3>
              <button
                onClick={() => setShowCreateForm(false)}
                className="text-secondary-400 hover:text-secondary-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleSchedulePost} className="space-y-6">
              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Post Content
                </label>
                <textarea
                  value={postContent}
                  onChange={(e) => setPostContent(e.target.value)}
                  placeholder="What's happening in politics?"
                  className="form-textarea w-full"
                  rows={4}
                  required
                />
                <div className="text-xs text-secondary-500 mt-1">
                  {postContent.length}/2000 characters
                </div>
              </div>

              {/* Schedule Date & Time */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Date
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="form-input w-full"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Time
                  </label>
                  <input
                    type="time"
                    value={selectedTime}
                    onChange={(e) => setSelectedTime(e.target.value)}
                    className="form-input w-full"
                    required
                  />
                </div>
              </div>

              {/* Post Type */}
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Post Type
                </label>
                <select
                  value={postType}
                  onChange={(e) => setPostType(e.target.value)}
                  className="form-select w-full"
                >
                  <option value="text">Text Post</option>
                  <option value="image">Image Post</option>
                  <option value="video">Video Post</option>
                  <option value="poll">Poll</option>
                  <option value="article">Article Share</option>
                </select>
              </div>

              {/* Hashtags */}
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Hashtags (comma-separated)
                </label>
                <input
                  type="text"
                  value={hashtags}
                  onChange={(e) => setHashtags(e.target.value)}
                  placeholder="e.g., politics, democracy, policy"
                  className="form-input w-full"
                />
              </div>

              {/* Optimal Timing Suggestion */}
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <span className="text-primary-600 text-lg">💡</span>
                  <div>
                    <h4 className="font-medium text-primary-900">Optimal Timing Suggestion</h4>
                    <p className="text-sm text-primary-700 mt-1">
                      Based on your audience, the best times to post are:
                      <strong> 9:00 AM, 1:00 PM, and 7:00 PM EST</strong>
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? 'Scheduling...' : 'Schedule Post'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Scheduled Posts List */}
      <div className="card">
        <div className="p-6 border-b border-secondary-200">
          <h3 className="text-lg font-semibold text-secondary-900">Scheduled Posts</h3>
        </div>
        
        <div className="divide-y divide-secondary-200">
          {scheduledPosts.map(post => {
            const dateTime = formatDateTime(post.scheduledFor);
            
            return (
              <div key={post.id} className="p-6 hover:bg-secondary-25 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(post.status)}`}>
                        {post.status}
                      </span>
                      <span className="text-sm text-secondary-500">
                        📅 {dateTime.date} at {dateTime.time} ({dateTime.relative})
                      </span>
                      <span className="text-sm text-secondary-500">
                        📊 ~{post.estimatedReach.toLocaleString()} reach
                      </span>
                    </div>
                    
                    <p className="text-secondary-900 mb-3 line-clamp-3">
                      {post.content}
                    </p>
                    
                    <div className="flex items-center gap-4">
                      {post.hashtags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {post.hashtags.map(hashtag => (
                            <span key={hashtag} className="text-xs text-primary-600">
                              #{hashtag}
                            </span>
                          ))}
                        </div>
                      )}
                      
                      {post.topics.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {post.topics.map(topic => (
                            <span key={topic} className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
                              {topic}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => duplicatePost(post)}
                      className="btn btn-ghost btn-sm"
                      title="Duplicate"
                    >
                      📋
                    </button>
                    <button
                      className="btn btn-ghost btn-sm"
                      title="Edit"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={() => deleteScheduledPost(post.id)}
                      className="btn btn-ghost btn-sm text-error-600 hover:bg-error-50"
                      title="Delete"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {scheduledPosts.length === 0 && (
          <div className="p-12 text-center">
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No scheduled posts</h3>
            <p className="text-secondary-600 mb-6">
              Schedule your first post to maintain consistent political engagement
            </p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="btn btn-primary"
            >
              Schedule Your First Post
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentScheduler;
