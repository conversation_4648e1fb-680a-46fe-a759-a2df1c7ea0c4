{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\SocialFeed.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport PostCard from '../components/PostCard';\nimport CreatePost from '../components/CreatePost';\nimport TrendingSidebar from '../components/TrendingSidebar';\nimport ProfessionalHeader from '../components/ProfessionalHeader';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport ContentScheduler from '../components/ContentScheduler';\nimport AnalyticsDashboard from '../components/AnalyticsDashboard';\nimport '../styles/design-system.css';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SocialFeed = () => {\n  _s();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all'); // 'all', 'following', 'trending'\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [viewMode, setViewMode] = useState('feed'); // 'feed', 'analytics', 'scheduler'\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);\n  const [selectedTopics, setSelectedTopics] = useState([]);\n  const [dateRange, setDateRange] = useState('all');\n  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'popular', 'engagement'\n\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    socket,\n    isConnected\n  } = useWebSocket();\n\n  // Fetch posts\n  const fetchPosts = useCallback(async (pageNum = 1, filterType = filter, reset = false) => {\n    try {\n      if (pageNum === 1) setLoading(true);\n      const response = await axios.get('/api/feed', {\n        params: {\n          page: pageNum,\n          limit: 20,\n          filter: filterType\n        }\n      });\n      const newPosts = response.data.posts;\n      if (reset || pageNum === 1) {\n        setPosts(newPosts);\n      } else {\n        setPosts(prev => [...prev, ...newPosts]);\n      }\n      setHasMore(newPosts.length === 20);\n      setPage(pageNum);\n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      setError('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [filter]);\n\n  // Initial load\n  useEffect(() => {\n    fetchPosts(1, filter, true);\n  }, [filter]);\n\n  // WebSocket listeners for real-time updates\n  useEffect(() => {\n    if (!socket) return;\n    const handleNewPost = post => {\n      setPosts(prev => [post, ...prev]);\n    };\n    const handlePostUpdate = updatedPost => {\n      setPosts(prev => prev.map(post => post.id === updatedPost.id ? {\n        ...post,\n        ...updatedPost\n      } : post));\n    };\n    const handlePostDelete = postId => {\n      setPosts(prev => prev.filter(post => post.id !== postId));\n    };\n    socket.on('new-post', handleNewPost);\n    socket.on('post-updated', handlePostUpdate);\n    socket.on('post-deleted', handlePostDelete);\n    return () => {\n      socket.off('new-post');\n      socket.off('post-updated');\n      socket.off('post-deleted');\n    };\n  }, [socket]);\n\n  // Handle filter change\n  const handleFilterChange = newFilter => {\n    setFilter(newFilter);\n    setPage(1);\n    setPosts([]);\n  };\n\n  // Handle refresh\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchPosts(1, filter, true);\n  };\n\n  // Load more posts\n  const loadMore = () => {\n    if (!loading && hasMore) {\n      fetchPosts(page + 1, filter, false);\n    }\n  };\n\n  // Handle new post created\n  const handlePostCreated = newPost => {\n    setPosts(prev => [newPost, ...prev]);\n  };\n\n  // Handle post vote\n  const handlePostVote = async (postId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/vote`, {\n        vote_type: voteType\n      });\n      setPosts(prev => prev.map(post => post.id === postId ? {\n        ...post,\n        upvotes: response.data.upvotes,\n        downvotes: response.data.downvotes,\n        user_vote: response.data.user_vote\n      } : post));\n    } catch (error) {\n      console.error('Error voting on post:', error);\n    }\n  };\n\n  // Handle post reaction\n  const handlePostReaction = async (postId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/react`, {\n        reaction_type: reactionType\n      });\n      setPosts(prev => prev.map(post => post.id === postId ? {\n        ...post,\n        reactions: response.data.reactions\n      } : post));\n    } catch (error) {\n      console.error('Error reacting to post:', error);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Join the Political Conversation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Sign in to participate in political discussions and share your views\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\",\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Political Feed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: isConnected ? 'Live' : 'Offline'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n              children: [{\n                key: 'all',\n                label: '🌍 All Posts'\n              }, {\n                key: 'following',\n                label: '👥 Following'\n              }, {\n                key: 'trending',\n                label: '🔥 Trending'\n              }].map(({\n                key,\n                label\n              }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleFilterChange(key),\n                className: `flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${filter === key ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: label\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CreatePost, {\n            onPostCreated: handlePostCreated\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRefresh,\n              disabled: refreshing,\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2\",\n              children: refreshing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), \"Refreshing...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"\\uD83D\\uDD04 Refresh Feed\"\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: loading && posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-6 animate-pulse\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gray-300 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-4 bg-gray-300 rounded w-1/4 mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-3 bg-gray-300 rounded w-1/6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-3/4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-sm p-12 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl mb-4\",\n                children: \"\\uD83D\\uDCED\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"No posts yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: filter === 'following' ? \"Follow some users to see their posts here\" : \"Be the first to start a political discussion!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), filter === 'following' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleFilterChange('all'),\n                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\",\n                children: \"View All Posts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [posts.map(post => /*#__PURE__*/_jsxDEV(PostCard, {\n                post: post,\n                onVote: handlePostVote,\n                onReaction: handlePostReaction\n              }, post.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this)), hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center py-6\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: loadMore,\n                  disabled: loading,\n                  className: \"bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center gap-2\",\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 29\n                    }, this), \"Loading...\"]\n                  }, void 0, true) : 'Load More Posts'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-red-400\",\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-red-800\",\n                  children: \"Error\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-red-700 mt-1\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(TrendingSidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(SocialFeed, \"HAzxo4JCe5TCTZLoEsFpc4/bEHE=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = SocialFeed;\nexport default SocialFeed;\nvar _c;\n$RefreshReg$(_c, \"SocialFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "useWebSocket", "PostCard", "CreatePost", "TrendingSidebar", "<PERSON><PERSON><PERSON><PERSON>", "AdvancedFilters", "ContentScheduler", "AnalyticsDashboard", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SocialFeed", "_s", "posts", "setPosts", "loading", "setLoading", "error", "setError", "filter", "setFilter", "page", "setPage", "hasMore", "setHasMore", "refreshing", "setRefreshing", "viewMode", "setViewMode", "showAdvancedFilters", "setShowAdvancedFilters", "selectedTopics", "setSelectedTopics", "date<PERSON><PERSON><PERSON>", "setDateRange", "sortBy", "setSortBy", "user", "isAuthenticated", "socket", "isConnected", "fetchPosts", "pageNum", "filterType", "reset", "response", "get", "params", "limit", "newPosts", "data", "prev", "length", "console", "handleNewPost", "post", "handlePostUpdate", "updatedPost", "map", "id", "handlePostDelete", "postId", "on", "off", "handleFilterChange", "newFilter", "handleRefresh", "loadMore", "handlePostCreated", "newPost", "handlePostVote", "voteType", "vote_type", "upvotes", "downvotes", "user_vote", "handlePostReaction", "reactionType", "reaction_type", "reactions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "key", "label", "onClick", "onPostCreated", "disabled", "Array", "_", "i", "onVote", "onReaction", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/SocialFeed.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport PostCard from '../components/PostCard';\nimport CreatePost from '../components/CreatePost';\nimport TrendingSidebar from '../components/TrendingSidebar';\nimport ProfessionalHeader from '../components/ProfessionalHeader';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport ContentScheduler from '../components/ContentScheduler';\nimport AnalyticsDashboard from '../components/AnalyticsDashboard';\nimport '../styles/design-system.css';\nimport axios from 'axios';\n\nconst SocialFeed = () => {\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all'); // 'all', 'following', 'trending'\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [viewMode, setViewMode] = useState('feed'); // 'feed', 'analytics', 'scheduler'\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);\n  const [selectedTopics, setSelectedTopics] = useState([]);\n  const [dateRange, setDateRange] = useState('all');\n  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'popular', 'engagement'\n\n  const { user, isAuthenticated } = useAuth();\n  const { socket, isConnected } = useWebSocket();\n\n  // Fetch posts\n  const fetchPosts = useCallback(async (pageNum = 1, filterType = filter, reset = false) => {\n    try {\n      if (pageNum === 1) setLoading(true);\n      \n      const response = await axios.get('/api/feed', {\n        params: {\n          page: pageNum,\n          limit: 20,\n          filter: filterType\n        }\n      });\n\n      const newPosts = response.data.posts;\n      \n      if (reset || pageNum === 1) {\n        setPosts(newPosts);\n      } else {\n        setPosts(prev => [...prev, ...newPosts]);\n      }\n\n      setHasMore(newPosts.length === 20);\n      setPage(pageNum);\n      \n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      setError('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [filter]);\n\n  // Initial load\n  useEffect(() => {\n    fetchPosts(1, filter, true);\n  }, [filter]);\n\n  // WebSocket listeners for real-time updates\n  useEffect(() => {\n    if (!socket) return;\n\n    const handleNewPost = (post) => {\n      setPosts(prev => [post, ...prev]);\n    };\n\n    const handlePostUpdate = (updatedPost) => {\n      setPosts(prev => prev.map(post => \n        post.id === updatedPost.id ? { ...post, ...updatedPost } : post\n      ));\n    };\n\n    const handlePostDelete = (postId) => {\n      setPosts(prev => prev.filter(post => post.id !== postId));\n    };\n\n    socket.on('new-post', handleNewPost);\n    socket.on('post-updated', handlePostUpdate);\n    socket.on('post-deleted', handlePostDelete);\n\n    return () => {\n      socket.off('new-post');\n      socket.off('post-updated');\n      socket.off('post-deleted');\n    };\n  }, [socket]);\n\n  // Handle filter change\n  const handleFilterChange = (newFilter) => {\n    setFilter(newFilter);\n    setPage(1);\n    setPosts([]);\n  };\n\n  // Handle refresh\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchPosts(1, filter, true);\n  };\n\n  // Load more posts\n  const loadMore = () => {\n    if (!loading && hasMore) {\n      fetchPosts(page + 1, filter, false);\n    }\n  };\n\n  // Handle new post created\n  const handlePostCreated = (newPost) => {\n    setPosts(prev => [newPost, ...prev]);\n  };\n\n  // Handle post vote\n  const handlePostVote = async (postId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/vote`, {\n        vote_type: voteType\n      });\n\n      setPosts(prev => prev.map(post => \n        post.id === postId \n          ? { \n              ...post, \n              upvotes: response.data.upvotes,\n              downvotes: response.data.downvotes,\n              user_vote: response.data.user_vote\n            }\n          : post\n      ));\n\n    } catch (error) {\n      console.error('Error voting on post:', error);\n    }\n  };\n\n  // Handle post reaction\n  const handlePostReaction = async (postId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/react`, {\n        reaction_type: reactionType\n      });\n\n      setPosts(prev => prev.map(post => \n        post.id === postId \n          ? { \n              ...post, \n              reactions: response.data.reactions\n            }\n          : post\n      ));\n\n    } catch (error) {\n      console.error('Error reacting to post:', error);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Join the Political Conversation</h2>\n          <p className=\"text-gray-600 mb-6\">Sign in to participate in political discussions and share your views</p>\n          <a href=\"/login\" className=\"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\">\n            Sign In\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          \n          {/* Main Feed */}\n          <div className=\"lg:col-span-3\">\n            {/* Header */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">Political Feed</h1>\n                <div className=\"flex items-center gap-2\">\n                  <span className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></span>\n                  <span className=\"text-sm text-gray-600\">\n                    {isConnected ? 'Live' : 'Offline'}\n                  </span>\n                </div>\n              </div>\n\n              {/* Filter Tabs */}\n              <div className=\"flex space-x-1 bg-gray-100 rounded-lg p-1\">\n                {[\n                  { key: 'all', label: '🌍 All Posts' },\n                  { key: 'following', label: '👥 Following' },\n                  { key: 'trending', label: '🔥 Trending' }\n                ].map(({ key, label }) => (\n                  <button\n                    key={key}\n                    onClick={() => handleFilterChange(key)}\n                    className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                      filter === key\n                        ? 'bg-white text-blue-600 shadow-sm'\n                        : 'text-gray-600 hover:text-gray-900'\n                    }`}\n                  >\n                    {label}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Create Post */}\n            <CreatePost onPostCreated={handlePostCreated} />\n\n            {/* Refresh Button */}\n            <div className=\"flex justify-center mb-6\">\n              <button\n                onClick={handleRefresh}\n                disabled={refreshing}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2\"\n              >\n                {refreshing ? (\n                  <>\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    Refreshing...\n                  </>\n                ) : (\n                  <>\n                    🔄 Refresh Feed\n                  </>\n                )}\n              </button>\n            </div>\n\n            {/* Posts */}\n            <div className=\"space-y-6\">\n              {loading && posts.length === 0 ? (\n                <div className=\"space-y-6\">\n                  {[...Array(5)].map((_, i) => (\n                    <div key={i} className=\"bg-white rounded-lg shadow-sm p-6 animate-pulse\">\n                      <div className=\"flex items-center space-x-3 mb-4\">\n                        <div className=\"w-10 h-10 bg-gray-300 rounded-full\"></div>\n                        <div className=\"flex-1\">\n                          <div className=\"h-4 bg-gray-300 rounded w-1/4 mb-2\"></div>\n                          <div className=\"h-3 bg-gray-300 rounded w-1/6\"></div>\n                        </div>\n                      </div>\n                      <div className=\"space-y-2\">\n                        <div className=\"h-4 bg-gray-300 rounded\"></div>\n                        <div className=\"h-4 bg-gray-300 rounded w-3/4\"></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : posts.length === 0 ? (\n                <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n                  <div className=\"text-6xl mb-4\">📭</div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No posts yet</h3>\n                  <p className=\"text-gray-600 mb-6\">\n                    {filter === 'following' \n                      ? \"Follow some users to see their posts here\"\n                      : \"Be the first to start a political discussion!\"\n                    }\n                  </p>\n                  {filter === 'following' && (\n                    <button\n                      onClick={() => handleFilterChange('all')}\n                      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\"\n                    >\n                      View All Posts\n                    </button>\n                  )}\n                </div>\n              ) : (\n                <>\n                  {posts.map(post => (\n                    <PostCard\n                      key={post.id}\n                      post={post}\n                      onVote={handlePostVote}\n                      onReaction={handlePostReaction}\n                    />\n                  ))}\n\n                  {/* Load More */}\n                  {hasMore && (\n                    <div className=\"flex justify-center py-6\">\n                      <button\n                        onClick={loadMore}\n                        disabled={loading}\n                        className=\"bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center gap-2\"\n                      >\n                        {loading ? (\n                          <>\n                            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                            Loading...\n                          </>\n                        ) : (\n                          'Load More Posts'\n                        )}\n                      </button>\n                    </div>\n                  )}\n                </>\n              )}\n            </div>\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mt-6\">\n                <div className=\"flex\">\n                  <div className=\"text-red-400\">⚠️</div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                    <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <TrendingSidebar />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SocialFeed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAO,6BAA6B;AACpC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAEhD,MAAM;IAAE2C,IAAI;IAAEC;EAAgB,CAAC,GAAGzC,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAE0C,MAAM;IAAEC;EAAY,CAAC,GAAG1C,YAAY,CAAC,CAAC;;EAE9C;EACA,MAAM2C,UAAU,GAAG7C,WAAW,CAAC,OAAO8C,OAAO,GAAG,CAAC,EAAEC,UAAU,GAAGxB,MAAM,EAAEyB,KAAK,GAAG,KAAK,KAAK;IACxF,IAAI;MACF,IAAIF,OAAO,KAAK,CAAC,EAAE1B,UAAU,CAAC,IAAI,CAAC;MAEnC,MAAM6B,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,WAAW,EAAE;QAC5CC,MAAM,EAAE;UACN1B,IAAI,EAAEqB,OAAO;UACbM,KAAK,EAAE,EAAE;UACT7B,MAAM,EAAEwB;QACV;MACF,CAAC,CAAC;MAEF,MAAMM,QAAQ,GAAGJ,QAAQ,CAACK,IAAI,CAACrC,KAAK;MAEpC,IAAI+B,KAAK,IAAIF,OAAO,KAAK,CAAC,EAAE;QAC1B5B,QAAQ,CAACmC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLnC,QAAQ,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGF,QAAQ,CAAC,CAAC;MAC1C;MAEAzB,UAAU,CAACyB,QAAQ,CAACG,MAAM,KAAK,EAAE,CAAC;MAClC9B,OAAO,CAACoB,OAAO,CAAC;IAElB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;MACjBU,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;;EAEZ;EACAxB,SAAS,CAAC,MAAM;IACd8C,UAAU,CAAC,CAAC,EAAEtB,MAAM,EAAE,IAAI,CAAC;EAC7B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,MAAM,EAAE;IAEb,MAAMe,aAAa,GAAIC,IAAI,IAAK;MAC9BzC,QAAQ,CAACqC,IAAI,IAAI,CAACI,IAAI,EAAE,GAAGJ,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,MAAMK,gBAAgB,GAAIC,WAAW,IAAK;MACxC3C,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKF,WAAW,CAACE,EAAE,GAAG;QAAE,GAAGJ,IAAI;QAAE,GAAGE;MAAY,CAAC,GAAGF,IAC7D,CAAC,CAAC;IACJ,CAAC;IAED,MAAMK,gBAAgB,GAAIC,MAAM,IAAK;MACnC/C,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAACoC,IAAI,IAAIA,IAAI,CAACI,EAAE,KAAKE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAEDtB,MAAM,CAACuB,EAAE,CAAC,UAAU,EAAER,aAAa,CAAC;IACpCf,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAEN,gBAAgB,CAAC;IAC3CjB,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAEF,gBAAgB,CAAC;IAE3C,OAAO,MAAM;MACXrB,MAAM,CAACwB,GAAG,CAAC,UAAU,CAAC;MACtBxB,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;MAC1BxB,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMyB,kBAAkB,GAAIC,SAAS,IAAK;IACxC7C,SAAS,CAAC6C,SAAS,CAAC;IACpB3C,OAAO,CAAC,CAAC,CAAC;IACVR,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAMoD,aAAa,GAAGA,CAAA,KAAM;IAC1BxC,aAAa,CAAC,IAAI,CAAC;IACnBe,UAAU,CAAC,CAAC,EAAEtB,MAAM,EAAE,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgD,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACpD,OAAO,IAAIQ,OAAO,EAAE;MACvBkB,UAAU,CAACpB,IAAI,GAAG,CAAC,EAAEF,MAAM,EAAE,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAIC,OAAO,IAAK;IACrCvD,QAAQ,CAACqC,IAAI,IAAI,CAACkB,OAAO,EAAE,GAAGlB,IAAI,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMmB,cAAc,GAAG,MAAAA,CAAOT,MAAM,EAAEU,QAAQ,KAAK;IACjD,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMvC,KAAK,CAACiD,IAAI,CAAC,aAAaM,MAAM,OAAO,EAAE;QAC5DW,SAAS,EAAED;MACb,CAAC,CAAC;MAEFzD,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKE,MAAM,GACd;QACE,GAAGN,IAAI;QACPkB,OAAO,EAAE5B,QAAQ,CAACK,IAAI,CAACuB,OAAO;QAC9BC,SAAS,EAAE7B,QAAQ,CAACK,IAAI,CAACwB,SAAS;QAClCC,SAAS,EAAE9B,QAAQ,CAACK,IAAI,CAACyB;MAC3B,CAAC,GACDpB,IACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAM2D,kBAAkB,GAAG,MAAAA,CAAOf,MAAM,EAAEgB,YAAY,KAAK;IACzD,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMvC,KAAK,CAACiD,IAAI,CAAC,aAAaM,MAAM,QAAQ,EAAE;QAC7DiB,aAAa,EAAED;MACjB,CAAC,CAAC;MAEF/D,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKE,MAAM,GACd;QACE,GAAGN,IAAI;QACPwB,SAAS,EAAElC,QAAQ,CAACK,IAAI,CAAC6B;MAC3B,CAAC,GACDxB,IACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,IAAI,CAACqB,eAAe,EAAE;IACpB,oBACE9B,OAAA;MAAKwE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAIwE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1F7E,OAAA;UAAGwE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAoE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1G7E,OAAA;UAAG8E,IAAI,EAAC,QAAQ;UAACN,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAE3F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCzE,OAAA;MAAKwE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CzE,OAAA;QAAKwE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAGpDzE,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAE5BzE,OAAA;YAAKwE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzE,OAAA;cAAKwE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDzE,OAAA;gBAAIwE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE7E,OAAA;gBAAKwE,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCzE,OAAA;kBAAMwE,SAAS,EAAE,wBAAwBxC,WAAW,GAAG,cAAc,GAAG,YAAY;gBAAG;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/F7E,OAAA;kBAAMwE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpCzC,WAAW,GAAG,MAAM,GAAG;gBAAS;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7E,OAAA;cAAKwE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EACvD,CACC;gBAAEM,GAAG,EAAE,KAAK;gBAAEC,KAAK,EAAE;cAAe,CAAC,EACrC;gBAAED,GAAG,EAAE,WAAW;gBAAEC,KAAK,EAAE;cAAe,CAAC,EAC3C;gBAAED,GAAG,EAAE,UAAU;gBAAEC,KAAK,EAAE;cAAc,CAAC,CAC1C,CAAC9B,GAAG,CAAC,CAAC;gBAAE6B,GAAG;gBAAEC;cAAM,CAAC,kBACnBhF,OAAA;gBAEEiF,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAACuB,GAAG,CAAE;gBACvCP,SAAS,EAAE,qEACT7D,MAAM,KAAKoE,GAAG,GACV,kCAAkC,GAClC,mCAAmC,EACtC;gBAAAN,QAAA,EAEFO;cAAK,GARDD,GAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASF,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7E,OAAA,CAACR,UAAU;YAAC0F,aAAa,EAAEtB;UAAkB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGhD7E,OAAA;YAAKwE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvCzE,OAAA;cACEiF,OAAO,EAAEvB,aAAc;cACvByB,QAAQ,EAAElE,UAAW;cACrBuD,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EAEpHxD,UAAU,gBACTjB,OAAA,CAAAE,SAAA;gBAAAuE,QAAA,gBACEzE,OAAA;kBAAKwE,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAEtG;cAAA,eAAE,CAAC,gBAEH7E,OAAA,CAAAE,SAAA;gBAAAuE,QAAA,EAAE;cAEF,gBAAE;YACH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBlE,OAAO,IAAIF,KAAK,CAACuC,MAAM,KAAK,CAAC,gBAC5B5C,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,GAAGW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAClC,GAAG,CAAC,CAACmC,CAAC,EAAEC,CAAC,kBACtBtF,OAAA;gBAAawE,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBACtEzE,OAAA;kBAAKwE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CzE,OAAA;oBAAKwE,SAAS,EAAC;kBAAoC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1D7E,OAAA;oBAAKwE,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBzE,OAAA;sBAAKwE,SAAS,EAAC;oBAAoC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1D7E,OAAA;sBAAKwE,SAAS,EAAC;oBAA+B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBzE,OAAA;oBAAKwE,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C7E,OAAA;oBAAKwE,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA,GAXES,CAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,GACJxE,KAAK,CAACuC,MAAM,KAAK,CAAC,gBACpB5C,OAAA;cAAKwE,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DzE,OAAA;gBAAKwE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvC7E,OAAA;gBAAIwE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE7E,OAAA;gBAAGwE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAC9B9D,MAAM,KAAK,WAAW,GACnB,2CAA2C,GAC3C;cAA+C;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElD,CAAC,EACHlE,MAAM,KAAK,WAAW,iBACrBX,OAAA;gBACEiF,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAAC,KAAK,CAAE;gBACzCgB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEN7E,OAAA,CAAAE,SAAA;cAAAuE,QAAA,GACGpE,KAAK,CAAC6C,GAAG,CAACH,IAAI,iBACb/C,OAAA,CAACT,QAAQ;gBAEPwD,IAAI,EAAEA,IAAK;gBACXwC,MAAM,EAAEzB,cAAe;gBACvB0B,UAAU,EAAEpB;cAAmB,GAH1BrB,IAAI,CAACI,EAAE;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIb,CACF,CAAC,EAGD9D,OAAO,iBACNf,OAAA;gBAAKwE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACvCzE,OAAA;kBACEiF,OAAO,EAAEtB,QAAS;kBAClBwB,QAAQ,EAAE5E,OAAQ;kBAClBiE,SAAS,EAAC,2GAA2G;kBAAAC,QAAA,EAEpHlE,OAAO,gBACNP,OAAA,CAAAE,SAAA;oBAAAuE,QAAA,gBACEzE,OAAA;sBAAKwE,SAAS,EAAC;oBAA8E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,cAEtG;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA,eACD;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELpE,KAAK,iBACJT,OAAA;YAAKwE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEzE,OAAA;cAAKwE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzE,OAAA;gBAAKwE,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC7E,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzE,OAAA;kBAAIwE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3D7E,OAAA;kBAAGwE,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEhE;gBAAK;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzE,OAAA,CAACP,eAAe;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CArUID,UAAU;EAAA,QAcoBd,OAAO,EACTC,YAAY;AAAA;AAAAmG,EAAA,GAfxCtF,UAAU;AAuUhB,eAAeA,UAAU;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}