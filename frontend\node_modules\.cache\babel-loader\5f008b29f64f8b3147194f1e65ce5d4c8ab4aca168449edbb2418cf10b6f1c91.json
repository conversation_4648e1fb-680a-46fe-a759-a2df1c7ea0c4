{"ast": null, "code": "// originally pulled out of simple-peer\n\nmodule.exports = function getBrowserRTC() {\n  if (typeof globalThis === 'undefined') return null;\n  var wrtc = {\n    RTCPeerConnection: globalThis.RTCPeerConnection || globalThis.mozRTCPeerConnection || globalThis.webkitRTCPeerConnection,\n    RTCSessionDescription: globalThis.RTCSessionDescription || globalThis.mozRTCSessionDescription || globalThis.webkitRTCSessionDescription,\n    RTCIceCandidate: globalThis.RTCIceCandidate || globalThis.mozRTCIceCandidate || globalThis.webkitRTCIceCandidate\n  };\n  if (!wrtc.RTCPeerConnection) return null;\n  return wrtc;\n};", "map": {"version": 3, "names": ["module", "exports", "getBrowserRTC", "globalThis", "wrtc", "RTCPeerConnection", "mozRTCPeerConnection", "webkitRTCPeerConnection", "RTCSessionDescription", "mozRTCSessionDescription", "webkitRTCSessionDescription", "RTCIceCandidate", "mozRTCIceCandidate", "webkitRTCIceCandidate"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/get-browser-rtc/index.js"], "sourcesContent": ["// originally pulled out of simple-peer\n\nmodule.exports = function getBrowserRTC () {\n  if (typeof globalThis === 'undefined') return null\n  var wrtc = {\n    RTCPeerConnection: globalThis.RTCPeerConnection || globalThis.mozRTCPeerConnection ||\n      globalThis.webkitRTCPeerConnection,\n    RTCSessionDescription: globalThis.RTCSessionDescription ||\n      globalThis.mozRTCSessionDescription || globalThis.webkitRTCSessionDescription,\n    RTCIceCandidate: globalThis.RTCIceCandidate || globalThis.mozRTCIceCandidate ||\n      globalThis.webkitRTCIceCandidate\n  }\n  if (!wrtc.RTCPeerConnection) return null\n  return wrtc\n}\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAAA,EAAI;EACzC,IAAI,OAAOC,UAAU,KAAK,WAAW,EAAE,OAAO,IAAI;EAClD,IAAIC,IAAI,GAAG;IACTC,iBAAiB,EAAEF,UAAU,CAACE,iBAAiB,IAAIF,UAAU,CAACG,oBAAoB,IAChFH,UAAU,CAACI,uBAAuB;IACpCC,qBAAqB,EAAEL,UAAU,CAACK,qBAAqB,IACrDL,UAAU,CAACM,wBAAwB,IAAIN,UAAU,CAACO,2BAA2B;IAC/EC,eAAe,EAAER,UAAU,CAACQ,eAAe,IAAIR,UAAU,CAACS,kBAAkB,IAC1ET,UAAU,CAACU;EACf,CAAC;EACD,IAAI,CAACT,IAAI,CAACC,iBAAiB,EAAE,OAAO,IAAI;EACxC,OAAOD,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}