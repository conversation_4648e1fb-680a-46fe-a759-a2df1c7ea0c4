{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\contexts\\\\ApiContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApiContext = /*#__PURE__*/createContext();\nexport const useApi = () => {\n  _s();\n  const context = useContext(ApiContext);\n  if (!context) {\n    throw new Error('useApi must be used within an ApiProvider');\n  }\n  return context;\n};\n_s(useApi, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ApiProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    token\n  } = useAuth();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  // Generic API call function\n  const apiCall = async (endpoint, options = {}) => {\n    const url = `${API_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...(token && {\n          'Authorization': `Bearer ${token}`\n        }),\n        ...options.headers\n      },\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n      if (response.ok) {\n        return {\n          success: true,\n          data\n        };\n      } else {\n        return {\n          success: false,\n          error: data.error || 'API call failed'\n        };\n      }\n    } catch (error) {\n      console.error('API call error:', error);\n      return {\n        success: false,\n        error: 'Network error occurred'\n      };\n    }\n  };\n\n  // Articles API\n  const articles = {\n    getAll: (params = {}) => {\n      const queryString = new URLSearchParams(params).toString();\n      return apiCall(`/api/articles${queryString ? `?${queryString}` : ''}`);\n    },\n    getBySlug: slug => apiCall(`/api/articles/${slug}`),\n    create: articleData => apiCall('/api/articles', {\n      method: 'POST',\n      body: JSON.stringify(articleData)\n    }),\n    update: (slug, updates) => apiCall(`/api/articles/${slug}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n    delete: slug => apiCall(`/api/articles/${slug}`, {\n      method: 'DELETE'\n    })\n  };\n\n  // Discussions API\n  const discussions = {\n    getAll: (params = {}) => {\n      const queryString = new URLSearchParams(params).toString();\n      return apiCall(`/api/discussions${queryString ? `?${queryString}` : ''}`);\n    },\n    getById: id => apiCall(`/api/discussions/${id}`),\n    create: threadData => apiCall('/api/discussions', {\n      method: 'POST',\n      body: JSON.stringify(threadData)\n    }),\n    update: (id, updates) => apiCall(`/api/discussions/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n    delete: id => apiCall(`/api/discussions/${id}`, {\n      method: 'DELETE'\n    })\n  };\n\n  // Comments API\n  const comments = {\n    create: commentData => apiCall('/api/comments', {\n      method: 'POST',\n      body: JSON.stringify(commentData)\n    }),\n    update: (id, updates) => apiCall(`/api/comments/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n    delete: id => apiCall(`/api/comments/${id}`, {\n      method: 'DELETE'\n    }),\n    vote: (id, voteType) => apiCall(`/api/comments/${id}/vote`, {\n      method: 'POST',\n      body: JSON.stringify({\n        vote_type: voteType\n      })\n    }),\n    getUserVote: id => apiCall(`/api/comments/${id}/vote`)\n  };\n\n  // Health check\n  const health = {\n    checkDatabase: () => apiCall('/health/db')\n  };\n  const value = {\n    apiCall,\n    articles,\n    discussions,\n    comments,\n    health\n  };\n  return /*#__PURE__*/_jsxDEV(ApiContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s2(ApiProvider, \"ZpOvj47KCDv4EW5BIUHQHDAsr3M=\", false, function () {\n  return [useAuth];\n});\n_c = ApiProvider;\nvar _c;\n$RefreshReg$(_c, \"ApiProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useAuth", "jsxDEV", "_jsxDEV", "ApiContext", "useApi", "_s", "context", "Error", "A<PERSON><PERSON><PERSON><PERSON>", "children", "_s2", "token", "API_URL", "process", "env", "REACT_APP_API_URL", "apiCall", "endpoint", "options", "url", "config", "headers", "response", "fetch", "data", "json", "ok", "success", "error", "console", "articles", "getAll", "params", "queryString", "URLSearchParams", "toString", "getBySlug", "slug", "create", "articleData", "method", "body", "JSON", "stringify", "update", "updates", "delete", "discussions", "getById", "id", "threadData", "comments", "commentData", "vote", "voteType", "vote_type", "getUserVote", "health", "checkDatabase", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/contexts/ApiContext.js"], "sourcesContent": ["import React, { createContext, useContext } from 'react';\nimport { useAuth } from './AuthContext';\n\nconst ApiContext = createContext();\n\nexport const useApi = () => {\n  const context = useContext(ApiContext);\n  if (!context) {\n    throw new Error('useApi must be used within an ApiProvider');\n  }\n  return context;\n};\n\nexport const ApiProvider = ({ children }) => {\n  const { token } = useAuth();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  // Generic API call function\n  const apiCall = async (endpoint, options = {}) => {\n    const url = `${API_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...(token && { 'Authorization': `Bearer ${token}` }),\n        ...options.headers\n      },\n      ...options\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (response.ok) {\n        return { success: true, data };\n      } else {\n        return { success: false, error: data.error || 'API call failed' };\n      }\n    } catch (error) {\n      console.error('API call error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  // Articles API\n  const articles = {\n    getAll: (params = {}) => {\n      const queryString = new URLSearchParams(params).toString();\n      return apiCall(`/api/articles${queryString ? `?${queryString}` : ''}`);\n    },\n\n    getBySlug: (slug) => apiCall(`/api/articles/${slug}`),\n\n    create: (articleData) => apiCall('/api/articles', {\n      method: 'POST',\n      body: JSON.stringify(articleData)\n    }),\n\n    update: (slug, updates) => apiCall(`/api/articles/${slug}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n\n    delete: (slug) => apiCall(`/api/articles/${slug}`, {\n      method: 'DELETE'\n    })\n  };\n\n  // Discussions API\n  const discussions = {\n    getAll: (params = {}) => {\n      const queryString = new URLSearchParams(params).toString();\n      return apiCall(`/api/discussions${queryString ? `?${queryString}` : ''}`);\n    },\n\n    getById: (id) => apiCall(`/api/discussions/${id}`),\n\n    create: (threadData) => apiCall('/api/discussions', {\n      method: 'POST',\n      body: JSON.stringify(threadData)\n    }),\n\n    update: (id, updates) => apiCall(`/api/discussions/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n\n    delete: (id) => apiCall(`/api/discussions/${id}`, {\n      method: 'DELETE'\n    })\n  };\n\n  // Comments API\n  const comments = {\n    create: (commentData) => apiCall('/api/comments', {\n      method: 'POST',\n      body: JSON.stringify(commentData)\n    }),\n\n    update: (id, updates) => apiCall(`/api/comments/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates)\n    }),\n\n    delete: (id) => apiCall(`/api/comments/${id}`, {\n      method: 'DELETE'\n    }),\n\n    vote: (id, voteType) => apiCall(`/api/comments/${id}/vote`, {\n      method: 'POST',\n      body: JSON.stringify({ vote_type: voteType })\n    }),\n\n    getUserVote: (id) => apiCall(`/api/comments/${id}/vote`)\n  };\n\n  // Health check\n  const health = {\n    checkDatabase: () => apiCall('/health/db')\n  };\n\n  const value = {\n    apiCall,\n    articles,\n    discussions,\n    comments,\n    health\n  };\n\n  return (\n    <ApiContext.Provider value={value}>\n      {children}\n    </ApiContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACxD,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,UAAU,gBAAGL,aAAa,CAAC,CAAC;AAElC,OAAO,MAAMM,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,OAAO,GAAGP,UAAU,CAACI,UAAU,CAAC;EACtC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,MAAM;AAQnB,OAAO,MAAMI,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC3C,MAAM;IAAEC;EAAM,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3B,MAAMY,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;EAExE;EACA,MAAMC,OAAO,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAChD,MAAMC,GAAG,GAAG,GAAGP,OAAO,GAAGK,QAAQ,EAAE;IACnC,MAAMG,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,IAAIV,KAAK,IAAI;UAAE,eAAe,EAAE,UAAUA,KAAK;QAAG,CAAC,CAAC;QACpD,GAAGO,OAAO,CAACG;MACb,CAAC;MACD,GAAGH;IACL,CAAC;IAED,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;MACzC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACI,EAAE,EAAE;QACf,OAAO;UAAEC,OAAO,EAAE,IAAI;UAAEH;QAAK,CAAC;MAChC,CAAC,MAAM;QACL,OAAO;UAAEG,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEJ,IAAI,CAACI,KAAK,IAAI;QAAkB,CAAC;MACnE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAyB,CAAC;IAC5D;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG;IACfC,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;MACvB,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;MAC1D,OAAOnB,OAAO,CAAC,gBAAgBiB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;IACxE,CAAC;IAEDG,SAAS,EAAGC,IAAI,IAAKrB,OAAO,CAAC,iBAAiBqB,IAAI,EAAE,CAAC;IAErDC,MAAM,EAAGC,WAAW,IAAKvB,OAAO,CAAC,eAAe,EAAE;MAChDwB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW;IAClC,CAAC,CAAC;IAEFK,MAAM,EAAEA,CAACP,IAAI,EAAEQ,OAAO,KAAK7B,OAAO,CAAC,iBAAiBqB,IAAI,EAAE,EAAE;MAC1DG,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,OAAO;IAC9B,CAAC,CAAC;IAEFC,MAAM,EAAGT,IAAI,IAAKrB,OAAO,CAAC,iBAAiBqB,IAAI,EAAE,EAAE;MACjDG,MAAM,EAAE;IACV,CAAC;EACH,CAAC;;EAED;EACA,MAAMO,WAAW,GAAG;IAClBhB,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;MACvB,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;MAC1D,OAAOnB,OAAO,CAAC,mBAAmBiB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;IAC3E,CAAC;IAEDe,OAAO,EAAGC,EAAE,IAAKjC,OAAO,CAAC,oBAAoBiC,EAAE,EAAE,CAAC;IAElDX,MAAM,EAAGY,UAAU,IAAKlC,OAAO,CAAC,kBAAkB,EAAE;MAClDwB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACO,UAAU;IACjC,CAAC,CAAC;IAEFN,MAAM,EAAEA,CAACK,EAAE,EAAEJ,OAAO,KAAK7B,OAAO,CAAC,oBAAoBiC,EAAE,EAAE,EAAE;MACzDT,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,OAAO;IAC9B,CAAC,CAAC;IAEFC,MAAM,EAAGG,EAAE,IAAKjC,OAAO,CAAC,oBAAoBiC,EAAE,EAAE,EAAE;MAChDT,MAAM,EAAE;IACV,CAAC;EACH,CAAC;;EAED;EACA,MAAMW,QAAQ,GAAG;IACfb,MAAM,EAAGc,WAAW,IAAKpC,OAAO,CAAC,eAAe,EAAE;MAChDwB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACS,WAAW;IAClC,CAAC,CAAC;IAEFR,MAAM,EAAEA,CAACK,EAAE,EAAEJ,OAAO,KAAK7B,OAAO,CAAC,iBAAiBiC,EAAE,EAAE,EAAE;MACtDT,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,OAAO;IAC9B,CAAC,CAAC;IAEFC,MAAM,EAAGG,EAAE,IAAKjC,OAAO,CAAC,iBAAiBiC,EAAE,EAAE,EAAE;MAC7CT,MAAM,EAAE;IACV,CAAC,CAAC;IAEFa,IAAI,EAAEA,CAACJ,EAAE,EAAEK,QAAQ,KAAKtC,OAAO,CAAC,iBAAiBiC,EAAE,OAAO,EAAE;MAC1DT,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEY,SAAS,EAAED;MAAS,CAAC;IAC9C,CAAC,CAAC;IAEFE,WAAW,EAAGP,EAAE,IAAKjC,OAAO,CAAC,iBAAiBiC,EAAE,OAAO;EACzD,CAAC;;EAED;EACA,MAAMQ,MAAM,GAAG;IACbC,aAAa,EAAEA,CAAA,KAAM1C,OAAO,CAAC,YAAY;EAC3C,CAAC;EAED,MAAM2C,KAAK,GAAG;IACZ3C,OAAO;IACPc,QAAQ;IACRiB,WAAW;IACXI,QAAQ;IACRM;EACF,CAAC;EAED,oBACEvD,OAAA,CAACC,UAAU,CAACyD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlD,QAAA,EAC/BA;EAAQ;IAAAoD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B,CAAC;AAACtD,GAAA,CAzHWF,WAAW;EAAA,QACJR,OAAO;AAAA;AAAAiE,EAAA,GADdzD,WAAW;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}