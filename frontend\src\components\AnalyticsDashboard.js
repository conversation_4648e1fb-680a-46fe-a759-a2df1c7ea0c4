import React, { useState, useEffect } from 'react';

const AnalyticsDashboard = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('engagement');
  const [analyticsData, setAnalyticsData] = useState(null);

  // Mock analytics data
  useEffect(() => {
    setAnalyticsData({
      overview: {
        totalPosts: 47,
        totalViews: 125430,
        totalEngagement: 8920,
        newFollowers: 234,
        engagementRate: 7.1,
        reachGrowth: 15.3
      },
      chartData: {
        engagement: [
          { date: '2024-05-28', value: 1200 },
          { date: '2024-05-29', value: 1450 },
          { date: '2024-05-30', value: 1100 },
          { date: '2024-05-31', value: 1800 },
          { date: '2024-06-01', value: 1650 },
          { date: '2024-06-02', value: 2100 },
          { date: '2024-06-03', value: 1920 }
        ],
        reach: [
          { date: '2024-05-28', value: 15200 },
          { date: '2024-05-29', value: 18450 },
          { date: '2024-05-30', value: 16100 },
          { date: '2024-05-31', value: 21800 },
          { date: '2024-06-01', value: 19650 },
          { date: '2024-06-02', value: 24100 },
          { date: '2024-06-03', value: 22920 }
        ]
      },
      topPosts: [
        {
          id: 1,
          content: "Climate change policy discussion: The urgency of renewable energy transition cannot be overstated...",
          views: 15420,
          engagement: 892,
          shares: 156,
          date: '2024-06-01'
        },
        {
          id: 2,
          content: "Healthcare reform update: Universal healthcare coverage analysis and implementation strategies...",
          views: 12350,
          engagement: 734,
          shares: 98,
          date: '2024-05-30'
        },
        {
          id: 3,
          content: "Economic policy insights: Inflation impact on working families and proposed solutions...",
          views: 9870,
          engagement: 567,
          shares: 87,
          date: '2024-05-29'
        }
      ],
      demographics: {
        ageGroups: [
          { range: '18-24', percentage: 15 },
          { range: '25-34', percentage: 28 },
          { range: '35-44', percentage: 25 },
          { range: '45-54', percentage: 20 },
          { range: '55+', percentage: 12 }
        ],
        locations: [
          { state: 'California', percentage: 18 },
          { state: 'New York', percentage: 14 },
          { state: 'Texas', percentage: 12 },
          { state: 'Florida', percentage: 10 },
          { state: 'Illinois', percentage: 8 }
        ]
      }
    });
  }, [timeRange]);

  const timeRanges = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' }
  ];

  const metrics = [
    { key: 'engagement', label: 'Engagement', icon: '💬', color: 'text-primary-600' },
    { key: 'reach', label: 'Reach', icon: '📊', color: 'text-success-600' },
    { key: 'followers', label: 'Followers', icon: '👥', color: 'text-purple-600' },
    { key: 'impressions', label: 'Impressions', icon: '👁️', color: 'text-orange-600' }
  ];

  if (!analyticsData) {
    return (
      <div className="space-y-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="card p-6 animate-pulse">
            <div className="h-6 bg-secondary-200 rounded w-1/4 mb-4"></div>
            <div className="h-32 bg-secondary-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-secondary-900 flex items-center gap-2">
              📊 Analytics Dashboard
            </h2>
            <p className="text-secondary-600 mt-1">
              Track your political content performance and audience insights
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="form-select"
            >
              {timeRanges.map(range => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
            <button className="btn btn-outline">
              📥 Export Report
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Posts</p>
              <p className="text-2xl font-bold text-secondary-900">{analyticsData.overview.totalPosts}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-xl">📝</span>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-sm text-success-600">↗️ +12% from last period</span>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Views</p>
              <p className="text-2xl font-bold text-secondary-900">{analyticsData.overview.totalViews.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-success-100 rounded-full flex items-center justify-center">
              <span className="text-xl">👁️</span>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-sm text-success-600">↗️ +{analyticsData.overview.reachGrowth}% reach growth</span>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Engagement</p>
              <p className="text-2xl font-bold text-secondary-900">{analyticsData.overview.totalEngagement.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-xl">💬</span>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-sm text-success-600">↗️ {analyticsData.overview.engagementRate}% rate</span>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">New Followers</p>
              <p className="text-2xl font-bold text-secondary-900">{analyticsData.overview.newFollowers}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-xl">👥</span>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-sm text-success-600">↗️ +18% growth rate</span>
          </div>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-secondary-900">Performance Trends</h3>
          <div className="flex items-center gap-2">
            {metrics.map(metric => (
              <button
                key={metric.key}
                onClick={() => setSelectedMetric(metric.key)}
                className={`btn btn-sm ${
                  selectedMetric === metric.key ? 'btn-primary' : 'btn-ghost'
                }`}
              >
                <span>{metric.icon}</span>
                <span className="hidden sm:inline ml-1">{metric.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Simple Chart Visualization */}
        <div className="h-64 flex items-end justify-between gap-2">
          {analyticsData.chartData[selectedMetric]?.map((point, index) => {
            const maxValue = Math.max(...analyticsData.chartData[selectedMetric].map(p => p.value));
            const height = (point.value / maxValue) * 100;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-primary-500 rounded-t transition-all duration-500 hover:bg-primary-600"
                  style={{ height: `${height}%` }}
                  title={`${point.date}: ${point.value.toLocaleString()}`}
                ></div>
                <div className="text-xs text-secondary-500 mt-2 transform -rotate-45 origin-left">
                  {new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Top Performing Posts */}
        <div className="card">
          <div className="p-6 border-b border-secondary-200">
            <h3 className="text-lg font-semibold text-secondary-900">Top Performing Posts</h3>
          </div>
          <div className="divide-y divide-secondary-200">
            {analyticsData.topPosts.map((post, index) => (
              <div key={post.id} className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm font-bold text-primary-600">
                    #{index + 1}
                  </div>
                  <div className="flex-1">
                    <p className="text-secondary-900 mb-3 line-clamp-2">
                      {post.content}
                    </p>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-secondary-500">Views</span>
                        <div className="font-semibold text-secondary-900">{post.views.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-secondary-500">Engagement</span>
                        <div className="font-semibold text-secondary-900">{post.engagement}</div>
                      </div>
                      <div>
                        <span className="text-secondary-500">Shares</span>
                        <div className="font-semibold text-secondary-900">{post.shares}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Audience Demographics */}
        <div className="card">
          <div className="p-6 border-b border-secondary-200">
            <h3 className="text-lg font-semibold text-secondary-900">Audience Demographics</h3>
          </div>
          <div className="p-6 space-y-6">
            
            {/* Age Groups */}
            <div>
              <h4 className="font-medium text-secondary-900 mb-3">Age Distribution</h4>
              <div className="space-y-3">
                {analyticsData.demographics.ageGroups.map(group => (
                  <div key={group.range} className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">{group.range}</span>
                    <div className="flex items-center gap-3 flex-1 ml-4">
                      <div className="flex-1 bg-secondary-200 rounded-full h-2">
                        <div
                          className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${group.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-secondary-900 w-8">
                        {group.percentage}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Locations */}
            <div>
              <h4 className="font-medium text-secondary-900 mb-3">Top Locations</h4>
              <div className="space-y-3">
                {analyticsData.demographics.locations.map(location => (
                  <div key={location.state} className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">{location.state}</span>
                    <div className="flex items-center gap-3 flex-1 ml-4">
                      <div className="flex-1 bg-secondary-200 rounded-full h-2">
                        <div
                          className="bg-success-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${location.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-secondary-900 w-8">
                        {location.percentage}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Insights and Recommendations */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">AI-Powered Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-primary-50 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <span className="text-primary-600 text-xl">🎯</span>
              <div>
                <h4 className="font-medium text-primary-900">Optimal Posting Times</h4>
                <p className="text-sm text-primary-700 mt-1">
                  Your audience is most active on weekdays between 9-11 AM and 7-9 PM EST. 
                  Consider scheduling more content during these peak hours.
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-success-50 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <span className="text-success-600 text-xl">📈</span>
              <div>
                <h4 className="font-medium text-success-900">Content Performance</h4>
                <p className="text-sm text-success-700 mt-1">
                  Posts about climate policy generate 40% more engagement than average. 
                  Consider creating more content on environmental topics.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
