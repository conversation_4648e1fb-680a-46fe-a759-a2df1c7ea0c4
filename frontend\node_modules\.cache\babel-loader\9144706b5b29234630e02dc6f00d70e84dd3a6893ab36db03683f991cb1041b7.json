{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\SocialFeed.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport PostCard from '../components/PostCard';\nimport CreatePost from '../components/CreatePost';\nimport TrendingSidebar from '../components/TrendingSidebar';\nimport ProfessionalHeader from '../components/ProfessionalHeader';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport ContentScheduler from '../components/ContentScheduler';\nimport AnalyticsDashboard from '../components/AnalyticsDashboard';\nimport '../styles/design-system.css';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SocialFeed = () => {\n  _s();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all'); // 'all', 'following', 'trending'\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [viewMode, setViewMode] = useState('feed'); // 'feed', 'analytics', 'scheduler'\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);\n  const [selectedTopics, setSelectedTopics] = useState([]);\n  const [dateRange, setDateRange] = useState('all');\n  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'popular', 'engagement'\n\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    socket,\n    isConnected\n  } = useWebSocket();\n\n  // Fetch posts\n  const fetchPosts = useCallback(async (pageNum = 1, filterType = filter, reset = false) => {\n    try {\n      if (pageNum === 1) setLoading(true);\n      const response = await axios.get('/api/feed', {\n        params: {\n          page: pageNum,\n          limit: 20,\n          filter: filterType\n        }\n      });\n      const newPosts = response.data.posts;\n      if (reset || pageNum === 1) {\n        setPosts(newPosts);\n      } else {\n        setPosts(prev => [...prev, ...newPosts]);\n      }\n      setHasMore(newPosts.length === 20);\n      setPage(pageNum);\n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      setError('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [filter]);\n\n  // Initial load\n  useEffect(() => {\n    fetchPosts(1, filter, true);\n  }, [filter]);\n\n  // WebSocket listeners for real-time updates\n  useEffect(() => {\n    if (!socket) return;\n    const handleNewPost = post => {\n      setPosts(prev => [post, ...prev]);\n    };\n    const handlePostUpdate = updatedPost => {\n      setPosts(prev => prev.map(post => post.id === updatedPost.id ? {\n        ...post,\n        ...updatedPost\n      } : post));\n    };\n    const handlePostDelete = postId => {\n      setPosts(prev => prev.filter(post => post.id !== postId));\n    };\n    socket.on('new-post', handleNewPost);\n    socket.on('post-updated', handlePostUpdate);\n    socket.on('post-deleted', handlePostDelete);\n    return () => {\n      socket.off('new-post');\n      socket.off('post-updated');\n      socket.off('post-deleted');\n    };\n  }, [socket]);\n\n  // Handle filter change\n  const handleFilterChange = newFilter => {\n    setFilter(newFilter);\n    setPage(1);\n    setPosts([]);\n  };\n\n  // Handle refresh\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchPosts(1, filter, true);\n  };\n\n  // Load more posts\n  const loadMore = () => {\n    if (!loading && hasMore) {\n      fetchPosts(page + 1, filter, false);\n    }\n  };\n\n  // Handle new post created\n  const handlePostCreated = newPost => {\n    setPosts(prev => [newPost, ...prev]);\n  };\n\n  // Handle post vote\n  const handlePostVote = async (postId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/vote`, {\n        vote_type: voteType\n      });\n      setPosts(prev => prev.map(post => post.id === postId ? {\n        ...post,\n        upvotes: response.data.upvotes,\n        downvotes: response.data.downvotes,\n        user_vote: response.data.user_vote\n      } : post));\n    } catch (error) {\n      console.error('Error voting on post:', error);\n    }\n  };\n\n  // Handle post reaction\n  const handlePostReaction = async (postId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/react`, {\n        reaction_type: reactionType\n      });\n      setPosts(prev => prev.map(post => post.id === postId ? {\n        ...post,\n        reactions: response.data.reactions\n      } : post));\n    } catch (error) {\n      console.error('Error reacting to post:', error);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Join the Political Conversation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Sign in to participate in political discussions and share your views\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\",\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-secondary-50\",\n    children: [/*#__PURE__*/_jsxDEV(ProfessionalHeader, {\n      viewMode: viewMode,\n      setViewMode: setViewMode,\n      isConnected: isConnected,\n      user: user\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-3\",\n          children: [viewMode === 'feed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card mb-6 p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-3xl font-bold text-secondary-900\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"gradient-primary bg-clip-text text-transparent\",\n                      children: \"Political Feed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-3 h-3 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-secondary-600\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowAdvancedFilters(!showAdvancedFilters),\n                    className: \"btn btn-outline btn-sm\",\n                    children: \"\\uD83D\\uDD0D Advanced Filters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleRefresh,\n                    disabled: refreshing,\n                    className: \"btn btn-primary btn-sm\",\n                    children: [refreshing ? '🔄' : '↻', \" Refresh\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), showAdvancedFilters && /*#__PURE__*/_jsxDEV(AdvancedFilters, {\n                selectedTopics: selectedTopics,\n                setSelectedTopics: setSelectedTopics,\n                dateRange: dateRange,\n                setDateRange: setDateRange,\n                sortBy: sortBy,\n                setSortBy: setSortBy,\n                onApplyFilters: () => {\n                  fetchPosts();\n                },\n                onClearFilters: () => {\n                  setSelectedTopics([]);\n                  setDateRange('all');\n                  setSortBy('recent');\n                  fetchPosts();\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1 bg-secondary-100 rounded-xl p-1\",\n                children: [{\n                  key: 'all',\n                  label: '🌍 All Posts',\n                  count: posts.length\n                }, {\n                  key: 'following',\n                  label: '👥 Following',\n                  count: 0\n                }, {\n                  key: 'trending',\n                  label: '🔥 Trending',\n                  count: 0\n                }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleFilterChange(tab.key),\n                  className: `flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all ${filter === tab.key ? 'bg-white text-primary-600 shadow-sm' : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: tab.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this), tab.count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-secondary-200 text-secondary-600 px-2 py-0.5 rounded-full text-xs\",\n                    children: tab.count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 27\n                  }, this)]\n                }, tab.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CreatePost, {\n              onPostCreated: handlePostCreated\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: loading && posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card p-6 animate-pulse\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-secondary-300 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-4 bg-secondary-300 rounded w-1/4 mb-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-3 bg-secondary-300 rounded w-1/6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-4 bg-secondary-300 rounded\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-4 bg-secondary-300 rounded w-3/4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 27\n                  }, this)]\n                }, i, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card p-12 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83D\\uDCED\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-secondary-900 mb-2\",\n                  children: \"No posts yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-secondary-600 mb-6\",\n                  children: filter === 'following' ? \"Follow some users to see their posts here\" : \"Be the first to start a political discussion!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), filter === 'following' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleFilterChange('all'),\n                  className: \"btn btn-primary\",\n                  children: \"View All Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [posts.map(post => /*#__PURE__*/_jsxDEV(PostCard, {\n                  post: post,\n                  onVote: handlePostVote,\n                  onReaction: handlePostReaction\n                }, post.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 25\n                }, this)), hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center py-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: loadMore,\n                    disabled: loading,\n                    className: \"btn btn-secondary btn-lg\",\n                    children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 33\n                      }, this), \"Loading...\"]\n                    }, void 0, true) : 'Load More Posts'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-error-50 border border-error-200 rounded-lg p-4 mt-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-error-400\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-sm font-medium text-error-800\",\n                    children: \"Error\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-error-700 mt-1\",\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), viewMode === 'analytics' && /*#__PURE__*/_jsxDEV(AnalyticsDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), viewMode === 'scheduler' && /*#__PURE__*/_jsxDEV(ContentScheduler, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), viewMode === 'events' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card p-12 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl mb-4\",\n              children: \"\\uD83C\\uDFAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-secondary-900 mb-2\",\n              children: \"Events Coming Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary-600\",\n              children: \"Political events and campaign management features will be available soon.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(TrendingSidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(SocialFeed, \"HAzxo4JCe5TCTZLoEsFpc4/bEHE=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = SocialFeed;\nexport default SocialFeed;\nvar _c;\n$RefreshReg$(_c, \"SocialFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "useWebSocket", "PostCard", "CreatePost", "TrendingSidebar", "<PERSON><PERSON><PERSON><PERSON>", "AdvancedFilters", "ContentScheduler", "AnalyticsDashboard", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SocialFeed", "_s", "posts", "setPosts", "loading", "setLoading", "error", "setError", "filter", "setFilter", "page", "setPage", "hasMore", "setHasMore", "refreshing", "setRefreshing", "viewMode", "setViewMode", "showAdvancedFilters", "setShowAdvancedFilters", "selectedTopics", "setSelectedTopics", "date<PERSON><PERSON><PERSON>", "setDateRange", "sortBy", "setSortBy", "user", "isAuthenticated", "socket", "isConnected", "fetchPosts", "pageNum", "filterType", "reset", "response", "get", "params", "limit", "newPosts", "data", "prev", "length", "console", "handleNewPost", "post", "handlePostUpdate", "updatedPost", "map", "id", "handlePostDelete", "postId", "on", "off", "handleFilterChange", "newFilter", "handleRefresh", "loadMore", "handlePostCreated", "newPost", "handlePostVote", "voteType", "vote_type", "upvotes", "downvotes", "user_vote", "handlePostReaction", "reactionType", "reaction_type", "reactions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "disabled", "onApplyFilters", "onClearFilters", "key", "label", "count", "tab", "onPostCreated", "Array", "_", "i", "onVote", "onReaction", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/SocialFeed.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport PostCard from '../components/PostCard';\nimport CreatePost from '../components/CreatePost';\nimport TrendingSidebar from '../components/TrendingSidebar';\nimport ProfessionalHeader from '../components/ProfessionalHeader';\nimport AdvancedFilters from '../components/AdvancedFilters';\nimport ContentScheduler from '../components/ContentScheduler';\nimport AnalyticsDashboard from '../components/AnalyticsDashboard';\nimport '../styles/design-system.css';\nimport axios from 'axios';\n\nconst SocialFeed = () => {\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all'); // 'all', 'following', 'trending'\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [viewMode, setViewMode] = useState('feed'); // 'feed', 'analytics', 'scheduler'\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);\n  const [selectedTopics, setSelectedTopics] = useState([]);\n  const [dateRange, setDateRange] = useState('all');\n  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'popular', 'engagement'\n\n  const { user, isAuthenticated } = useAuth();\n  const { socket, isConnected } = useWebSocket();\n\n  // Fetch posts\n  const fetchPosts = useCallback(async (pageNum = 1, filterType = filter, reset = false) => {\n    try {\n      if (pageNum === 1) setLoading(true);\n      \n      const response = await axios.get('/api/feed', {\n        params: {\n          page: pageNum,\n          limit: 20,\n          filter: filterType\n        }\n      });\n\n      const newPosts = response.data.posts;\n      \n      if (reset || pageNum === 1) {\n        setPosts(newPosts);\n      } else {\n        setPosts(prev => [...prev, ...newPosts]);\n      }\n\n      setHasMore(newPosts.length === 20);\n      setPage(pageNum);\n      \n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      setError('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [filter]);\n\n  // Initial load\n  useEffect(() => {\n    fetchPosts(1, filter, true);\n  }, [filter]);\n\n  // WebSocket listeners for real-time updates\n  useEffect(() => {\n    if (!socket) return;\n\n    const handleNewPost = (post) => {\n      setPosts(prev => [post, ...prev]);\n    };\n\n    const handlePostUpdate = (updatedPost) => {\n      setPosts(prev => prev.map(post => \n        post.id === updatedPost.id ? { ...post, ...updatedPost } : post\n      ));\n    };\n\n    const handlePostDelete = (postId) => {\n      setPosts(prev => prev.filter(post => post.id !== postId));\n    };\n\n    socket.on('new-post', handleNewPost);\n    socket.on('post-updated', handlePostUpdate);\n    socket.on('post-deleted', handlePostDelete);\n\n    return () => {\n      socket.off('new-post');\n      socket.off('post-updated');\n      socket.off('post-deleted');\n    };\n  }, [socket]);\n\n  // Handle filter change\n  const handleFilterChange = (newFilter) => {\n    setFilter(newFilter);\n    setPage(1);\n    setPosts([]);\n  };\n\n  // Handle refresh\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchPosts(1, filter, true);\n  };\n\n  // Load more posts\n  const loadMore = () => {\n    if (!loading && hasMore) {\n      fetchPosts(page + 1, filter, false);\n    }\n  };\n\n  // Handle new post created\n  const handlePostCreated = (newPost) => {\n    setPosts(prev => [newPost, ...prev]);\n  };\n\n  // Handle post vote\n  const handlePostVote = async (postId, voteType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/vote`, {\n        vote_type: voteType\n      });\n\n      setPosts(prev => prev.map(post => \n        post.id === postId \n          ? { \n              ...post, \n              upvotes: response.data.upvotes,\n              downvotes: response.data.downvotes,\n              user_vote: response.data.user_vote\n            }\n          : post\n      ));\n\n    } catch (error) {\n      console.error('Error voting on post:', error);\n    }\n  };\n\n  // Handle post reaction\n  const handlePostReaction = async (postId, reactionType) => {\n    try {\n      const response = await axios.post(`/api/feed/${postId}/react`, {\n        reaction_type: reactionType\n      });\n\n      setPosts(prev => prev.map(post => \n        post.id === postId \n          ? { \n              ...post, \n              reactions: response.data.reactions\n            }\n          : post\n      ));\n\n    } catch (error) {\n      console.error('Error reacting to post:', error);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Join the Political Conversation</h2>\n          <p className=\"text-gray-600 mb-6\">Sign in to participate in political discussions and share your views</p>\n          <a href=\"/login\" className=\"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\">\n            Sign In\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-secondary-50\">\n      {/* Professional Header */}\n      <ProfessionalHeader\n        viewMode={viewMode}\n        setViewMode={setViewMode}\n        isConnected={isConnected}\n        user={user}\n      />\n\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n\n          {/* Main Content Area */}\n          <div className=\"lg:col-span-3\">\n\n            {/* Feed View */}\n            {viewMode === 'feed' && (\n              <>\n                {/* Advanced Filter Bar */}\n                <div className=\"card mb-6 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center gap-4\">\n                      <h1 className=\"text-3xl font-bold text-secondary-900\">\n                        <span className=\"gradient-primary bg-clip-text text-transparent\">\n                          Political Feed\n                        </span>\n                      </h1>\n                      <div className=\"flex items-center gap-2\">\n                        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`}></div>\n                        <span className=\"text-sm font-medium text-secondary-600\">\n                          {isConnected ? 'Live' : 'Offline'}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center gap-3\">\n                      <button\n                        onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}\n                        className=\"btn btn-outline btn-sm\"\n                      >\n                        🔍 Advanced Filters\n                      </button>\n                      <button\n                        onClick={handleRefresh}\n                        disabled={refreshing}\n                        className=\"btn btn-primary btn-sm\"\n                      >\n                        {refreshing ? '🔄' : '↻'} Refresh\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Advanced Filters */}\n                  {showAdvancedFilters && (\n                    <AdvancedFilters\n                      selectedTopics={selectedTopics}\n                      setSelectedTopics={setSelectedTopics}\n                      dateRange={dateRange}\n                      setDateRange={setDateRange}\n                      sortBy={sortBy}\n                      setSortBy={setSortBy}\n                      onApplyFilters={() => {\n                        fetchPosts();\n                      }}\n                      onClearFilters={() => {\n                        setSelectedTopics([]);\n                        setDateRange('all');\n                        setSortBy('recent');\n                        fetchPosts();\n                      }}\n                    />\n                  )}\n\n                  {/* Filter Tabs */}\n                  <div className=\"flex space-x-1 bg-secondary-100 rounded-xl p-1\">\n                    {[\n                      { key: 'all', label: '🌍 All Posts', count: posts.length },\n                      { key: 'following', label: '👥 Following', count: 0 },\n                      { key: 'trending', label: '🔥 Trending', count: 0 }\n                    ].map(tab => (\n                      <button\n                        key={tab.key}\n                        onClick={() => handleFilterChange(tab.key)}\n                        className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all ${\n                          filter === tab.key\n                            ? 'bg-white text-primary-600 shadow-sm'\n                            : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'\n                        }`}\n                      >\n                        <span>{tab.label}</span>\n                        {tab.count > 0 && (\n                          <span className=\"bg-secondary-200 text-secondary-600 px-2 py-0.5 rounded-full text-xs\">\n                            {tab.count}\n                          </span>\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Create Post */}\n                <CreatePost onPostCreated={handlePostCreated} />\n\n                {/* Posts */}\n                <div className=\"space-y-6\">\n                  {loading && posts.length === 0 ? (\n                    <div className=\"space-y-6\">\n                      {[...Array(5)].map((_, i) => (\n                        <div key={i} className=\"card p-6 animate-pulse\">\n                          <div className=\"flex items-center space-x-3 mb-4\">\n                            <div className=\"w-10 h-10 bg-secondary-300 rounded-full\"></div>\n                            <div className=\"flex-1\">\n                              <div className=\"h-4 bg-secondary-300 rounded w-1/4 mb-2\"></div>\n                              <div className=\"h-3 bg-secondary-300 rounded w-1/6\"></div>\n                            </div>\n                          </div>\n                          <div className=\"space-y-2\">\n                            <div className=\"h-4 bg-secondary-300 rounded\"></div>\n                            <div className=\"h-4 bg-secondary-300 rounded w-3/4\"></div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : posts.length === 0 ? (\n                    <div className=\"card p-12 text-center\">\n                      <div className=\"text-6xl mb-4\">📭</div>\n                      <h3 className=\"text-lg font-medium text-secondary-900 mb-2\">No posts yet</h3>\n                      <p className=\"text-secondary-600 mb-6\">\n                        {filter === 'following'\n                          ? \"Follow some users to see their posts here\"\n                          : \"Be the first to start a political discussion!\"\n                        }\n                      </p>\n                      {filter === 'following' && (\n                        <button\n                          onClick={() => handleFilterChange('all')}\n                          className=\"btn btn-primary\"\n                        >\n                          View All Posts\n                        </button>\n                      )}\n                    </div>\n                  ) : (\n                    <>\n                      {posts.map(post => (\n                        <PostCard\n                          key={post.id}\n                          post={post}\n                          onVote={handlePostVote}\n                          onReaction={handlePostReaction}\n                        />\n                      ))}\n\n                      {/* Load More */}\n                      {hasMore && (\n                        <div className=\"flex justify-center py-6\">\n                          <button\n                            onClick={loadMore}\n                            disabled={loading}\n                            className=\"btn btn-secondary btn-lg\"\n                          >\n                            {loading ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"></div>\n                                Loading...\n                              </>\n                            ) : (\n                              'Load More Posts'\n                            )}\n                          </button>\n                        </div>\n                      )}\n                    </>\n                  )}\n                </div>\n\n                {error && (\n                  <div className=\"bg-error-50 border border-error-200 rounded-lg p-4 mt-6\">\n                    <div className=\"flex\">\n                      <div className=\"text-error-400\">⚠️</div>\n                      <div className=\"ml-3\">\n                        <h3 className=\"text-sm font-medium text-error-800\">Error</h3>\n                        <p className=\"text-sm text-error-700 mt-1\">{error}</p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </>\n            )}\n\n            {/* Analytics View */}\n            {viewMode === 'analytics' && (\n              <AnalyticsDashboard />\n            )}\n\n            {/* Scheduler View */}\n            {viewMode === 'scheduler' && (\n              <ContentScheduler />\n            )}\n\n            {/* Events View */}\n            {viewMode === 'events' && (\n              <div className=\"card p-12 text-center\">\n                <div className=\"text-6xl mb-4\">🎪</div>\n                <h3 className=\"text-lg font-medium text-secondary-900 mb-2\">Events Coming Soon</h3>\n                <p className=\"text-secondary-600\">\n                  Political events and campaign management features will be available soon.\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <TrendingSidebar />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SocialFeed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAO,6BAA6B;AACpC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAEhD,MAAM;IAAE2C,IAAI;IAAEC;EAAgB,CAAC,GAAGzC,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAE0C,MAAM;IAAEC;EAAY,CAAC,GAAG1C,YAAY,CAAC,CAAC;;EAE9C;EACA,MAAM2C,UAAU,GAAG7C,WAAW,CAAC,OAAO8C,OAAO,GAAG,CAAC,EAAEC,UAAU,GAAGxB,MAAM,EAAEyB,KAAK,GAAG,KAAK,KAAK;IACxF,IAAI;MACF,IAAIF,OAAO,KAAK,CAAC,EAAE1B,UAAU,CAAC,IAAI,CAAC;MAEnC,MAAM6B,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,WAAW,EAAE;QAC5CC,MAAM,EAAE;UACN1B,IAAI,EAAEqB,OAAO;UACbM,KAAK,EAAE,EAAE;UACT7B,MAAM,EAAEwB;QACV;MACF,CAAC,CAAC;MAEF,MAAMM,QAAQ,GAAGJ,QAAQ,CAACK,IAAI,CAACrC,KAAK;MAEpC,IAAI+B,KAAK,IAAIF,OAAO,KAAK,CAAC,EAAE;QAC1B5B,QAAQ,CAACmC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLnC,QAAQ,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGF,QAAQ,CAAC,CAAC;MAC1C;MAEAzB,UAAU,CAACyB,QAAQ,CAACG,MAAM,KAAK,EAAE,CAAC;MAClC9B,OAAO,CAACoB,OAAO,CAAC;IAElB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;MACjBU,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;;EAEZ;EACAxB,SAAS,CAAC,MAAM;IACd8C,UAAU,CAAC,CAAC,EAAEtB,MAAM,EAAE,IAAI,CAAC;EAC7B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,MAAM,EAAE;IAEb,MAAMe,aAAa,GAAIC,IAAI,IAAK;MAC9BzC,QAAQ,CAACqC,IAAI,IAAI,CAACI,IAAI,EAAE,GAAGJ,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,MAAMK,gBAAgB,GAAIC,WAAW,IAAK;MACxC3C,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKF,WAAW,CAACE,EAAE,GAAG;QAAE,GAAGJ,IAAI;QAAE,GAAGE;MAAY,CAAC,GAAGF,IAC7D,CAAC,CAAC;IACJ,CAAC;IAED,MAAMK,gBAAgB,GAAIC,MAAM,IAAK;MACnC/C,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAACoC,IAAI,IAAIA,IAAI,CAACI,EAAE,KAAKE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAEDtB,MAAM,CAACuB,EAAE,CAAC,UAAU,EAAER,aAAa,CAAC;IACpCf,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAEN,gBAAgB,CAAC;IAC3CjB,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAEF,gBAAgB,CAAC;IAE3C,OAAO,MAAM;MACXrB,MAAM,CAACwB,GAAG,CAAC,UAAU,CAAC;MACtBxB,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;MAC1BxB,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMyB,kBAAkB,GAAIC,SAAS,IAAK;IACxC7C,SAAS,CAAC6C,SAAS,CAAC;IACpB3C,OAAO,CAAC,CAAC,CAAC;IACVR,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAMoD,aAAa,GAAGA,CAAA,KAAM;IAC1BxC,aAAa,CAAC,IAAI,CAAC;IACnBe,UAAU,CAAC,CAAC,EAAEtB,MAAM,EAAE,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgD,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACpD,OAAO,IAAIQ,OAAO,EAAE;MACvBkB,UAAU,CAACpB,IAAI,GAAG,CAAC,EAAEF,MAAM,EAAE,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAIC,OAAO,IAAK;IACrCvD,QAAQ,CAACqC,IAAI,IAAI,CAACkB,OAAO,EAAE,GAAGlB,IAAI,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMmB,cAAc,GAAG,MAAAA,CAAOT,MAAM,EAAEU,QAAQ,KAAK;IACjD,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMvC,KAAK,CAACiD,IAAI,CAAC,aAAaM,MAAM,OAAO,EAAE;QAC5DW,SAAS,EAAED;MACb,CAAC,CAAC;MAEFzD,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKE,MAAM,GACd;QACE,GAAGN,IAAI;QACPkB,OAAO,EAAE5B,QAAQ,CAACK,IAAI,CAACuB,OAAO;QAC9BC,SAAS,EAAE7B,QAAQ,CAACK,IAAI,CAACwB,SAAS;QAClCC,SAAS,EAAE9B,QAAQ,CAACK,IAAI,CAACyB;MAC3B,CAAC,GACDpB,IACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAM2D,kBAAkB,GAAG,MAAAA,CAAOf,MAAM,EAAEgB,YAAY,KAAK;IACzD,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMvC,KAAK,CAACiD,IAAI,CAAC,aAAaM,MAAM,QAAQ,EAAE;QAC7DiB,aAAa,EAAED;MACjB,CAAC,CAAC;MAEF/D,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACO,GAAG,CAACH,IAAI,IAC5BA,IAAI,CAACI,EAAE,KAAKE,MAAM,GACd;QACE,GAAGN,IAAI;QACPwB,SAAS,EAAElC,QAAQ,CAACK,IAAI,CAAC6B;MAC3B,CAAC,GACDxB,IACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,IAAI,CAACqB,eAAe,EAAE;IACpB,oBACE9B,OAAA;MAAKwE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAIwE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1F7E,OAAA;UAAGwE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAoE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1G7E,OAAA;UAAG8E,IAAI,EAAC,QAAQ;UAACN,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAE3F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAE3CzE,OAAA,CAACN,kBAAkB;MACjByB,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA,WAAY;MACzBY,WAAW,EAAEA,WAAY;MACzBH,IAAI,EAAEA;IAAK;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEF7E,OAAA;MAAKwE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CzE,OAAA;QAAKwE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAGpDzE,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,GAG3BtD,QAAQ,KAAK,MAAM,iBAClBnB,OAAA,CAAAE,SAAA;YAAAuE,QAAA,gBAEEzE,OAAA;cAAKwE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BzE,OAAA;gBAAKwE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDzE,OAAA;kBAAKwE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCzE,OAAA;oBAAIwE,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,eACnDzE,OAAA;sBAAMwE,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAEjE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL7E,OAAA;oBAAKwE,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCzE,OAAA;sBAAKwE,SAAS,EAAE,wBAAwBxC,WAAW,GAAG,gBAAgB,GAAG,cAAc;oBAAiB;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/G7E,OAAA;sBAAMwE,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,EACrDzC,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7E,OAAA;kBAAKwE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCzE,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMzD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;oBAC5DmD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACnC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7E,OAAA;oBACE+E,OAAO,EAAErB,aAAc;oBACvBsB,QAAQ,EAAE/D,UAAW;oBACrBuD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GAEjCxD,UAAU,GAAG,IAAI,GAAG,GAAG,EAAC,UAC3B;kBAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLxD,mBAAmB,iBAClBrB,OAAA,CAACL,eAAe;gBACd4B,cAAc,EAAEA,cAAe;gBAC/BC,iBAAiB,EAAEA,iBAAkB;gBACrCC,SAAS,EAAEA,SAAU;gBACrBC,YAAY,EAAEA,YAAa;gBAC3BC,MAAM,EAAEA,MAAO;gBACfC,SAAS,EAAEA,SAAU;gBACrBqD,cAAc,EAAEA,CAAA,KAAM;kBACpBhD,UAAU,CAAC,CAAC;gBACd,CAAE;gBACFiD,cAAc,EAAEA,CAAA,KAAM;kBACpB1D,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,YAAY,CAAC,KAAK,CAAC;kBACnBE,SAAS,CAAC,QAAQ,CAAC;kBACnBK,UAAU,CAAC,CAAC;gBACd;cAAE;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,eAGD7E,OAAA;gBAAKwE,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAC5D,CACC;kBAAEU,GAAG,EAAE,KAAK;kBAAEC,KAAK,EAAE,cAAc;kBAAEC,KAAK,EAAEhF,KAAK,CAACuC;gBAAO,CAAC,EAC1D;kBAAEuC,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,cAAc;kBAAEC,KAAK,EAAE;gBAAE,CAAC,EACrD;kBAAEF,GAAG,EAAE,UAAU;kBAAEC,KAAK,EAAE,aAAa;kBAAEC,KAAK,EAAE;gBAAE,CAAC,CACpD,CAACnC,GAAG,CAACoC,GAAG,iBACPtF,OAAA;kBAEE+E,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC8B,GAAG,CAACH,GAAG,CAAE;kBAC3CX,SAAS,EAAE,yGACT7D,MAAM,KAAK2E,GAAG,CAACH,GAAG,GACd,qCAAqC,GACrC,mEAAmE,EACtE;kBAAAV,QAAA,gBAEHzE,OAAA;oBAAAyE,QAAA,EAAOa,GAAG,CAACF;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACvBS,GAAG,CAACD,KAAK,GAAG,CAAC,iBACZrF,OAAA;oBAAMwE,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,EACnFa,GAAG,CAACD;kBAAK;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACP;gBAAA,GAbIS,GAAG,CAACH,GAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcN,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7E,OAAA,CAACR,UAAU;cAAC+F,aAAa,EAAE3B;YAAkB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGhD7E,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBlE,OAAO,IAAIF,KAAK,CAACuC,MAAM,KAAK,CAAC,gBAC5B5C,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACtC,GAAG,CAAC,CAACuC,CAAC,EAAEC,CAAC,kBACtB1F,OAAA;kBAAawE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAC7CzE,OAAA;oBAAKwE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CzE,OAAA;sBAAKwE,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/D7E,OAAA;sBAAKwE,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrBzE,OAAA;wBAAKwE,SAAS,EAAC;sBAAyC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/D7E,OAAA;wBAAKwE,SAAS,EAAC;sBAAoC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBzE,OAAA;sBAAKwE,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpD7E,OAAA;sBAAKwE,SAAS,EAAC;oBAAoC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA,GAXEa,CAAC;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYN,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJxE,KAAK,CAACuC,MAAM,KAAK,CAAC,gBACpB5C,OAAA;gBAAKwE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCzE,OAAA;kBAAKwE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvC7E,OAAA;kBAAIwE,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E7E,OAAA;kBAAGwE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EACnC9D,MAAM,KAAK,WAAW,GACnB,2CAA2C,GAC3C;gBAA+C;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAElD,CAAC,EACHlE,MAAM,KAAK,WAAW,iBACrBX,OAAA;kBACE+E,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC,KAAK,CAAE;kBACzCgB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAEN7E,OAAA,CAAAE,SAAA;gBAAAuE,QAAA,GACGpE,KAAK,CAAC6C,GAAG,CAACH,IAAI,iBACb/C,OAAA,CAACT,QAAQ;kBAEPwD,IAAI,EAAEA,IAAK;kBACX4C,MAAM,EAAE7B,cAAe;kBACvB8B,UAAU,EAAExB;gBAAmB,GAH1BrB,IAAI,CAACI,EAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIb,CACF,CAAC,EAGD9D,OAAO,iBACNf,OAAA;kBAAKwE,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvCzE,OAAA;oBACE+E,OAAO,EAAEpB,QAAS;oBAClBqB,QAAQ,EAAEzE,OAAQ;oBAClBiE,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAEnClE,OAAO,gBACNP,OAAA,CAAAE,SAAA;sBAAAuE,QAAA,gBACEzE,OAAA;wBAAKwE,SAAS,EAAC;sBAAgF;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,cAExG;oBAAA,eAAE,CAAC,GAEH;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA,eACD;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAELpE,KAAK,iBACJT,OAAA;cAAKwE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eACtEzE,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzE,OAAA;kBAAKwE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxC7E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBzE,OAAA;oBAAIwE,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7D7E,OAAA;oBAAGwE,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEhE;kBAAK;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD,CACH,EAGA1D,QAAQ,KAAK,WAAW,iBACvBnB,OAAA,CAACH,kBAAkB;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACtB,EAGA1D,QAAQ,KAAK,WAAW,iBACvBnB,OAAA,CAACJ,gBAAgB;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACpB,EAGA1D,QAAQ,KAAK,QAAQ,iBACpBnB,OAAA;YAAKwE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzE,OAAA;cAAKwE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvC7E,OAAA;cAAIwE,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnF7E,OAAA;cAAGwE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzE,OAAA,CAACP,eAAe;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CApYID,UAAU;EAAA,QAcoBd,OAAO,EACTC,YAAY;AAAA;AAAAuG,EAAA,GAfxC1F,UAAU;AAsYhB,eAAeA,UAAU;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}