const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { userQueries } = require('./database');

class WebSocketManager {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production'
          ? ['https://your-frontend-domain.com']
          : ['http://localhost:3000', 'http://localhost:3001'],
        methods: ['GET', 'POST']
      }
    });

    this.activeDebates = new Map(); // debateId -> Set of socketIds
    this.userSockets = new Map(); // userId -> socketId
    this.socketUsers = new Map(); // socketId -> userId

    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`🔌 User connected: ${socket.id}`);

      // Authentication middleware for socket
      socket.on('authenticate', async (token) => {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          const user = await userQueries.findById(decoded.userId);
          
          if (user) {
            socket.userId = user.id;
            socket.user = user;
            this.userSockets.set(user.id, socket.id);
            this.socketUsers.set(socket.id, user.id);
            
            socket.emit('authenticated', { 
              success: true, 
              user: { id: user.id, username: user.username, role: user.role }
            });
            console.log(`✅ User authenticated: ${user.username} (${socket.id})`);
          } else {
            socket.emit('authenticated', { success: false, error: 'User not found' });
          }
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Invalid token' });
        }
      });

      // Join debate room
      socket.on('join-debate', (debateId) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        socket.join(`debate-${debateId}`);
        
        if (!this.activeDebates.has(debateId)) {
          this.activeDebates.set(debateId, new Set());
        }
        this.activeDebates.get(debateId).add(socket.id);

        // Notify others in the debate
        socket.to(`debate-${debateId}`).emit('user-joined-debate', {
          userId: socket.userId,
          username: socket.user.username,
          timestamp: new Date().toISOString()
        });

        // Send current participant count
        const participantCount = this.activeDebates.get(debateId).size;
        this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
          debateId,
          count: participantCount
        });

        console.log(`👥 User ${socket.user.username} joined debate ${debateId}`);
      });

      // Leave debate room
      socket.on('leave-debate', (debateId) => {
        socket.leave(`debate-${debateId}`);
        
        if (this.activeDebates.has(debateId)) {
          this.activeDebates.get(debateId).delete(socket.id);
          
          if (this.activeDebates.get(debateId).size === 0) {
            this.activeDebates.delete(debateId);
          } else {
            // Update participant count
            const participantCount = this.activeDebates.get(debateId).size;
            this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
              debateId,
              count: participantCount
            });
          }
        }

        // Notify others
        socket.to(`debate-${debateId}`).emit('user-left-debate', {
          userId: socket.userId,
          username: socket.user?.username,
          timestamp: new Date().toISOString()
        });

        console.log(`👋 User ${socket.user?.username} left debate ${debateId}`);
      });

      // Real-time chat for debates
      socket.on('debate-chat-message', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const message = {
          id: Date.now().toString(),
          debateId: data.debateId,
          userId: socket.userId,
          username: socket.user.username,
          message: data.message,
          timestamp: new Date().toISOString()
        };

        // Broadcast to all users in the debate room
        this.io.to(`debate-${data.debateId}`).emit('debate-chat-message', message);
        
        console.log(`💬 Chat message in debate ${data.debateId}: ${socket.user.username}: ${data.message}`);
      });

      // Real-time polls for debates
      socket.on('debate-poll-vote', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const voteData = {
          pollId: data.pollId,
          debateId: data.debateId,
          userId: socket.userId,
          username: socket.user.username,
          option: data.option,
          timestamp: new Date().toISOString()
        };

        // Broadcast vote to all users in the debate room
        this.io.to(`debate-${data.debateId}`).emit('debate-poll-vote', voteData);
        
        console.log(`🗳️ Poll vote in debate ${data.debateId}: ${socket.user.username} voted ${data.option}`);
      });

      // Join discussion thread for real-time comments
      socket.on('join-discussion', (threadId) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        socket.join(`discussion-${threadId}`);
        console.log(`💭 User ${socket.user.username} joined discussion ${threadId}`);
      });

      // Leave discussion thread
      socket.on('leave-discussion', (threadId) => {
        socket.leave(`discussion-${threadId}`);
        console.log(`👋 User ${socket.user?.username} left discussion ${threadId}`);
      });

      // Real-time comment notifications
      socket.on('new-comment', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const commentData = {
          threadId: data.threadId,
          commentId: data.commentId,
          userId: socket.userId,
          username: socket.user.username,
          content: data.content,
          timestamp: new Date().toISOString()
        };

        // Broadcast to all users in the discussion thread
        socket.to(`discussion-${data.threadId}`).emit('new-comment', commentData);
        
        console.log(`💬 New comment in discussion ${data.threadId}: ${socket.user.username}`);
      });

      // Typing indicators for discussions
      socket.on('typing-start', (data) => {
        if (!socket.userId) return;
        
        socket.to(`discussion-${data.threadId}`).emit('user-typing', {
          userId: socket.userId,
          username: socket.user.username,
          threadId: data.threadId
        });
      });

      socket.on('typing-stop', (data) => {
        if (!socket.userId) return;
        
        socket.to(`discussion-${data.threadId}`).emit('user-stopped-typing', {
          userId: socket.userId,
          threadId: data.threadId
        });
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`🔌 User disconnected: ${socket.id}`);
        
        if (socket.userId) {
          this.userSockets.delete(socket.userId);
          this.socketUsers.delete(socket.id);

          // Remove from all active debates
          for (const [debateId, participants] of this.activeDebates.entries()) {
            if (participants.has(socket.id)) {
              participants.delete(socket.id);
              
              // Update participant count
              const participantCount = participants.size;
              this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
                debateId,
                count: participantCount
              });

              // Notify others
              socket.to(`debate-${debateId}`).emit('user-left-debate', {
                userId: socket.userId,
                username: socket.user?.username,
                timestamp: new Date().toISOString()
              });

              if (participants.size === 0) {
                this.activeDebates.delete(debateId);
              }
            }
          }
        }
      });
    });
  }

  // Method to send notifications to specific users
  sendNotificationToUser(userId, notification) {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  // Method to broadcast debate status updates
  broadcastDebateStatusUpdate(debateId, status) {
    this.io.to(`debate-${debateId}`).emit('debate-status-updated', {
      debateId,
      status,
      timestamp: new Date().toISOString()
    });
  }

  // Method to get active debate participants
  getActiveDebateParticipants(debateId) {
    const participants = this.activeDebates.get(debateId);
    return participants ? participants.size : 0;
  }

  // Method to get all active debates
  getActiveDebates() {
    const debates = {};
    for (const [debateId, participants] of this.activeDebates.entries()) {
      debates[debateId] = participants.size;
    }
    return debates;
  }
}

module.exports = WebSocketManager;
