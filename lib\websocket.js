const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { userQueries } = require('./database');

class WebSocketManager {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production'
          ? ['https://your-frontend-domain.com']
          : ['http://localhost:3000', 'http://localhost:3001'],
        methods: ['GET', 'POST']
      }
    });

    this.activeDebates = new Map(); // debateId -> Set of socketIds
    this.userSockets = new Map(); // userId -> socketId
    this.socketUsers = new Map(); // socketId -> userId
    this.videoDebates = new Map(); // debateId -> Map of socketId -> userInfo
    this.speakingQueues = new Map(); // debateId -> Array of users waiting to speak
    this.currentSpeakers = new Map(); // debateId -> current speaker info
    this.recordingStatus = new Map(); // debateId -> recording status

    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`🔌 User connected: ${socket.id}`);

      // Authentication middleware for socket
      socket.on('authenticate', async (token) => {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          const user = await userQueries.findById(decoded.userId);
          
          if (user) {
            socket.userId = user.id;
            socket.user = user;
            this.userSockets.set(user.id, socket.id);
            this.socketUsers.set(socket.id, user.id);
            
            socket.emit('authenticated', { 
              success: true, 
              user: { id: user.id, username: user.username, role: user.role }
            });
            console.log(`✅ User authenticated: ${user.username} (${socket.id})`);
          } else {
            socket.emit('authenticated', { success: false, error: 'User not found' });
          }
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Invalid token' });
        }
      });

      // Join debate room
      socket.on('join-debate', (debateId) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        socket.join(`debate-${debateId}`);
        
        if (!this.activeDebates.has(debateId)) {
          this.activeDebates.set(debateId, new Set());
        }
        this.activeDebates.get(debateId).add(socket.id);

        // Notify others in the debate
        socket.to(`debate-${debateId}`).emit('user-joined-debate', {
          userId: socket.userId,
          username: socket.user.username,
          timestamp: new Date().toISOString()
        });

        // Send current participant count
        const participantCount = this.activeDebates.get(debateId).size;
        this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
          debateId,
          count: participantCount
        });

        console.log(`👥 User ${socket.user.username} joined debate ${debateId}`);
      });

      // Leave debate room
      socket.on('leave-debate', (debateId) => {
        socket.leave(`debate-${debateId}`);
        
        if (this.activeDebates.has(debateId)) {
          this.activeDebates.get(debateId).delete(socket.id);
          
          if (this.activeDebates.get(debateId).size === 0) {
            this.activeDebates.delete(debateId);
          } else {
            // Update participant count
            const participantCount = this.activeDebates.get(debateId).size;
            this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
              debateId,
              count: participantCount
            });
          }
        }

        // Notify others
        socket.to(`debate-${debateId}`).emit('user-left-debate', {
          userId: socket.userId,
          username: socket.user?.username,
          timestamp: new Date().toISOString()
        });

        console.log(`👋 User ${socket.user?.username} left debate ${debateId}`);
      });

      // Real-time chat for debates
      socket.on('debate-chat-message', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const message = {
          id: Date.now().toString(),
          debateId: data.debateId,
          userId: socket.userId,
          username: socket.user.username,
          message: data.message,
          timestamp: new Date().toISOString()
        };

        // Broadcast to all users in the debate room
        this.io.to(`debate-${data.debateId}`).emit('debate-chat-message', message);
        
        console.log(`💬 Chat message in debate ${data.debateId}: ${socket.user.username}: ${data.message}`);
      });

      // Real-time polls for debates
      socket.on('debate-poll-vote', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const voteData = {
          pollId: data.pollId,
          debateId: data.debateId,
          userId: socket.userId,
          username: socket.user.username,
          option: data.option,
          timestamp: new Date().toISOString()
        };

        // Broadcast vote to all users in the debate room
        this.io.to(`debate-${data.debateId}`).emit('debate-poll-vote', voteData);
        
        console.log(`🗳️ Poll vote in debate ${data.debateId}: ${socket.user.username} voted ${data.option}`);
      });

      // Join discussion thread for real-time comments
      socket.on('join-discussion', (threadId) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        socket.join(`discussion-${threadId}`);
        console.log(`💭 User ${socket.user.username} joined discussion ${threadId}`);
      });

      // Leave discussion thread
      socket.on('leave-discussion', (threadId) => {
        socket.leave(`discussion-${threadId}`);
        console.log(`👋 User ${socket.user?.username} left discussion ${threadId}`);
      });

      // Real-time comment notifications
      socket.on('new-comment', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const commentData = {
          threadId: data.threadId,
          commentId: data.commentId,
          userId: socket.userId,
          username: socket.user.username,
          content: data.content,
          timestamp: new Date().toISOString()
        };

        // Broadcast to all users in the discussion thread
        socket.to(`discussion-${data.threadId}`).emit('new-comment', commentData);
        
        console.log(`💬 New comment in discussion ${data.threadId}: ${socket.user.username}`);
      });

      // Typing indicators for discussions
      socket.on('typing-start', (data) => {
        if (!socket.userId) return;
        
        socket.to(`discussion-${data.threadId}`).emit('user-typing', {
          userId: socket.userId,
          username: socket.user.username,
          threadId: data.threadId
        });
      });

      socket.on('typing-stop', (data) => {
        if (!socket.userId) return;

        socket.to(`discussion-${data.threadId}`).emit('user-stopped-typing', {
          userId: socket.userId,
          threadId: data.threadId
        });
      });

      // Video Debate Event Handlers
      socket.on('join-video-debate', (data) => {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const { debateId } = data;
        socket.join(`video-debate-${debateId}`);

        // Initialize video debate room if it doesn't exist
        if (!this.videoDebates.has(debateId)) {
          this.videoDebates.set(debateId, new Map());
          this.speakingQueues.set(debateId, []);
          this.currentSpeakers.set(debateId, null);
          this.recordingStatus.set(debateId, 'stopped');
        }

        // Add user to video debate
        const userInfo = {
          userId: socket.userId,
          username: socket.user.username,
          socketId: socket.id,
          videoEnabled: true,
          audioEnabled: true,
          isScreenSharing: false
        };

        this.videoDebates.get(debateId).set(socket.id, userInfo);

        // Notify existing participants about new user
        socket.to(`video-debate-${debateId}`).emit('user-joined-video', {
          socketId: socket.id,
          userId: socket.userId,
          username: socket.user.username
        });

        // Send current participants to new user
        const participants = Array.from(this.videoDebates.get(debateId).values())
          .filter(p => p.socketId !== socket.id);

        socket.emit('existing-video-participants', participants);

        // Send current speaking queue and speaker
        socket.emit('speaking-queue-updated', this.speakingQueues.get(debateId));
        socket.emit('current-speaker-changed', this.currentSpeakers.get(debateId));
        socket.emit('recording-status-changed', this.recordingStatus.get(debateId));

        console.log(`🎥 User ${socket.user.username} joined video debate ${debateId}`);
      });

      socket.on('leave-video-debate', (data) => {
        const { debateId } = data;
        this.handleVideoDebateLeave(socket, debateId);
      });

      socket.on('video-signal', (data) => {
        socket.to(data.to).emit('video-signal', data);
      });

      socket.on('video-toggle', (data) => {
        if (!socket.userId) return;

        const { debateId, enabled } = data;
        const videoDebate = this.videoDebates.get(debateId);

        if (videoDebate && videoDebate.has(socket.id)) {
          videoDebate.get(socket.id).videoEnabled = enabled;

          socket.to(`video-debate-${debateId}`).emit('video-toggled', {
            userId: socket.userId,
            enabled
          });
        }
      });

      socket.on('audio-toggle', (data) => {
        if (!socket.userId) return;

        const { debateId, enabled } = data;
        const videoDebate = this.videoDebates.get(debateId);

        if (videoDebate && videoDebate.has(socket.id)) {
          videoDebate.get(socket.id).audioEnabled = enabled;

          socket.to(`video-debate-${debateId}`).emit('audio-toggled', {
            userId: socket.userId,
            enabled
          });
        }
      });

      socket.on('screen-share-started', (data) => {
        if (!socket.userId) return;

        const { debateId } = data;
        const videoDebate = this.videoDebates.get(debateId);

        if (videoDebate && videoDebate.has(socket.id)) {
          videoDebate.get(socket.id).isScreenSharing = true;

          this.io.to(`video-debate-${debateId}`).emit('screen-share-started', {
            userId: socket.userId,
            username: socket.user.username
          });
        }
      });

      socket.on('screen-share-stopped', (data) => {
        if (!socket.userId) return;

        const { debateId } = data;
        const videoDebate = this.videoDebates.get(debateId);

        if (videoDebate && videoDebate.has(socket.id)) {
          videoDebate.get(socket.id).isScreenSharing = false;

          this.io.to(`video-debate-${debateId}`).emit('screen-share-stopped', {
            userId: socket.userId,
            username: socket.user.username
          });
        }
      });

      socket.on('raise-hand', (data) => {
        if (!socket.userId) return;

        const { debateId, username } = data;
        const speakingQueue = this.speakingQueues.get(debateId);

        if (speakingQueue) {
          // Check if user is not already in queue
          const alreadyInQueue = speakingQueue.some(item => item.userId === socket.userId);

          if (!alreadyInQueue) {
            const queueItem = {
              userId: socket.userId,
              username,
              timestamp: new Date().toISOString()
            };

            speakingQueue.push(queueItem);

            this.io.to(`video-debate-${debateId}`).emit('hand-raised', queueItem);
            this.io.to(`video-debate-${debateId}`).emit('speaking-queue-updated', speakingQueue);
          }
        }
      });

      socket.on('lower-hand', (data) => {
        if (!socket.userId) return;

        const { debateId } = data;
        const speakingQueue = this.speakingQueues.get(debateId);

        if (speakingQueue) {
          const index = speakingQueue.findIndex(item => item.userId === socket.userId);

          if (index !== -1) {
            speakingQueue.splice(index, 1);

            this.io.to(`video-debate-${debateId}`).emit('hand-lowered', { userId: socket.userId });
            this.io.to(`video-debate-${debateId}`).emit('speaking-queue-updated', speakingQueue);
          }
        }
      });

      socket.on('give-floor', (data) => {
        if (!socket.userId) return;

        const { debateId, userId } = data;

        // Check if user is moderator (this should be validated against database)
        const speakingQueue = this.speakingQueues.get(debateId);
        const videoDebate = this.videoDebates.get(debateId);

        if (speakingQueue && videoDebate) {
          // Find the user in the queue
          const userIndex = speakingQueue.findIndex(item => item.userId === userId);

          if (userIndex !== -1) {
            const speaker = speakingQueue[userIndex];
            speakingQueue.splice(userIndex, 1);

            // Set as current speaker
            this.currentSpeakers.set(debateId, speaker);

            this.io.to(`video-debate-${debateId}`).emit('current-speaker-changed', speaker);
            this.io.to(`video-debate-${debateId}`).emit('speaking-queue-updated', speakingQueue);

            console.log(`🎤 Floor given to ${speaker.username} in debate ${debateId}`);
          }
        }
      });

      socket.on('recording-started', (data) => {
        if (!socket.userId) return;

        const { debateId } = data;
        this.recordingStatus.set(debateId, 'recording');

        this.io.to(`video-debate-${debateId}`).emit('recording-status-changed', 'recording');

        console.log(`🔴 Recording started for debate ${debateId} by ${socket.user.username}`);
      });

      socket.on('recording-stopped', (data) => {
        if (!socket.userId) return;

        const { debateId } = data;
        this.recordingStatus.set(debateId, 'stopped');

        this.io.to(`video-debate-${debateId}`).emit('recording-status-changed', 'stopped');

        console.log(`⏹️ Recording stopped for debate ${debateId} by ${socket.user.username}`);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`🔌 User disconnected: ${socket.id}`);
        
        if (socket.userId) {
          this.userSockets.delete(socket.userId);
          this.socketUsers.delete(socket.id);

          // Remove from all active debates
          for (const [debateId, participants] of this.activeDebates.entries()) {
            if (participants.has(socket.id)) {
              participants.delete(socket.id);

              // Update participant count
              const participantCount = participants.size;
              this.io.to(`debate-${debateId}`).emit('participant-count-updated', {
                debateId,
                count: participantCount
              });

              // Notify others
              socket.to(`debate-${debateId}`).emit('user-left-debate', {
                userId: socket.userId,
                username: socket.user?.username,
                timestamp: new Date().toISOString()
              });

              if (participants.size === 0) {
                this.activeDebates.delete(debateId);
              }
            }
          }

          // Remove from all video debates
          for (const [debateId, videoParticipants] of this.videoDebates.entries()) {
            if (videoParticipants.has(socket.id)) {
              this.handleVideoDebateLeave(socket, debateId);
            }
          }
        }
      });
    });
  }

  // Method to send notifications to specific users
  sendNotificationToUser(userId, notification) {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  // Method to broadcast debate status updates
  broadcastDebateStatusUpdate(debateId, status) {
    this.io.to(`debate-${debateId}`).emit('debate-status-updated', {
      debateId,
      status,
      timestamp: new Date().toISOString()
    });
  }

  // Method to get active debate participants
  getActiveDebateParticipants(debateId) {
    const participants = this.activeDebates.get(debateId);
    return participants ? participants.size : 0;
  }

  // Method to get all active debates
  getActiveDebates() {
    const debates = {};
    for (const [debateId, participants] of this.activeDebates.entries()) {
      debates[debateId] = participants.size;
    }
    return debates;
  }

  // Helper method to handle video debate leave
  handleVideoDebateLeave(socket, debateId) {
    const videoDebate = this.videoDebates.get(debateId);

    if (videoDebate && videoDebate.has(socket.id)) {
      const userInfo = videoDebate.get(socket.id);
      videoDebate.delete(socket.id);

      // Remove from speaking queue if present
      const speakingQueue = this.speakingQueues.get(debateId);
      if (speakingQueue) {
        const index = speakingQueue.findIndex(item => item.userId === socket.userId);
        if (index !== -1) {
          speakingQueue.splice(index, 1);
          this.io.to(`video-debate-${debateId}`).emit('speaking-queue-updated', speakingQueue);
        }
      }

      // Clear current speaker if it's this user
      const currentSpeaker = this.currentSpeakers.get(debateId);
      if (currentSpeaker && currentSpeaker.userId === socket.userId) {
        this.currentSpeakers.set(debateId, null);
        this.io.to(`video-debate-${debateId}`).emit('current-speaker-changed', null);
      }

      // Notify others about user leaving
      socket.to(`video-debate-${debateId}`).emit('user-left-video', {
        socketId: socket.id,
        userId: socket.userId,
        username: userInfo.username
      });

      // Leave the room
      socket.leave(`video-debate-${debateId}`);

      // Clean up empty video debate rooms
      if (videoDebate.size === 0) {
        this.videoDebates.delete(debateId);
        this.speakingQueues.delete(debateId);
        this.currentSpeakers.delete(debateId);
        this.recordingStatus.delete(debateId);
      }

      console.log(`🎥 User ${userInfo.username} left video debate ${debateId}`);
    }
  }

  // Method to get video debate participants
  getVideoDebateParticipants(debateId) {
    const videoDebate = this.videoDebates.get(debateId);
    return videoDebate ? Array.from(videoDebate.values()) : [];
  }

  // Method to get speaking queue for a debate
  getSpeakingQueue(debateId) {
    return this.speakingQueues.get(debateId) || [];
  }

  // Method to get current speaker for a debate
  getCurrentSpeaker(debateId) {
    return this.currentSpeakers.get(debateId);
  }

  // Method to get recording status for a debate
  getRecordingStatus(debateId) {
    return this.recordingStatus.get(debateId) || 'stopped';
  }
}

module.exports = WebSocketManager;
