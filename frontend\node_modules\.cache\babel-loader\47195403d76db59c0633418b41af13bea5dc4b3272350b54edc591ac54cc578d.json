{"ast": null, "code": "/**\n * Module exports.\n */\n\nmodule.exports = deprecate;\n\n/**\n * Mark that a method should not be used.\n * Returns a modified function which warns once by default.\n *\n * If `localStorage.noDeprecation = true` is set, then it is a no-op.\n *\n * If `localStorage.throwDeprecation = true` is set, then deprecated functions\n * will throw an Error when invoked.\n *\n * If `localStorage.traceDeprecation = true` is set, then deprecated functions\n * will invoke `console.trace()` instead of `console.error()`.\n *\n * @param {Function} fn - the function to deprecate\n * @param {String} msg - the string to print to the console when `fn` is invoked\n * @returns {Function} a new \"deprecated\" version of `fn`\n * @api public\n */\n\nfunction deprecate(fn, msg) {\n  if (config('noDeprecation')) {\n    return fn;\n  }\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (config('throwDeprecation')) {\n        throw new Error(msg);\n      } else if (config('traceDeprecation')) {\n        console.trace(msg);\n      } else {\n        console.warn(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n  return deprecated;\n}\n\n/**\n * Checks `localStorage` for boolean values for the given `name`.\n *\n * @param {String} name\n * @returns {Boolean}\n * @api private\n */\n\nfunction config(name) {\n  // accessing global.localStorage can trigger a DOMException in sandboxed iframes\n  try {\n    if (!global.localStorage) return false;\n  } catch (_) {\n    return false;\n  }\n  var val = global.localStorage[name];\n  if (null == val) return false;\n  return String(val).toLowerCase() === 'true';\n}", "map": {"version": 3, "names": ["module", "exports", "deprecate", "fn", "msg", "config", "warned", "deprecated", "Error", "console", "trace", "warn", "apply", "arguments", "name", "global", "localStorage", "_", "val", "String", "toLowerCase"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/util-deprecate/browser.js"], "sourcesContent": ["\n/**\n * Module exports.\n */\n\nmodule.exports = deprecate;\n\n/**\n * Mark that a method should not be used.\n * Returns a modified function which warns once by default.\n *\n * If `localStorage.noDeprecation = true` is set, then it is a no-op.\n *\n * If `localStorage.throwDeprecation = true` is set, then deprecated functions\n * will throw an Error when invoked.\n *\n * If `localStorage.traceDeprecation = true` is set, then deprecated functions\n * will invoke `console.trace()` instead of `console.error()`.\n *\n * @param {Function} fn - the function to deprecate\n * @param {String} msg - the string to print to the console when `fn` is invoked\n * @returns {Function} a new \"deprecated\" version of `fn`\n * @api public\n */\n\nfunction deprecate (fn, msg) {\n  if (config('noDeprecation')) {\n    return fn;\n  }\n\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (config('throwDeprecation')) {\n        throw new Error(msg);\n      } else if (config('traceDeprecation')) {\n        console.trace(msg);\n      } else {\n        console.warn(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n}\n\n/**\n * Checks `localStorage` for boolean values for the given `name`.\n *\n * @param {String} name\n * @returns {Boolean}\n * @api private\n */\n\nfunction config (name) {\n  // accessing global.localStorage can trigger a DOMException in sandboxed iframes\n  try {\n    if (!global.localStorage) return false;\n  } catch (_) {\n    return false;\n  }\n  var val = global.localStorage[name];\n  if (null == val) return false;\n  return String(val).toLowerCase() === 'true';\n}\n"], "mappings": "AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAGC,SAAS;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAASA,CAAEC,EAAE,EAAEC,GAAG,EAAE;EAC3B,IAAIC,MAAM,CAAC,eAAe,CAAC,EAAE;IAC3B,OAAOF,EAAE;EACX;EAEA,IAAIG,MAAM,GAAG,KAAK;EAClB,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACD,MAAM,EAAE;MACX,IAAID,MAAM,CAAC,kBAAkB,CAAC,EAAE;QAC9B,MAAM,IAAIG,KAAK,CAACJ,GAAG,CAAC;MACtB,CAAC,MAAM,IAAIC,MAAM,CAAC,kBAAkB,CAAC,EAAE;QACrCI,OAAO,CAACC,KAAK,CAACN,GAAG,CAAC;MACpB,CAAC,MAAM;QACLK,OAAO,CAACE,IAAI,CAACP,GAAG,CAAC;MACnB;MACAE,MAAM,GAAG,IAAI;IACf;IACA,OAAOH,EAAE,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAClC;EAEA,OAAON,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASF,MAAMA,CAAES,IAAI,EAAE;EACrB;EACA,IAAI;IACF,IAAI,CAACC,MAAM,CAACC,YAAY,EAAE,OAAO,KAAK;EACxC,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,IAAIC,GAAG,GAAGH,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC;EACnC,IAAI,IAAI,IAAII,GAAG,EAAE,OAAO,KAAK;EAC7B,OAAOC,MAAM,CAACD,GAAG,CAAC,CAACE,WAAW,CAAC,CAAC,KAAK,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}