import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';

const WebSocketContext = createContext();

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export const WebSocketProvider = ({ children }) => {
  const { user, token } = useAuth();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [activeDebates, setActiveDebates] = useState({});
  const [debateMessages, setDebateMessages] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [typingUsers, setTypingUsers] = useState({});
  
  const socketRef = useRef(null);
  const typingTimeoutRef = useRef({});

  useEffect(() => {
    if (user && token) {
      // Initialize socket connection
      const newSocket = io(process.env.REACT_APP_API_URL || 'http://localhost:3000', {
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      socketRef.current = newSocket;
      setSocket(newSocket);

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('🔌 Connected to WebSocket server');
        setIsConnected(true);
        
        // Authenticate with the server
        newSocket.emit('authenticate', token);
      });

      newSocket.on('disconnect', () => {
        console.log('🔌 Disconnected from WebSocket server');
        setIsConnected(false);
      });

      newSocket.on('authenticated', (data) => {
        if (data.success) {
          console.log('✅ WebSocket authentication successful');
        } else {
          console.error('❌ WebSocket authentication failed:', data.error);
        }
      });

      // Debate event handlers
      newSocket.on('user-joined-debate', (data) => {
        console.log(`👥 User ${data.username} joined debate`);
        addNotification({
          type: 'info',
          message: `${data.username} joined the debate`,
          timestamp: data.timestamp
        });
      });

      newSocket.on('user-left-debate', (data) => {
        console.log(`👋 User ${data.username} left debate`);
        addNotification({
          type: 'info',
          message: `${data.username} left the debate`,
          timestamp: data.timestamp
        });
      });

      newSocket.on('participant-count-updated', (data) => {
        setActiveDebates(prev => ({
          ...prev,
          [data.debateId]: data.count
        }));
      });

      newSocket.on('debate-chat-message', (message) => {
        setDebateMessages(prev => ({
          ...prev,
          [message.debateId]: [
            ...(prev[message.debateId] || []),
            message
          ]
        }));
      });

      newSocket.on('debate-status-updated', (data) => {
        addNotification({
          type: 'info',
          message: `Debate status updated to: ${data.status}`,
          timestamp: data.timestamp
        });
      });

      // Discussion event handlers
      newSocket.on('new-comment', (data) => {
        addNotification({
          type: 'comment',
          message: `${data.username} added a new comment`,
          threadId: data.threadId,
          timestamp: data.timestamp
        });
      });

      newSocket.on('user-typing', (data) => {
        setTypingUsers(prev => ({
          ...prev,
          [data.threadId]: {
            ...prev[data.threadId],
            [data.userId]: data.username
          }
        }));

        // Clear typing indicator after 3 seconds
        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {
          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);
        }
        
        typingTimeoutRef.current[`${data.threadId}-${data.userId}`] = setTimeout(() => {
          setTypingUsers(prev => {
            const newState = { ...prev };
            if (newState[data.threadId]) {
              delete newState[data.threadId][data.userId];
              if (Object.keys(newState[data.threadId]).length === 0) {
                delete newState[data.threadId];
              }
            }
            return newState;
          });
        }, 3000);
      });

      newSocket.on('user-stopped-typing', (data) => {
        setTypingUsers(prev => {
          const newState = { ...prev };
          if (newState[data.threadId]) {
            delete newState[data.threadId][data.userId];
            if (Object.keys(newState[data.threadId]).length === 0) {
              delete newState[data.threadId];
            }
          }
          return newState;
        });

        if (typingTimeoutRef.current[`${data.threadId}-${data.userId}`]) {
          clearTimeout(typingTimeoutRef.current[`${data.threadId}-${data.userId}`]);
          delete typingTimeoutRef.current[`${data.threadId}-${data.userId}`];
        }
      });

      // General notification handler
      newSocket.on('notification', (notification) => {
        addNotification(notification);
      });

      newSocket.on('error', (error) => {
        console.error('WebSocket error:', error);
        addNotification({
          type: 'error',
          message: error.message || 'WebSocket error occurred',
          timestamp: new Date().toISOString()
        });
      });

      return () => {
        newSocket.disconnect();
        setSocket(null);
        setIsConnected(false);
        
        // Clear all timeouts
        Object.values(typingTimeoutRef.current).forEach(timeout => {
          clearTimeout(timeout);
        });
        typingTimeoutRef.current = {};
      };
    }
  }, [user, token]);

  const addNotification = (notification) => {
    const newNotification = {
      id: Date.now().toString(),
      ...notification
    };
    
    setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep last 50 notifications
    
    // Auto-remove notification after 5 seconds for non-error types
    if (notification.type !== 'error') {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Debate functions
  const joinDebate = (debateId) => {
    if (socket && isConnected) {
      socket.emit('join-debate', debateId);
    }
  };

  const leaveDebate = (debateId) => {
    if (socket && isConnected) {
      socket.emit('leave-debate', debateId);
    }
  };

  const sendDebateMessage = (debateId, message) => {
    if (socket && isConnected) {
      socket.emit('debate-chat-message', { debateId, message });
    }
  };

  const voteInPoll = (debateId, pollId, option) => {
    if (socket && isConnected) {
      socket.emit('debate-poll-vote', { debateId, pollId, option });
    }
  };

  // Discussion functions
  const joinDiscussion = (threadId) => {
    if (socket && isConnected) {
      socket.emit('join-discussion', threadId);
    }
  };

  const leaveDiscussion = (threadId) => {
    if (socket && isConnected) {
      socket.emit('leave-discussion', threadId);
    }
  };

  const notifyNewComment = (threadId, commentId, content) => {
    if (socket && isConnected) {
      socket.emit('new-comment', { threadId, commentId, content });
    }
  };

  const startTyping = (threadId) => {
    if (socket && isConnected) {
      socket.emit('typing-start', { threadId });
    }
  };

  const stopTyping = (threadId) => {
    if (socket && isConnected) {
      socket.emit('typing-stop', { threadId });
    }
  };

  const value = {
    socket,
    isConnected,
    activeDebates,
    debateMessages,
    notifications,
    typingUsers,
    
    // Actions
    joinDebate,
    leaveDebate,
    sendDebateMessage,
    voteInPoll,
    joinDiscussion,
    leaveDiscussion,
    notifyNewComment,
    startTyping,
    stopTyping,
    removeNotification,
    
    // Utilities
    getDebateParticipantCount: (debateId) => activeDebates[debateId] || 0,
    getDebateMessages: (debateId) => debateMessages[debateId] || [],
    getTypingUsers: (threadId) => typingUsers[threadId] || {}
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export default WebSocketContext;
