{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    updateProfile: updateAuthProfile\n  } = useAuth();\n  const [profile, setProfile] = useState({\n    username: '',\n    bio: '',\n    role: 'regular',\n    verified_status: false\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState('');\n  const [verificationRequests, setVerificationRequests] = useState([]);\n  const [showVerificationForm, setShowVerificationForm] = useState(false);\n  const [verificationData, setVerificationData] = useState({\n    credentials: '',\n    expertise_area: '',\n    reason: ''\n  });\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  useEffect(() => {\n    if (isAuthenticated) {\n      getProfile();\n      fetchVerificationRequests();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n  const getProfile = async () => {\n    try {\n      setLoading(true);\n      if (user) {\n        setProfile({\n          username: user.username || '',\n          bio: user.bio || '',\n          role: user.role || 'regular',\n          verified_status: user.verified_status || false\n        });\n      }\n    } catch (error) {\n      console.error('Error loading profile:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchVerificationRequests = async () => {\n    try {\n      const response = await axios.get(`${API_URL}/api/verification/my-requests`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setVerificationRequests(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching verification requests:', error);\n    }\n  };\n  const updateProfile = async e => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n    try {\n      const result = await updateAuthProfile({\n        username: profile.username,\n        bio: profile.bio\n      });\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n      } else {\n        setMessage('Error updating profile: ' + result.error);\n      }\n    } catch (error) {\n      setMessage('Error updating profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const submitVerificationRequest = async e => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n    try {\n      const response = await axios.post(`${API_URL}/api/verification/request`, verificationData, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setMessage('Verification request submitted successfully!');\n        setShowVerificationForm(false);\n        setVerificationData({\n          credentials: '',\n          expertise_area: '',\n          reason: ''\n        });\n        fetchVerificationRequests(); // Refresh requests\n      }\n    } catch (error) {\n      setMessage('Error submitting verification request');\n      console.error('Error:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading profile...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please log in to view your profile.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Manage your account settings and preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Profile Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mb-4 p-3 rounded-md ${message.includes('Error') ? 'bg-red-50 text-red-600 border border-red-200' : 'bg-green-50 text-green-600 border border-green-200'}`,\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: updateProfile,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  value: user.email,\n                  disabled: true,\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-500\",\n                  children: \"Email cannot be changed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"username\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  value: profile.username,\n                  onChange: e => setProfile({\n                    ...profile,\n                    username: e.target.value\n                  }),\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"bio\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Bio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"bio\",\n                  rows: 4,\n                  value: profile.bio,\n                  onChange: e => setProfile({\n                    ...profile,\n                    bio: e.target.value\n                  }),\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                  placeholder: \"Tell us about yourself...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: saving,\n                  className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\",\n                  children: saving ? 'Saving...' : 'Save Changes'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Account Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${profile.role === 'admin' ? 'bg-purple-100 text-purple-800' : profile.role === 'verified' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: profile.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Verification Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${profile.verified_status ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                  children: profile.verified_status ? 'Verified' : 'Pending'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Member Since\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-900\",\n                  children: new Date(user.created_at).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), !profile.verified_status && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 text-sm\",\n                children: \"Request Verification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-xs text-gray-500\",\n                children: \"Verified users can create and edit articles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Activity Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Articles Read\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Discussions Joined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Comments Posted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"23\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Debates Watched\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"IlfmPaZFJSSNfpD/TIabNLOixOs=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "axios", "jsxDEV", "_jsxDEV", "Profile", "_s", "user", "token", "isAuthenticated", "updateProfile", "updateAuthProfile", "profile", "setProfile", "username", "bio", "role", "verified_status", "loading", "setLoading", "saving", "setSaving", "message", "setMessage", "verificationRequests", "setVerificationRequests", "showVerificationForm", "setShowVerificationForm", "verificationData", "setVerificationData", "credentials", "expertise_area", "reason", "API_URL", "process", "env", "REACT_APP_API_URL", "getProfile", "fetchVerificationRequests", "error", "console", "response", "get", "headers", "data", "e", "preventDefault", "result", "success", "submitVerificationRequest", "post", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "onSubmit", "htmlFor", "type", "id", "value", "email", "disabled", "onChange", "target", "rows", "placeholder", "Date", "created_at", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst Profile = () => {\n  const { user, token, isAuthenticated, updateProfile: updateAuthProfile } = useAuth();\n  const [profile, setProfile] = useState({\n    username: '',\n    bio: '',\n    role: 'regular',\n    verified_status: false\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState('');\n  const [verificationRequests, setVerificationRequests] = useState([]);\n  const [showVerificationForm, setShowVerificationForm] = useState(false);\n  const [verificationData, setVerificationData] = useState({\n    credentials: '',\n    expertise_area: '',\n    reason: ''\n  });\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      getProfile();\n      fetchVerificationRequests();\n    } else {\n      setLoading(false);\n    }\n  }, [isAuthenticated]);\n\n  const getProfile = async () => {\n    try {\n      setLoading(true);\n      if (user) {\n        setProfile({\n          username: user.username || '',\n          bio: user.bio || '',\n          role: user.role || 'regular',\n          verified_status: user.verified_status || false\n        });\n      }\n    } catch (error) {\n      console.error('Error loading profile:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchVerificationRequests = async () => {\n    try {\n      const response = await axios.get(`${API_URL}/api/verification/my-requests`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setVerificationRequests(response.data.data || []);\n    } catch (error) {\n      console.error('Error fetching verification requests:', error);\n    }\n  };\n\n  const updateProfile = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n\n    try {\n      const result = await updateAuthProfile({\n        username: profile.username,\n        bio: profile.bio\n      });\n\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n      } else {\n        setMessage('Error updating profile: ' + result.error);\n      }\n    } catch (error) {\n      setMessage('Error updating profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const submitVerificationRequest = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setMessage('');\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/verification/request`,\n        verificationData,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setMessage('Verification request submitted successfully!');\n        setShowVerificationForm(false);\n        setVerificationData({ credentials: '', expertise_area: '', reason: '' });\n        fetchVerificationRequests(); // Refresh requests\n      }\n    } catch (error) {\n      setMessage('Error submitting verification request');\n      console.error('Error:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading profile...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Access Denied</h2>\n          <p className=\"text-gray-600\">Please log in to view your profile.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Profile</h1>\n          <p className=\"text-lg text-gray-600\">\n            Manage your account settings and preferences\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Profile Info */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Profile Information</h2>\n              \n              {message && (\n                <div className={`mb-4 p-3 rounded-md ${\n                  message.includes('Error') \n                    ? 'bg-red-50 text-red-600 border border-red-200' \n                    : 'bg-green-50 text-green-600 border border-green-200'\n                }`}>\n                  {message}\n                </div>\n              )}\n\n              <form onSubmit={updateProfile} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={user.email}\n                    disabled\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                  />\n                  <p className=\"mt-1 text-sm text-gray-500\">Email cannot be changed</p>\n                </div>\n\n                <div>\n                  <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                    Username\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    value={profile.username}\n                    onChange={(e) => setProfile({ ...profile, username: e.target.value })}\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700\">\n                    Bio\n                  </label>\n                  <textarea\n                    id=\"bio\"\n                    rows={4}\n                    value={profile.bio}\n                    onChange={(e) => setProfile({ ...profile, bio: e.target.value })}\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Tell us about yourself...\"\n                  />\n                </div>\n\n                <div>\n                  <button\n                    type=\"submit\"\n                    disabled={saving}\n                    className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n                  >\n                    {saving ? 'Saving...' : 'Save Changes'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Account Status */}\n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Account Status</h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Role</span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    profile.role === 'admin' ? 'bg-purple-100 text-purple-800' :\n                    profile.role === 'verified' ? 'bg-green-100 text-green-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {profile.role}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Verification Status</span>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    profile.verified_status \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    {profile.verified_status ? 'Verified' : 'Pending'}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Member Since</span>\n                  <span className=\"text-sm text-gray-900\">\n                    {new Date(user.created_at).toLocaleDateString()}\n                  </span>\n                </div>\n              </div>\n\n              {!profile.verified_status && (\n                <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                  <button className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 text-sm\">\n                    Request Verification\n                  </button>\n                  <p className=\"mt-2 text-xs text-gray-500\">\n                    Verified users can create and edit articles\n                  </p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Activity Summary</h3>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Articles Read</span>\n                  <span className=\"text-sm font-medium text-gray-900\">12</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Discussions Joined</span>\n                  <span className=\"text-sm font-medium text-gray-900\">5</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Comments Posted</span>\n                  <span className=\"text-sm font-medium text-gray-900\">23</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Debates Watched</span>\n                  <span className=\"text-sm font-medium text-gray-900\">3</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAkB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACpF,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC;IACrCe,QAAQ,EAAE,EAAE;IACZC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,SAAS;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC;IACvD+B,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExEpC,SAAS,CAAC,MAAM;IACd,IAAIS,eAAe,EAAE;MACnB4B,UAAU,CAAC,CAAC;MACZC,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,eAAe,CAAC,CAAC;EAErB,MAAM4B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIZ,IAAI,EAAE;QACRM,UAAU,CAAC;UACTC,QAAQ,EAAEP,IAAI,CAACO,QAAQ,IAAI,EAAE;UAC7BC,GAAG,EAAER,IAAI,CAACQ,GAAG,IAAI,EAAE;UACnBC,IAAI,EAAET,IAAI,CAACS,IAAI,IAAI,SAAS;UAC5BC,eAAe,EAAEV,IAAI,CAACU,eAAe,IAAI;QAC3C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,GAAGT,OAAO,+BAA+B,EAAE;QAC1EU,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnC,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFiB,uBAAuB,CAACgB,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,MAAM7B,aAAa,GAAG,MAAOmC,CAAC,IAAK;IACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzB,SAAS,CAAC,IAAI,CAAC;IACfE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMwB,MAAM,GAAG,MAAMpC,iBAAiB,CAAC;QACrCG,QAAQ,EAAEF,OAAO,CAACE,QAAQ;QAC1BC,GAAG,EAAEH,OAAO,CAACG;MACf,CAAC,CAAC;MAEF,IAAIgC,MAAM,CAACC,OAAO,EAAE;QAClBzB,UAAU,CAAC,+BAA+B,CAAC;MAC7C,CAAC,MAAM;QACLA,UAAU,CAAC,0BAA0B,GAAGwB,MAAM,CAACR,KAAK,CAAC;MACvD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdhB,UAAU,CAAC,wBAAwB,CAAC;IACtC,CAAC,SAAS;MACRF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM4B,yBAAyB,GAAG,MAAOJ,CAAC,IAAK;IAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzB,SAAS,CAAC,IAAI,CAAC;IACfE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMvC,KAAK,CAACgD,IAAI,CAC/B,GAAGjB,OAAO,2BAA2B,EACrCL,gBAAgB,EAChB;QACEe,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnC,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIiC,QAAQ,CAACG,IAAI,CAACI,OAAO,EAAE;QACzBzB,UAAU,CAAC,8CAA8C,CAAC;QAC1DI,uBAAuB,CAAC,KAAK,CAAC;QAC9BE,mBAAmB,CAAC;UAAEC,WAAW,EAAE,EAAE;UAAEC,cAAc,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAC,CAAC;QACxEM,yBAAyB,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,UAAU,CAAC,uCAAuC,CAAC;MACnDiB,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC,CAAC,SAAS;MACRlB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACEd,OAAA;MAAK+C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEhD,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhD,OAAA;UAAK+C,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FpD,OAAA;UAAG+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACjD,IAAI,EAAE;IACT,oBACEH,OAAA;MAAK+C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEhD,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhD,OAAA;UAAI+C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEpD,OAAA;UAAG+C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpD,OAAA;IAAK+C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtChD,OAAA;MAAK+C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DhD,OAAA;QAAK+C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhD,OAAA;UAAI+C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEpD,OAAA;UAAG+C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpD,OAAA;QAAK+C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDhD,OAAA;UAAK+C,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BhD,OAAA;YAAK+C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhD,OAAA;cAAI+C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEhFlC,OAAO,iBACNlB,OAAA;cAAK+C,SAAS,EAAE,uBACd7B,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,GACrB,8CAA8C,GAC9C,oDAAoD,EACvD;cAAAL,QAAA,EACA9B;YAAO;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAEDpD,OAAA;cAAMsD,QAAQ,EAAEhD,aAAc;cAACyC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAClDhD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAOuD,OAAO,EAAC,OAAO;kBAACR,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACEwD,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAC,OAAO;kBACVC,KAAK,EAAEvD,IAAI,CAACwD,KAAM;kBAClBC,QAAQ;kBACRb,SAAS,EAAC;gBAAwF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACFpD,OAAA;kBAAG+C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eAENpD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAOuD,OAAO,EAAC,UAAU;kBAACR,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE9E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,UAAU;kBACbC,KAAK,EAAElD,OAAO,CAACE,QAAS;kBACxBmD,QAAQ,EAAGpB,CAAC,IAAKhC,UAAU,CAAC;oBAAE,GAAGD,OAAO;oBAAEE,QAAQ,EAAE+B,CAAC,CAACqB,MAAM,CAACJ;kBAAM,CAAC,CAAE;kBACtEX,SAAS,EAAC;gBAA4H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAOuD,OAAO,EAAC,KAAK;kBAACR,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEzE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACEyD,EAAE,EAAC,KAAK;kBACRM,IAAI,EAAE,CAAE;kBACRL,KAAK,EAAElD,OAAO,CAACG,GAAI;kBACnBkD,QAAQ,EAAGpB,CAAC,IAAKhC,UAAU,CAAC;oBAAE,GAAGD,OAAO;oBAAEG,GAAG,EAAE8B,CAAC,CAACqB,MAAM,CAACJ;kBAAM,CAAC,CAAE;kBACjEX,SAAS,EAAC,4HAA4H;kBACtIiB,WAAW,EAAC;gBAA2B;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAgD,QAAA,eACEhD,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACbI,QAAQ,EAAE5C,MAAO;kBACjB+B,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAE3KhC,MAAM,GAAG,WAAW,GAAG;gBAAc;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK+C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhD,OAAA;YAAK+C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhD,OAAA;cAAI+C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5EpD,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhD,OAAA;gBAAK+C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDhD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDpD,OAAA;kBAAM+C,SAAS,EAAE,8CACfvC,OAAO,CAACI,IAAI,KAAK,OAAO,GAAG,+BAA+B,GAC1DJ,OAAO,CAACI,IAAI,KAAK,UAAU,GAAG,6BAA6B,GAC3D,2BAA2B,EAC1B;kBAAAoC,QAAA,EACAxC,OAAO,CAACI;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENpD,OAAA;gBAAK+C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDhD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEpD,OAAA;kBAAM+C,SAAS,EAAE,8CACfvC,OAAO,CAACK,eAAe,GACnB,6BAA6B,GAC7B,+BAA+B,EAClC;kBAAAmC,QAAA,EACAxC,OAAO,CAACK,eAAe,GAAG,UAAU,GAAG;gBAAS;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENpD,OAAA;gBAAK+C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDhD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3DpD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACpC,IAAIiB,IAAI,CAAC9D,IAAI,CAAC+D,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL,CAAC5C,OAAO,CAACK,eAAe,iBACvBb,OAAA;cAAK+C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDhD,OAAA;gBAAQ+C,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAEnG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpD,OAAA;gBAAG+C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpD,OAAA;YAAK+C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhD,OAAA;cAAI+C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE9EpD,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhD,OAAA;gBAAK+C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DpD,OAAA;kBAAM+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNpD,OAAA;gBAAK+C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjEpD,OAAA;kBAAM+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNpD,OAAA;gBAAK+C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9DpD,OAAA;kBAAM+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNpD,OAAA;gBAAK+C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChD,OAAA;kBAAM+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9DpD,OAAA;kBAAM+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAtSID,OAAO;EAAA,QACgEJ,OAAO;AAAA;AAAAuE,EAAA,GAD9EnE,OAAO;AAwSb,eAAeA,OAAO;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}