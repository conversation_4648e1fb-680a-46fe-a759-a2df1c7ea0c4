{"ast": null, "code": "exports = module.exports = require('./lib/_stream_readable.js');\nexports.Stream = exports;\nexports.Readable = exports;\nexports.Writable = require('./lib/_stream_writable.js');\nexports.Duplex = require('./lib/_stream_duplex.js');\nexports.Transform = require('./lib/_stream_transform.js');\nexports.PassThrough = require('./lib/_stream_passthrough.js');\nexports.finished = require('./lib/internal/streams/end-of-stream.js');\nexports.pipeline = require('./lib/internal/streams/pipeline.js');", "map": {"version": 3, "names": ["exports", "module", "require", "Stream", "Readable", "Writable", "Duplex", "Transform", "PassThrough", "finished", "pipeline"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/readable-stream/readable-browser.js"], "sourcesContent": ["exports = module.exports = require('./lib/_stream_readable.js');\nexports.Stream = exports;\nexports.Readable = exports;\nexports.Writable = require('./lib/_stream_writable.js');\nexports.Duplex = require('./lib/_stream_duplex.js');\nexports.Transform = require('./lib/_stream_transform.js');\nexports.PassThrough = require('./lib/_stream_passthrough.js');\nexports.finished = require('./lib/internal/streams/end-of-stream.js');\nexports.pipeline = require('./lib/internal/streams/pipeline.js');\n"], "mappings": "AAAAA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAGE,OAAO,CAAC,2BAA2B,CAAC;AAC/DF,OAAO,CAACG,MAAM,GAAGH,OAAO;AACxBA,OAAO,CAACI,QAAQ,GAAGJ,OAAO;AAC1BA,OAAO,CAACK,QAAQ,GAAGH,OAAO,CAAC,2BAA2B,CAAC;AACvDF,OAAO,CAACM,MAAM,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AACnDF,OAAO,CAACO,SAAS,GAAGL,OAAO,CAAC,4BAA4B,CAAC;AACzDF,OAAO,CAACQ,WAAW,GAAGN,OAAO,CAAC,8BAA8B,CAAC;AAC7DF,OAAO,CAACS,QAAQ,GAAGP,OAAO,CAAC,yCAAyC,CAAC;AACrEF,OAAO,CAACU,QAAQ,GAAGR,OAAO,CAAC,oCAAoC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}