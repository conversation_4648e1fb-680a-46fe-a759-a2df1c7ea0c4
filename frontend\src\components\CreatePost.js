import React, { useState, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const CreatePost = ({ onPostCreated }) => {
  const [content, setContent] = useState('');
  const [postType, setPostType] = useState('text');
  const [hashtags, setHashtags] = useState('');
  const [topics, setTopics] = useState([]);
  const [visibility, setVisibility] = useState('public');
  const [mediaFiles, setMediaFiles] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [error, setError] = useState('');

  const { user } = useAuth();
  const fileInputRef = useRef(null);

  const politicalTopics = [
    'Climate Change', 'Healthcare Reform', 'Economic Policy', 'Immigration',
    'Foreign Policy', 'Education', 'Criminal Justice', 'Technology Policy',
    'Trade Policy', 'Social Issues', 'Defense', 'Infrastructure',
    'Tax Policy', 'Labor Rights', 'Civil Rights', 'Energy Policy'
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!content.trim()) {
      setError('Please enter some content');
      return;
    }

    if (content.length > 2000) {
      setError('Post is too long (max 2000 characters)');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Process hashtags
      const hashtagArray = hashtags
        .split(/[,\s]+/)
        .filter(tag => tag.trim())
        .map(tag => tag.replace('#', '').toLowerCase());

      // For now, we'll simulate media upload URLs
      // In a real app, you'd upload files to a storage service first
      const mediaUrls = mediaFiles.map(file => URL.createObjectURL(file));

      const postData = {
        content: content.trim(),
        post_type: postType,
        hashtags: hashtagArray,
        topics: topics,
        visibility: visibility,
        media_urls: mediaUrls
      };

      const response = await axios.post('/api/feed', postData);
      
      // Reset form
      setContent('');
      setHashtags('');
      setTopics([]);
      setMediaFiles([]);
      setPostType('text');
      setVisibility('public');
      setShowAdvanced(false);
      
      // Notify parent component
      onPostCreated(response.data);

    } catch (error) {
      console.error('Error creating post:', error);
      setError(error.response?.data?.error || 'Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setMediaFiles(prev => [...prev, ...files]);
    
    // Update post type based on file types
    if (files.some(file => file.type.startsWith('image/'))) {
      setPostType('image');
    } else if (files.some(file => file.type.startsWith('video/'))) {
      setPostType('video');
    }
  };

  const removeFile = (index) => {
    setMediaFiles(prev => prev.filter((_, i) => i !== index));
    if (mediaFiles.length === 1) {
      setPostType('text');
    }
  };

  const toggleTopic = (topic) => {
    setTopics(prev => 
      prev.includes(topic) 
        ? prev.filter(t => t !== topic)
        : [...prev, topic]
    );
  };

  const characterCount = content.length;
  const isOverLimit = characterCount > 2000;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <form onSubmit={handleSubmit}>
        {/* Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
              {user?.username?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">Share your political thoughts</h3>
              <p className="text-sm text-gray-500">What's happening in politics today?</p>
            </div>
          </div>

          {/* Content Input */}
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What's your take on current political events? Share your thoughts, analysis, or questions..."
            className={`w-full p-4 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isOverLimit ? 'border-red-300' : 'border-gray-300'
            }`}
            rows={4}
            maxLength={2500}
          />

          {/* Character Count */}
          <div className="flex justify-between items-center mt-2">
            <div className="text-sm text-gray-500">
              <span className={isOverLimit ? 'text-red-500' : ''}>
                {characterCount}/2000 characters
              </span>
            </div>
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {showAdvanced ? 'Hide Options' : 'More Options'}
            </button>
          </div>

          {/* Media Preview */}
          {mediaFiles.length > 0 && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3">
              {mediaFiles.map((file, index) => (
                <div key={index} className="relative">
                  {file.type.startsWith('image/') ? (
                    <img
                      src={URL.createObjectURL(file)}
                      alt="Preview"
                      className="w-full h-24 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500 text-sm">{file.name}</span>
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
              {/* Post Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Post Type</label>
                <select
                  value={postType}
                  onChange={(e) => setPostType(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="text">Text Post</option>
                  <option value="image">Image Post</option>
                  <option value="video">Video Post</option>
                  <option value="poll">Poll</option>
                  <option value="article_share">Article Share</option>
                </select>
              </div>

              {/* Hashtags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Hashtags (comma-separated)
                </label>
                <input
                  type="text"
                  value={hashtags}
                  onChange={(e) => setHashtags(e.target.value)}
                  placeholder="e.g., #politics, #election2024, #democracy"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Political Topics */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Political Topics (select relevant topics)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                  {politicalTopics.map(topic => (
                    <label key={topic} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={topics.includes(topic)}
                        onChange={() => toggleTopic(topic)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{topic}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Visibility */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Visibility</label>
                <select
                  value={visibility}
                  onChange={(e) => setVisibility(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="public">🌍 Public - Everyone can see</option>
                  <option value="followers">👥 Followers - Only followers can see</option>
                  <option value="private">🔒 Private - Only you can see</option>
                </select>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Media Upload */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <span>📷</span>
              <span className="text-sm">Media</span>
            </button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*"
              onChange={handleFileSelect}
              className="hidden"
            />

            {/* Poll Option */}
            <button
              type="button"
              onClick={() => setPostType('poll')}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                postType === 'poll' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <span>📊</span>
              <span className="text-sm">Poll</span>
            </button>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting || !content.trim() || isOverLimit}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Posting...</span>
              </>
            ) : (
              <>
                <span>📝</span>
                <span>Post</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreatePost;
