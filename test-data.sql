-- Insert test user
INSERT INTO users (username, email, password_hash, role, is_verified, created_at) 
VALUES (
  'testuser', 
  '<EMAIL>', 
  '$2b$10$rOzJqQZJqQZJqQZJqQZJqOzJqQZJqQZJqQZJqQZJqQZJqQZJqQZJq', -- password123
  'user', 
  true, 
  NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert test debate
INSERT INTO live_debates (title, description, topic, scheduled_at, status, moderator_id, created_at)
VALUES (
  'Climate Change Policy Debate',
  'A comprehensive discussion on climate change policies and their economic impact',
  'Environmental Policy',
  NOW() + INTERVAL '1 hour',
  'live',
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  NOW()
) ON CONFLICT DO NOTHING;

-- Insert another test debate
INSERT INTO live_debates (title, description, topic, scheduled_at, status, moderator_id, created_at)
VALUES (
  'Healthcare Reform Discussion',
  'Exploring different approaches to healthcare reform and universal coverage',
  'Healthcare Policy',
  NOW() + INTERVAL '2 hours',
  'scheduled',
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  NOW()
) ON CONFLICT DO NOTHING;
