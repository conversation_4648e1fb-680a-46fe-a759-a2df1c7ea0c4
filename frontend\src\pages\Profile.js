import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const Profile = () => {
  const { user, token, isAuthenticated, updateProfile: updateAuthProfile } = useAuth();
  const [profile, setProfile] = useState({
    username: '',
    bio: '',
    role: 'regular',
    verified_status: false
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [verificationRequests, setVerificationRequests] = useState([]);
  const [showVerificationForm, setShowVerificationForm] = useState(false);
  const [verificationData, setVerificationData] = useState({
    credentials: '',
    expertise_area: '',
    reason: ''
  });

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

  useEffect(() => {
    if (isAuthenticated) {
      getProfile();
      fetchVerificationRequests();
    } else {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const getProfile = async () => {
    try {
      setLoading(true);
      if (user) {
        setProfile({
          username: user.username || '',
          bio: user.bio || '',
          role: user.role || 'regular',
          verified_status: user.verified_status || false
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchVerificationRequests = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/verification/my-requests`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      setVerificationRequests(response.data.data || []);
    } catch (error) {
      console.error('Error fetching verification requests:', error);
    }
  };

  const updateProfile = async (e) => {
    e.preventDefault();
    setSaving(true);
    setMessage('');

    try {
      const result = await updateAuthProfile({
        username: profile.username,
        bio: profile.bio
      });

      if (result.success) {
        setMessage('Profile updated successfully!');
      } else {
        setMessage('Error updating profile: ' + result.error);
      }
    } catch (error) {
      setMessage('Error updating profile');
    } finally {
      setSaving(false);
    }
  };

  const submitVerificationRequest = async (e) => {
    e.preventDefault();
    setSaving(true);
    setMessage('');

    try {
      const response = await axios.post(
        `${API_URL}/api/verification/request`,
        verificationData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        setMessage('Verification request submitted successfully!');
        setShowVerificationForm(false);
        setVerificationData({ credentials: '', expertise_area: '', reason: '' });
        fetchVerificationRequests(); // Refresh requests
      }
    } catch (error) {
      setMessage('Error submitting verification request');
      console.error('Error:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">Please log in to view your profile.</p>
          <a href="/login" className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
            Login
          </a>
        </div>
      </div>
    );
  }

  // Verification form
  if (showVerificationForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setShowVerificationForm(false)}
              className="text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Profile
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Request Verification</h1>
            <p className="text-gray-600">Submit your credentials for verification to gain access to advanced features.</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <form onSubmit={submitVerificationRequest}>
              <div className="mb-6">
                <label htmlFor="expertise_area" className="block text-sm font-medium text-gray-700 mb-2">
                  Area of Expertise *
                </label>
                <select
                  id="expertise_area"
                  value={verificationData.expertise_area}
                  onChange={(e) => setVerificationData({ ...verificationData, expertise_area: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select your area of expertise</option>
                  <option value="Political Science">Political Science</option>
                  <option value="Public Policy">Public Policy</option>
                  <option value="International Relations">International Relations</option>
                  <option value="Constitutional Law">Constitutional Law</option>
                  <option value="Economics">Economics</option>
                  <option value="Journalism">Journalism</option>
                  <option value="Government Official">Government Official</option>
                  <option value="Academic Researcher">Academic Researcher</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="credentials" className="block text-sm font-medium text-gray-700 mb-2">
                  Credentials *
                </label>
                <textarea
                  id="credentials"
                  value={verificationData.credentials}
                  onChange={(e) => setVerificationData({ ...verificationData, credentials: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="4"
                  placeholder="List your relevant credentials, degrees, certifications, or professional experience"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for Verification *
                </label>
                <textarea
                  id="reason"
                  value={verificationData.reason}
                  onChange={(e) => setVerificationData({ ...verificationData, reason: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="4"
                  placeholder="Explain why you want to be verified and how you plan to contribute to the platform"
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowVerificationForm(false)}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {saving ? 'Submitting...' : 'Submit Request'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Profile</h1>
          <p className="text-lg text-gray-600">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Info */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Profile Information</h2>

              {message && (
                <div className={`mb-4 p-3 rounded-md ${
                  message.includes('Error')
                    ? 'bg-red-50 text-red-600 border border-red-200'
                    : 'bg-green-50 text-green-600 border border-green-200'
                }`}>
                  {message}
                </div>
              )}

              <form onSubmit={updateProfile} className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={user?.email || ''}
                    disabled
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">Email cannot be changed</p>
                </div>

                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    value={profile.username}
                    onChange={(e) => setProfile({ ...profile, username: e.target.value })}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                    Bio
                  </label>
                  <textarea
                    id="bio"
                    rows={4}
                    value={profile.bio}
                    onChange={(e) => setProfile({ ...profile, bio: e.target.value })}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Tell us about yourself..."
                  />
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={saving}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {saving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Account Status */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Status</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Role</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    profile.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                    profile.verified_status ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {profile.role === 'admin' ? 'Admin' : profile.verified_status ? 'Verified' : 'Regular'}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Verification Status</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    profile.verified_status
                      ? 'bg-green-100 text-green-800'
                      : verificationRequests.length > 0
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {profile.verified_status ? 'Verified ✓' :
                     verificationRequests.length > 0 ? 'Pending Review' : 'Not Verified'}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Member Since</span>
                  <span className="text-sm text-gray-900">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
              </div>

              {!profile.verified_status && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  {verificationRequests.length === 0 ? (
                    <button
                      onClick={() => setShowVerificationForm(true)}
                      className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 text-sm"
                    >
                      Request Verification
                    </button>
                  ) : (
                    <div className="text-center">
                      <p className="text-sm text-yellow-600 mb-2">
                        Verification request submitted
                      </p>
                      <p className="text-xs text-gray-500">
                        Status: {verificationRequests[0]?.status || 'Pending'}
                      </p>
                    </div>
                  )}
                  <p className="mt-2 text-xs text-gray-500">
                    Verified users can create articles, submit research, and schedule debates
                  </p>
                </div>
              )}
            </div>

            {/* Verification Requests */}
            {verificationRequests.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Verification Requests</h3>
                <div className="space-y-3">
                  {verificationRequests.map((request) => (
                    <div key={request.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm font-medium text-gray-900">
                          {request.expertise_area}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          request.status === 'approved' ? 'bg-green-100 text-green-800' :
                          request.status === 'rejected' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {request.status}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Submitted: {new Date(request.created_at).toLocaleDateString()}
                      </p>
                      {request.admin_notes && (
                        <p className="text-xs text-gray-600 mt-1">
                          <strong>Admin Notes:</strong> {request.admin_notes}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Benefits</h3>

              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-600">Read knowledge base articles</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-600">Participate in discussions</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-600">Watch live debates</span>
                </div>
                <div className="flex items-center">
                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>
                    {profile.verified_status ? '✓' : '○'}
                  </span>
                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>
                    Create and edit articles
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>
                    {profile.verified_status ? '✓' : '○'}
                  </span>
                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>
                    Submit research papers
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={`mr-2 ${profile.verified_status ? 'text-green-500' : 'text-gray-400'}`}>
                    {profile.verified_status ? '✓' : '○'}
                  </span>
                  <span className={profile.verified_status ? 'text-gray-600' : 'text-gray-400'}>
                    Schedule live debates
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
