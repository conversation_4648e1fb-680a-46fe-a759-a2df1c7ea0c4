{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\ResearchRepository.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResearchRepository = () => {\n  _s();\n  const [papers, setPapers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all');\n  const [selectedPaper, setSelectedPaper] = useState(null);\n  const [showSubmissionForm, setShowSubmissionForm] = useState(false);\n  const [newPaper, setNewPaper] = useState({\n    title: '',\n    abstract: '',\n    category: '',\n    keywords: '',\n    content: ''\n  });\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isVerified\n  } = useAuth();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  const fetchPapers = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/research`);\n      setPapers(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch research papers');\n      console.error('Error fetching papers:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n  useEffect(() => {\n    fetchPapers();\n  }, [fetchPapers]);\n  const handleSubmitPaper = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to submit a paper');\n      return;\n    }\n    try {\n      const keywordsArray = newPaper.keywords.split(',').map(k => k.trim()).filter(k => k);\n      const response = await axios.post(`${API_URL}/api/research`, {\n        ...newPaper,\n        keywords: keywordsArray\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewPaper({\n          title: '',\n          abstract: '',\n          category: '',\n          keywords: '',\n          content: ''\n        });\n        setShowSubmissionForm(false);\n        fetchPapers(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to submit paper');\n      console.error('Error submitting paper:', err);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'under_review':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'submitted':\n        return 'bg-blue-100 text-blue-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  };\n  const filteredPapers = papers.filter(paper => filter === 'all' || paper.status === filter);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading research papers...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchPapers,\n          className: \"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Paper submission form\n  if (showSubmissionForm) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSubmissionForm(false),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Repository\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Submit Research Paper\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Submit your political research for peer review and publication.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmitPaper,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"title\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Paper Title *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"title\",\n                value: newPaper.title,\n                onChange: e => setNewPaper({\n                  ...newPaper,\n                  title: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter the title of your research paper\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category\",\n                value: newPaper.category,\n                onChange: e => setNewPaper({\n                  ...newPaper,\n                  category: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Political Theory\",\n                  children: \"Political Theory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Comparative Politics\",\n                  children: \"Comparative Politics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"International Relations\",\n                  children: \"International Relations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Public Policy\",\n                  children: \"Public Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Electoral Systems\",\n                  children: \"Electoral Systems\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Political Communication\",\n                  children: \"Political Communication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Political Economy\",\n                  children: \"Political Economy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Constitutional Law\",\n                  children: \"Constitutional Law\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"abstract\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Abstract *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"abstract\",\n                value: newPaper.abstract,\n                onChange: e => setNewPaper({\n                  ...newPaper,\n                  abstract: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"6\",\n                placeholder: \"Provide a comprehensive abstract of your research (250-500 words)\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"keywords\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Keywords *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"keywords\",\n                value: newPaper.keywords,\n                onChange: e => setNewPaper({\n                  ...newPaper,\n                  keywords: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter keywords separated by commas (e.g., democracy, voting, elections)\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Separate keywords with commas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"content\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Full Paper Content *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"content\",\n                value: newPaper.content,\n                onChange: e => setNewPaper({\n                  ...newPaper,\n                  content: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"20\",\n                placeholder: \"Paste the full content of your research paper here...\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowSubmissionForm(false),\n                className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                children: \"Submit for Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Research Repository\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Access peer-reviewed political research papers and submit your own work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filter,\n                onChange: e => setFilter(e.target.value),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Papers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"published\",\n                  children: \"Published\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"under_review\",\n                  children: \"Under Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"submitted\",\n                  children: \"Submitted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"rejected\",\n                  children: \"Rejected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSubmissionForm(true),\n              className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mb-4\",\n              children: \"Submit Paper\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/login\",\n              className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-md text-center block mb-4\",\n              title: \"Login required\",\n              children: \"Login to Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium mb-2\",\n                children: \"Submission Guidelines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Original research only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Peer review required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 APA citation format\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Maximum 10,000 words\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), !isVerified && isAuthenticated && /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-orange-600\",\n                  children: \"\\u2022 Verified status recommended\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-3/4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900\",\n                  children: [\"Research Papers (\", filteredPapers.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-200\",\n              children: filteredPapers.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-12 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400 text-6xl mb-4\",\n                  children: \"\\uD83D\\uDCC4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-gray-900 mb-2\",\n                  children: \"No research papers found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-4\",\n                  children: filter === 'all' ? 'Be the first to submit a research paper!' : `No papers with status \"${filter.replace('_', ' ')}\" found.`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), isAuthenticated && filter === 'all' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowSubmissionForm(true),\n                  className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n                  children: \"Submit First Paper\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this) : filteredPapers.map(paper => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 flex-1 mr-4\",\n                    children: paper.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`,\n                    children: paper.status.replace('_', ' ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-3 line-clamp-3\",\n                  children: paper.abstract\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 text-sm text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Author:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 31\n                    }, this), \" \", paper.author_username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Category:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 31\n                    }, this), \" \", paper.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Submitted:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 31\n                    }, this), \" \", formatDate(paper.created_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this), paper.status === 'published' && paper.published_at && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Published:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 33\n                    }, this), \" \", formatDate(paper.published_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), paper.keywords && paper.keywords.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-2 mb-4\",\n                  children: paper.keywords.map((keyword, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\",\n                    children: keyword\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                    children: [paper.download_count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [paper.download_count, \" downloads\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 56\n                    }, this), paper.citation_count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [paper.citation_count, \" citations\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 56\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setSelectedPaper(paper),\n                      className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                      children: \"View Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 27\n                    }, this), paper.status === 'published' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\",\n                      children: \"Download\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this)]\n              }, paper.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(ResearchRepository, \"rBisFhpDtqgK5Gmya5gc5jmx2T0=\", false, function () {\n  return [useAuth];\n});\n_c = ResearchRepository;\nexport default ResearchRepository;\nvar _c;\n$RefreshReg$(_c, \"ResearchRepository\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "axios", "jsxDEV", "_jsxDEV", "ResearchRepository", "_s", "papers", "setPapers", "loading", "setLoading", "error", "setError", "filter", "setFilter", "selectedPaper", "setSelectedPaper", "showSubmissionForm", "setShowSubmissionForm", "newPaper", "setNewPaper", "title", "abstract", "category", "keywords", "content", "user", "token", "isAuthenticated", "isVerified", "API_URL", "process", "env", "REACT_APP_API_URL", "fetchPapers", "response", "get", "data", "err", "console", "handleSubmitPaper", "e", "preventDefault", "keywordsArray", "split", "map", "k", "trim", "post", "headers", "success", "getStatusColor", "status", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "filteredPapers", "paper", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "rows", "href", "length", "replace", "author_username", "created_at", "published_at", "keyword", "index", "download_count", "citation_count", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/ResearchRepository.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst ResearchRepository = () => {\n  const [papers, setPapers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all');\n  const [selectedPaper, setSelectedPaper] = useState(null);\n  const [showSubmissionForm, setShowSubmissionForm] = useState(false);\n  const [newPaper, setNewPaper] = useState({\n    title: '',\n    abstract: '',\n    category: '',\n    keywords: '',\n    content: ''\n  });\n  const { user, token, isAuthenticated, isVerified } = useAuth();\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  const fetchPapers = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/research`);\n      setPapers(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch research papers');\n      console.error('Error fetching papers:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n\n  useEffect(() => {\n    fetchPapers();\n  }, [fetchPapers]);\n\n  const handleSubmitPaper = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to submit a paper');\n      return;\n    }\n\n    try {\n      const keywordsArray = newPaper.keywords.split(',').map(k => k.trim()).filter(k => k);\n\n      const response = await axios.post(\n        `${API_URL}/api/research`,\n        {\n          ...newPaper,\n          keywords: keywordsArray\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewPaper({ title: '', abstract: '', category: '', keywords: '', content: '' });\n        setShowSubmissionForm(false);\n        fetchPapers(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to submit paper');\n      console.error('Error submitting paper:', err);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'under_review':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'submitted':\n        return 'bg-blue-100 text-blue-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  };\n\n  const filteredPapers = papers.filter(paper =>\n    filter === 'all' || paper.status === filter\n  );\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading research papers...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-xl mb-4\">⚠️</div>\n          <p className=\"text-gray-600\">{error}</p>\n          <button\n            onClick={fetchPapers}\n            className=\"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Paper submission form\n  if (showSubmissionForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setShowSubmissionForm(false)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Repository\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Submit Research Paper</h1>\n            <p className=\"text-gray-600\">Submit your political research for peer review and publication.</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <form onSubmit={handleSubmitPaper}>\n              <div className=\"mb-6\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Paper Title *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  value={newPaper.title}\n                  onChange={(e) => setNewPaper({ ...newPaper, title: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter the title of your research paper\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category *\n                </label>\n                <select\n                  id=\"category\"\n                  value={newPaper.category}\n                  onChange={(e) => setNewPaper({ ...newPaper, category: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">Select a category</option>\n                  <option value=\"Political Theory\">Political Theory</option>\n                  <option value=\"Comparative Politics\">Comparative Politics</option>\n                  <option value=\"International Relations\">International Relations</option>\n                  <option value=\"Public Policy\">Public Policy</option>\n                  <option value=\"Electoral Systems\">Electoral Systems</option>\n                  <option value=\"Political Communication\">Political Communication</option>\n                  <option value=\"Political Economy\">Political Economy</option>\n                  <option value=\"Constitutional Law\">Constitutional Law</option>\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"abstract\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Abstract *\n                </label>\n                <textarea\n                  id=\"abstract\"\n                  value={newPaper.abstract}\n                  onChange={(e) => setNewPaper({ ...newPaper, abstract: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"6\"\n                  placeholder=\"Provide a comprehensive abstract of your research (250-500 words)\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"keywords\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Keywords *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"keywords\"\n                  value={newPaper.keywords}\n                  onChange={(e) => setNewPaper({ ...newPaper, keywords: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter keywords separated by commas (e.g., democracy, voting, elections)\"\n                  required\n                />\n                <p className=\"text-sm text-gray-500 mt-1\">Separate keywords with commas</p>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"content\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Full Paper Content *\n                </label>\n                <textarea\n                  id=\"content\"\n                  value={newPaper.content}\n                  onChange={(e) => setNewPaper({ ...newPaper, content: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"20\"\n                  placeholder=\"Paste the full content of your research paper here...\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowSubmissionForm(false)}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Submit for Review\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Research Repository</h1>\n          <p className=\"text-lg text-gray-600\">\n            Access peer-reviewed political research papers and submit your own work\n          </p>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-1/4\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Filters</h2>\n\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Status</label>\n                <select\n                  value={filter}\n                  onChange={(e) => setFilter(e.target.value)}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"all\">All Papers</option>\n                  <option value=\"published\">Published</option>\n                  <option value=\"under_review\">Under Review</option>\n                  <option value=\"submitted\">Submitted</option>\n                  <option value=\"rejected\">Rejected</option>\n                </select>\n              </div>\n\n              {isAuthenticated ? (\n                <button\n                  onClick={() => setShowSubmissionForm(true)}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mb-4\"\n                >\n                  Submit Paper\n                </button>\n              ) : (\n                <a\n                  href=\"/login\"\n                  className=\"w-full bg-gray-400 text-white py-2 px-4 rounded-md text-center block mb-4\"\n                  title=\"Login required\"\n                >\n                  Login to Submit\n                </a>\n              )}\n\n              <div className=\"text-sm text-gray-600\">\n                <h3 className=\"font-medium mb-2\">Submission Guidelines</h3>\n                <ul className=\"space-y-1\">\n                  <li>• Original research only</li>\n                  <li>• Peer review required</li>\n                  <li>• APA citation format</li>\n                  <li>• Maximum 10,000 words</li>\n                  {!isVerified && isAuthenticated && (\n                    <li className=\"text-orange-600\">• Verified status recommended</li>\n                  )}\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:w-3/4\">\n            <div className=\"bg-white rounded-lg shadow-md\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Research Papers ({filteredPapers.length})\n                  </h2>\n                </div>\n              </div>\n\n              <div className=\"divide-y divide-gray-200\">\n                {filteredPapers.length === 0 ? (\n                  <div className=\"p-12 text-center\">\n                    <div className=\"text-gray-400 text-6xl mb-4\">📄</div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No research papers found</h3>\n                    <p className=\"text-gray-600 mb-4\">\n                      {filter === 'all'\n                        ? 'Be the first to submit a research paper!'\n                        : `No papers with status \"${filter.replace('_', ' ')}\" found.`\n                      }\n                    </p>\n                    {isAuthenticated && filter === 'all' && (\n                      <button\n                        onClick={() => setShowSubmissionForm(true)}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n                      >\n                        Submit First Paper\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  filteredPapers.map((paper) => (\n                    <div key={paper.id} className=\"p-6 hover:bg-gray-50\">\n                      <div className=\"flex justify-between items-start mb-3\">\n                        <h3 className=\"text-lg font-medium text-gray-900 flex-1 mr-4\">\n                          {paper.title}\n                        </h3>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`}>\n                          {paper.status.replace('_', ' ')}\n                        </span>\n                      </div>\n\n                      <p className=\"text-gray-600 mb-3 line-clamp-3\">\n                        {paper.abstract}\n                      </p>\n\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-3\">\n                        <span><strong>Author:</strong> {paper.author_username}</span>\n                        <span><strong>Category:</strong> {paper.category}</span>\n                        <span><strong>Submitted:</strong> {formatDate(paper.created_at)}</span>\n                        {paper.status === 'published' && paper.published_at && (\n                          <span><strong>Published:</strong> {formatDate(paper.published_at)}</span>\n                        )}\n                      </div>\n\n                      {paper.keywords && paper.keywords.length > 0 && (\n                        <div className=\"flex flex-wrap gap-2 mb-4\">\n                          {paper.keywords.map((keyword, index) => (\n                            <span key={index} className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">\n                              {keyword}\n                            </span>\n                          ))}\n                        </div>\n                      )}\n\n                      <div className=\"flex justify-between items-center\">\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          {paper.download_count > 0 && <span>{paper.download_count} downloads</span>}\n                          {paper.citation_count > 0 && <span>{paper.citation_count} citations</span>}\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => setSelectedPaper(paper)}\n                            className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                          >\n                            View Details\n                          </button>\n                          {paper.status === 'published' && (\n                            <button className=\"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\">\n                              Download\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResearchRepository;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAE9D,MAAM6B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExE,MAAMC,WAAW,GAAGlC,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFU,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGN,OAAO,eAAe,CAAC;MAC3DtB,SAAS,CAAC2B,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ1B,QAAQ,CAAC,iCAAiC,CAAC;MAC3C2B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAE2B,GAAG,CAAC;IAC9C,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACoB,OAAO,CAAC,CAAC;EAEb/B,SAAS,CAAC,MAAM;IACdmC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAMM,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACd,eAAe,EAAE;MACpBhB,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IAEA,IAAI;MACF,MAAM+B,aAAa,GAAGxB,QAAQ,CAACK,QAAQ,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAClC,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAAC;MAEpF,MAAMX,QAAQ,GAAG,MAAMjC,KAAK,CAAC8C,IAAI,CAC/B,GAAGlB,OAAO,eAAe,EACzB;QACE,GAAGX,QAAQ;QACXK,QAAQ,EAAEmB;MACZ,CAAC,EACD;QACEM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUtB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIQ,QAAQ,CAACE,IAAI,CAACa,OAAO,EAAE;QACzB9B,WAAW,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QACjFP,qBAAqB,CAAC,KAAK,CAAC;QAC5BgB,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ1B,QAAQ,CAAC,wBAAwB,CAAC;MAClC2B,OAAO,CAAC5B,KAAK,CAAC,yBAAyB,EAAE2B,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,MAAMa,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,cAAc;QACjB,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,cAAc,GAAGnD,MAAM,CAACM,MAAM,CAAC8C,KAAK,IACxC9C,MAAM,KAAK,KAAK,IAAI8C,KAAK,CAACP,MAAM,KAAKvC,MACvC,CAAC;EAED,IAAIJ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKwD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzD,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F7D,OAAA;UAAGwD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItD,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKwD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzD,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnD7D,OAAA;UAAGwD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAElD;QAAK;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxC7D,OAAA;UACE8D,OAAO,EAAEhC,WAAY;UACrB0B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIhD,kBAAkB,EAAE;IACtB,oBACEb,OAAA;MAAKwD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCzD,OAAA;QAAKwD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DzD,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YACE8D,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,KAAK,CAAE;YAC5C0C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA;YAAIwD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChF7D,OAAA;YAAGwD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA+D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eAEN7D,OAAA;UAAKwD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzD,OAAA;YAAM+D,QAAQ,EAAE3B,iBAAkB;YAAAqB,QAAA,gBAChCzD,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOgE,OAAO,EAAC,OAAO;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEiE,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEpD,QAAQ,CAACE,KAAM;gBACtBmD,QAAQ,EAAG/B,CAAC,IAAKrB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEE,KAAK,EAAEoB,CAAC,CAACgC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACrEX,SAAS,EAAC,wFAAwF;gBAClGc,WAAW,EAAC,wCAAwC;gBACpDC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOgE,OAAO,EAAC,UAAU;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEkE,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEpD,QAAQ,CAACI,QAAS;gBACzBiD,QAAQ,EAAG/B,CAAC,IAAKrB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,QAAQ,EAAEkB,CAAC,CAACgC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACxEX,SAAS,EAAC,wFAAwF;gBAClGe,QAAQ;gBAAAd,QAAA,gBAERzD,OAAA;kBAAQmE,KAAK,EAAC,EAAE;kBAAAV,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C7D,OAAA;kBAAQmE,KAAK,EAAC,kBAAkB;kBAAAV,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1D7D,OAAA;kBAAQmE,KAAK,EAAC,sBAAsB;kBAAAV,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClE7D,OAAA;kBAAQmE,KAAK,EAAC,yBAAyB;kBAAAV,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxE7D,OAAA;kBAAQmE,KAAK,EAAC,eAAe;kBAAAV,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD7D,OAAA;kBAAQmE,KAAK,EAAC,mBAAmB;kBAAAV,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5D7D,OAAA;kBAAQmE,KAAK,EAAC,yBAAyB;kBAAAV,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxE7D,OAAA;kBAAQmE,KAAK,EAAC,mBAAmB;kBAAAV,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5D7D,OAAA;kBAAQmE,KAAK,EAAC,oBAAoB;kBAAAV,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOgE,OAAO,EAAC,UAAU;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEkE,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEpD,QAAQ,CAACG,QAAS;gBACzBkD,QAAQ,EAAG/B,CAAC,IAAKrB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEG,QAAQ,EAAEmB,CAAC,CAACgC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACxEX,SAAS,EAAC,wFAAwF;gBAClGgB,IAAI,EAAC,GAAG;gBACRF,WAAW,EAAC,mEAAmE;gBAC/EC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOgE,OAAO,EAAC,UAAU;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEiE,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEpD,QAAQ,CAACK,QAAS;gBACzBgD,QAAQ,EAAG/B,CAAC,IAAKrB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,QAAQ,EAAEiB,CAAC,CAACgC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACxEX,SAAS,EAAC,wFAAwF;gBAClGc,WAAW,EAAC,yEAAyE;gBACrFC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF7D,OAAA;gBAAGwD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOgE,OAAO,EAAC,SAAS;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAElF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEkE,EAAE,EAAC,SAAS;gBACZC,KAAK,EAAEpD,QAAQ,CAACM,OAAQ;gBACxB+C,QAAQ,EAAG/B,CAAC,IAAKrB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEM,OAAO,EAAEgB,CAAC,CAACgC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEX,SAAS,EAAC,wFAAwF;gBAClGgB,IAAI,EAAC,IAAI;gBACTF,WAAW,EAAC,uDAAuD;gBACnEC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzD,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,KAAK,CAAE;gBAC5C0C,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7D,OAAA;IAAKwD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCzD,OAAA;MAAKwD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DzD,OAAA;QAAKwD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBzD,OAAA;UAAIwD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E7D,OAAA;UAAGwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9CzD,OAAA;UAAKwD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBzD,OAAA;YAAKwD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzD,OAAA;cAAIwD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAErE7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOwD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E7D,OAAA;gBACEmE,KAAK,EAAE1D,MAAO;gBACd2D,QAAQ,EAAG/B,CAAC,IAAK3B,SAAS,CAAC2B,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE;gBAC3CX,SAAS,EAAC,iHAAiH;gBAAAC,QAAA,gBAE3HzD,OAAA;kBAAQmE,KAAK,EAAC,KAAK;kBAAAV,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC7D,OAAA;kBAAQmE,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C7D,OAAA;kBAAQmE,KAAK,EAAC,cAAc;kBAAAV,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClD7D,OAAA;kBAAQmE,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C7D,OAAA;kBAAQmE,KAAK,EAAC,UAAU;kBAAAV,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELrC,eAAe,gBACdxB,OAAA;cACE8D,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,IAAI,CAAE;cAC3C0C,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET7D,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,2EAA2E;cACrFvC,KAAK,EAAC,gBAAgB;cAAAwC,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,eAED7D,OAAA;cAAKwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCzD,OAAA;gBAAIwD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D7D,OAAA;gBAAIwD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvBzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjC7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/B7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9B7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9B,CAACpC,UAAU,IAAID,eAAe,iBAC7BxB,OAAA;kBAAIwD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAClE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBzD,OAAA;YAAKwD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5CzD,OAAA;cAAKwD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CzD,OAAA;gBAAKwD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChDzD,OAAA;kBAAIwD,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,GAAC,mBACjC,EAACH,cAAc,CAACoB,MAAM,EAAC,GAC1C;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCH,cAAc,CAACoB,MAAM,KAAK,CAAC,gBAC1B1E,OAAA;gBAAKwD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BzD,OAAA;kBAAKwD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrD7D,OAAA;kBAAIwD,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtF7D,OAAA;kBAAGwD,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC9BhD,MAAM,KAAK,KAAK,GACb,0CAA0C,GAC1C,0BAA0BA,MAAM,CAACkE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBAAU;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE/D,CAAC,EACHrC,eAAe,IAAIf,MAAM,KAAK,KAAK,iBAClCT,OAAA;kBACE8D,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,IAAI,CAAE;kBAC3C0C,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1E;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GAENP,cAAc,CAACb,GAAG,CAAEc,KAAK,iBACvBvD,OAAA;gBAAoBwD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAClDzD,OAAA;kBAAKwD,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDzD,OAAA;oBAAIwD,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAC1DF,KAAK,CAACtC;kBAAK;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACL7D,OAAA;oBAAMwD,SAAS,EAAE,8CAA8CT,cAAc,CAACQ,KAAK,CAACP,MAAM,CAAC,EAAG;oBAAAS,QAAA,EAC3FF,KAAK,CAACP,MAAM,CAAC2B,OAAO,CAAC,GAAG,EAAE,GAAG;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN7D,OAAA;kBAAGwD,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC3CF,KAAK,CAACrC;gBAAQ;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAEJ7D,OAAA;kBAAKwD,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEzD,OAAA;oBAAAyD,QAAA,gBAAMzD,OAAA;sBAAAyD,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACN,KAAK,CAACqB,eAAe;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7D7D,OAAA;oBAAAyD,QAAA,gBAAMzD,OAAA;sBAAAyD,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACN,KAAK,CAACpC,QAAQ;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxD7D,OAAA;oBAAAyD,QAAA,gBAAMzD,OAAA;sBAAAyD,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACZ,UAAU,CAACM,KAAK,CAACsB,UAAU,CAAC;kBAAA;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACtEN,KAAK,CAACP,MAAM,KAAK,WAAW,IAAIO,KAAK,CAACuB,YAAY,iBACjD9E,OAAA;oBAAAyD,QAAA,gBAAMzD,OAAA;sBAAAyD,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACZ,UAAU,CAACM,KAAK,CAACuB,YAAY,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACzE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAELN,KAAK,CAACnC,QAAQ,IAAImC,KAAK,CAACnC,QAAQ,CAACsD,MAAM,GAAG,CAAC,iBAC1C1E,OAAA;kBAAKwD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvCF,KAAK,CAACnC,QAAQ,CAACqB,GAAG,CAAC,CAACsC,OAAO,EAAEC,KAAK,kBACjChF,OAAA;oBAAkBwD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9EsB;kBAAO,GADCC,KAAK;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eAED7D,OAAA;kBAAKwD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDzD,OAAA;oBAAKwD,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,GAC/DF,KAAK,CAAC0B,cAAc,GAAG,CAAC,iBAAIjF,OAAA;sBAAAyD,QAAA,GAAOF,KAAK,CAAC0B,cAAc,EAAC,YAAU;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACzEN,KAAK,CAAC2B,cAAc,GAAG,CAAC,iBAAIlF,OAAA;sBAAAyD,QAAA,GAAOF,KAAK,CAAC2B,cAAc,EAAC,YAAU;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACN7D,OAAA;oBAAKwD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BzD,OAAA;sBACE8D,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC2C,KAAK,CAAE;sBACvCC,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAClE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRN,KAAK,CAACP,MAAM,KAAK,WAAW,iBAC3BhD,OAAA;sBAAQwD,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,EAAC;oBAEvF;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnDEN,KAAK,CAACW,EAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoDb,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CApZID,kBAAkB;EAAA,QAc+BJ,OAAO;AAAA;AAAAsF,EAAA,GAdxDlF,kBAAkB;AAsZxB,eAAeA,kBAAkB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}