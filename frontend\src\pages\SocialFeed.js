import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useWebSocket } from '../contexts/WebSocketContext';
import PostCard from '../components/PostCard';
import CreatePost from '../components/CreatePost';
import TrendingSidebar from '../components/TrendingSidebar';
import ProfessionalHeader from '../components/ProfessionalHeader';
import AdvancedFilters from '../components/AdvancedFilters';
import ContentScheduler from '../components/ContentScheduler';
import AnalyticsDashboard from '../components/AnalyticsDashboard';
import '../styles/design-system.css';
import axios from 'axios';

const SocialFeed = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'following', 'trending'
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState('feed'); // 'feed', 'analytics', 'scheduler'
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState([]);
  const [dateRange, setDateRange] = useState('all');
  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'popular', 'engagement'

  const { user, isAuthenticated } = useAuth();
  const { socket, isConnected } = useWebSocket();

  // Fetch posts
  const fetchPosts = useCallback(async (pageNum = 1, filterType = filter, reset = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      
      const response = await axios.get('/api/feed', {
        params: {
          page: pageNum,
          limit: 20,
          filter: filterType
        }
      });

      const newPosts = response.data.posts;
      
      if (reset || pageNum === 1) {
        setPosts(newPosts);
      } else {
        setPosts(prev => [...prev, ...newPosts]);
      }

      setHasMore(newPosts.length === 20);
      setPage(pageNum);
      
    } catch (error) {
      console.error('Error fetching posts:', error);
      setError('Failed to load posts');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [filter]);

  // Initial load
  useEffect(() => {
    fetchPosts(1, filter, true);
  }, [filter]);

  // WebSocket listeners for real-time updates
  useEffect(() => {
    if (!socket) return;

    const handleNewPost = (post) => {
      setPosts(prev => [post, ...prev]);
    };

    const handlePostUpdate = (updatedPost) => {
      setPosts(prev => prev.map(post => 
        post.id === updatedPost.id ? { ...post, ...updatedPost } : post
      ));
    };

    const handlePostDelete = (postId) => {
      setPosts(prev => prev.filter(post => post.id !== postId));
    };

    socket.on('new-post', handleNewPost);
    socket.on('post-updated', handlePostUpdate);
    socket.on('post-deleted', handlePostDelete);

    return () => {
      socket.off('new-post');
      socket.off('post-updated');
      socket.off('post-deleted');
    };
  }, [socket]);

  // Handle filter change
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
    setPage(1);
    setPosts([]);
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchPosts(1, filter, true);
  };

  // Load more posts
  const loadMore = () => {
    if (!loading && hasMore) {
      fetchPosts(page + 1, filter, false);
    }
  };

  // Handle new post created
  const handlePostCreated = (newPost) => {
    setPosts(prev => [newPost, ...prev]);
  };

  // Handle post vote
  const handlePostVote = async (postId, voteType) => {
    try {
      const response = await axios.post(`/api/feed/${postId}/vote`, {
        vote_type: voteType
      });

      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              upvotes: response.data.upvotes,
              downvotes: response.data.downvotes,
              user_vote: response.data.user_vote
            }
          : post
      ));

    } catch (error) {
      console.error('Error voting on post:', error);
    }
  };

  // Handle post reaction
  const handlePostReaction = async (postId, reactionType) => {
    try {
      const response = await axios.post(`/api/feed/${postId}/react`, {
        reaction_type: reactionType
      });

      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              reactions: response.data.reactions
            }
          : post
      ));

    } catch (error) {
      console.error('Error reacting to post:', error);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Join the Political Conversation</h2>
          <p className="text-gray-600 mb-6">Sign in to participate in political discussions and share your views</p>
          <a href="/login" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Professional Header */}
      <ProfessionalHeader
        viewMode={viewMode}
        setViewMode={setViewMode}
        isConnected={isConnected}
        user={user}
      />

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">

          {/* Main Content Area */}
          <div className="lg:col-span-3">

            {/* Feed View */}
            {viewMode === 'feed' && (
              <>
                {/* Advanced Filter Bar */}
                <div className="card mb-6 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <h1 className="text-3xl font-bold text-secondary-900">
                        <span className="gradient-primary bg-clip-text text-transparent">
                          Political Feed
                        </span>
                      </h1>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`}></div>
                        <span className="text-sm font-medium text-secondary-600">
                          {isConnected ? 'Live' : 'Offline'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                        className="btn btn-outline btn-sm"
                      >
                        🔍 Advanced Filters
                      </button>
                      <button
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="btn btn-primary btn-sm"
                      >
                        {refreshing ? '🔄' : '↻'} Refresh
                      </button>
                    </div>
                  </div>

                  {/* Advanced Filters */}
                  {showAdvancedFilters && (
                    <AdvancedFilters
                      selectedTopics={selectedTopics}
                      setSelectedTopics={setSelectedTopics}
                      dateRange={dateRange}
                      setDateRange={setDateRange}
                      sortBy={sortBy}
                      setSortBy={setSortBy}
                      onApplyFilters={() => {
                        fetchPosts();
                      }}
                      onClearFilters={() => {
                        setSelectedTopics([]);
                        setDateRange('all');
                        setSortBy('recent');
                        fetchPosts();
                      }}
                    />
                  )}

                  {/* Filter Tabs */}
                  <div className="flex space-x-1 bg-secondary-100 rounded-xl p-1">
                    {[
                      { key: 'all', label: '🌍 All Posts', count: posts.length },
                      { key: 'following', label: '👥 Following', count: 0 },
                      { key: 'trending', label: '🔥 Trending', count: 0 }
                    ].map(tab => (
                      <button
                        key={tab.key}
                        onClick={() => handleFilterChange(tab.key)}
                        className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                          filter === tab.key
                            ? 'bg-white text-primary-600 shadow-sm'
                            : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'
                        }`}
                      >
                        <span>{tab.label}</span>
                        {tab.count > 0 && (
                          <span className="bg-secondary-200 text-secondary-600 px-2 py-0.5 rounded-full text-xs">
                            {tab.count}
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Create Post */}
                <CreatePost onPostCreated={handlePostCreated} />

                {/* Posts */}
                <div className="space-y-6">
                  {loading && posts.length === 0 ? (
                    <div className="space-y-6">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="card p-6 animate-pulse">
                          <div className="flex items-center space-x-3 mb-4">
                            <div className="w-10 h-10 bg-secondary-300 rounded-full"></div>
                            <div className="flex-1">
                              <div className="h-4 bg-secondary-300 rounded w-1/4 mb-2"></div>
                              <div className="h-3 bg-secondary-300 rounded w-1/6"></div>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="h-4 bg-secondary-300 rounded"></div>
                            <div className="h-4 bg-secondary-300 rounded w-3/4"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : posts.length === 0 ? (
                    <div className="card p-12 text-center">
                      <div className="text-6xl mb-4">📭</div>
                      <h3 className="text-lg font-medium text-secondary-900 mb-2">No posts yet</h3>
                      <p className="text-secondary-600 mb-6">
                        {filter === 'following'
                          ? "Follow some users to see their posts here"
                          : "Be the first to start a political discussion!"
                        }
                      </p>
                      {filter === 'following' && (
                        <button
                          onClick={() => handleFilterChange('all')}
                          className="btn btn-primary"
                        >
                          View All Posts
                        </button>
                      )}
                    </div>
                  ) : (
                    <>
                      {posts.map(post => (
                        <PostCard
                          key={post.id}
                          post={post}
                          onVote={handlePostVote}
                          onReaction={handlePostReaction}
                        />
                      ))}

                      {/* Load More */}
                      {hasMore && (
                        <div className="flex justify-center py-6">
                          <button
                            onClick={loadMore}
                            disabled={loading}
                            className="btn btn-secondary btn-lg"
                          >
                            {loading ? (
                              <>
                                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                                Loading...
                              </>
                            ) : (
                              'Load More Posts'
                            )}
                          </button>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {error && (
                  <div className="bg-error-50 border border-error-200 rounded-lg p-4 mt-6">
                    <div className="flex">
                      <div className="text-error-400">⚠️</div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-error-800">Error</h3>
                        <p className="text-sm text-error-700 mt-1">{error}</p>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Analytics View */}
            {viewMode === 'analytics' && (
              <AnalyticsDashboard />
            )}

            {/* Scheduler View */}
            {viewMode === 'scheduler' && (
              <ContentScheduler />
            )}

            {/* Events View */}
            {viewMode === 'events' && (
              <div className="card p-12 text-center">
                <div className="text-6xl mb-4">🎪</div>
                <h3 className="text-lg font-medium text-secondary-900 mb-2">Events Coming Soon</h3>
                <p className="text-secondary-600">
                  Political events and campaign management features will be available soon.
                </p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <TrendingSidebar />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialFeed;
