import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import KnowledgeBase from './pages/KnowledgeBase';
import Discussions from './pages/Discussions';
import LiveDebates from './pages/LiveDebates';
import ResearchRepository from './pages/ResearchRepository';
import Login from './pages/Login';
import Register from './pages/Register';
import Profile from './pages/Profile';
import { AuthProvider } from './contexts/AuthContext';
import { ApiProvider } from './contexts/ApiContext';
import { WebSocketProvider } from './contexts/WebSocketContext';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <ApiProvider>
        <WebSocketProvider>
          <Router>
            <div className="App">
              <Navbar />
              <main className="main-content">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/knowledge" element={<KnowledgeBase />} />
                  <Route path="/discussions" element={<Discussions />} />
                  <Route path="/debates" element={<LiveDebates />} />
                  <Route path="/research" element={<ResearchRepository />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/profile" element={<Profile />} />
                </Routes>
              </main>
            </div>
          </Router>
        </WebSocketProvider>
      </ApiProvider>
    </AuthProvider>
  );
}

export default App;
