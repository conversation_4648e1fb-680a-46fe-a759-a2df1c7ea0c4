import React, { useState, useEffect } from 'react';
import axios from 'axios';

const TrendingSidebar = () => {
  const [trendingHashtags, setTrendingHashtags] = useState([]);
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('topics');

  useEffect(() => {
    fetchTrendingData();
  }, []);

  const fetchTrendingData = async () => {
    try {
      const [hashtagsResponse, topicsResponse] = await Promise.all([
        axios.get('/api/feed/hashtags/trending'),
        axios.get('/api/feed/topics/trending')
      ]);

      setTrendingHashtags(hashtagsResponse.data);
      setTrendingTopics(topicsResponse.data);
    } catch (error) {
      console.error('Error fetching trending data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getCategoryEmoji = (category) => {
    const emojis = {
      domestic: '🏠',
      foreign: '🌍',
      economic: '💰',
      social: '👥',
      environmental: '🌱',
      defense: '🛡️',
      technology: '💻'
    };
    return emojis[category] || '📋';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
            <div className="h-6 bg-gray-300 rounded w-3/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, j) => (
                <div key={j} className="h-4 bg-gray-300 rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Trending Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">🔥</span>
            Trending Now
          </h3>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-100">
          <button
            onClick={() => setActiveTab('topics')}
            className={`flex-1 px-4 py-3 text-sm font-medium ${
              activeTab === 'topics'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Topics
          </button>
          <button
            onClick={() => setActiveTab('hashtags')}
            className={`flex-1 px-4 py-3 text-sm font-medium ${
              activeTab === 'hashtags'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Hashtags
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {activeTab === 'topics' ? (
            <div className="space-y-3">
              {trendingTopics.slice(0, 8).map((topic, index) => (
                <div
                  key={topic.name}
                  className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">
                      {index + 1}
                    </span>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span>{getCategoryEmoji(topic.category)}</span>
                        <h4 className="font-medium text-gray-900">{topic.name}</h4>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatNumber(topic.post_count)} posts
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {topic.trending_score?.toFixed(1) || '0.0'}
                    </div>
                    <div className="text-xs text-gray-500">score</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {trendingHashtags.slice(0, 10).map((hashtag, index) => (
                <div
                  key={hashtag.name}
                  className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">
                      {index + 1}
                    </span>
                    <div>
                      <h4 className="font-medium text-blue-600">#{hashtag.name}</h4>
                      <p className="text-xs text-gray-500">
                        {formatNumber(hashtag.post_count)} posts
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {hashtag.trending_score?.toFixed(1) || '0.0'}
                    </div>
                    <div className="text-xs text-gray-500">score</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {((activeTab === 'topics' && trendingTopics.length === 0) ||
            (activeTab === 'hashtags' && trendingHashtags.length === 0)) && (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">📊</div>
              <p className="text-gray-500 text-sm">
                No trending {activeTab} yet
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <span className="mr-2">📊</span>
          Platform Stats
        </h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Active Discussions</span>
            <span className="font-semibold text-gray-900">1,234</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Live Debates</span>
            <span className="font-semibold text-green-600">5</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Online Users</span>
            <span className="font-semibold text-blue-600">892</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Posts Today</span>
            <span className="font-semibold text-gray-900">456</span>
          </div>
        </div>
      </div>

      {/* Suggested Topics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <span className="mr-2">💡</span>
          Suggested Topics
        </h3>
        <div className="space-y-3">
          {[
            { name: 'Election 2024', category: 'domestic', posts: 1250 },
            { name: 'Climate Action', category: 'environmental', posts: 890 },
            { name: 'Healthcare Debate', category: 'domestic', posts: 756 },
            { name: 'Tech Regulation', category: 'technology', posts: 432 }
          ].map((topic) => (
            <div
              key={topic.name}
              className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span>{getCategoryEmoji(topic.category)}</span>
                <div>
                  <h4 className="font-medium text-gray-900">{topic.name}</h4>
                  <p className="text-xs text-gray-500">
                    {formatNumber(topic.posts)} posts
                  </p>
                </div>
              </div>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                Follow
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Who to Follow */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <span className="mr-2">👥</span>
          Who to Follow
        </h3>
        <div className="space-y-4">
          {[
            { username: 'PoliticalAnalyst', role: 'verified', followers: '12.5K' },
            { username: 'PolicyExpert', role: 'expert', followers: '8.9K' },
            { username: 'CitizenVoice', role: 'user', followers: '3.2K' }
          ].map((user) => (
            <div key={user.username} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {user.username.charAt(0)}
                </div>
                <div>
                  <div className="flex items-center space-x-1">
                    <h4 className="font-medium text-gray-900">{user.username}</h4>
                    {user.role === 'verified' && (
                      <span className="text-blue-500" title="Verified">✓</span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">{user.followers} followers</p>
                </div>
              </div>
              <button className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm hover:bg-blue-700">
                Follow
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrendingSidebar;
