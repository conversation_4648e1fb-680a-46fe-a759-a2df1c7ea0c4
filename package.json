{"name": "politica-backend", "version": "1.0.0", "description": "Backend for Politica - Political Education and Discussion Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["politics", "education", "discussion", "platform"], "author": "", "license": "ISC", "dependencies": {"autoprefixer": "^10.4.21", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}