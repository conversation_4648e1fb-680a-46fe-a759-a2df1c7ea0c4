{"ast": null, "code": "/*! simple-peer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nconst debug = require('debug')('simple-peer');\nconst getBrowserRTC = require('get-browser-rtc');\nconst randombytes = require('randombytes');\nconst stream = require('readable-stream');\nconst queueMicrotask = require('queue-microtask'); // TODO: remove when Node 10 is not supported\nconst errCode = require('err-code');\nconst {\n  Buffer\n} = require('buffer');\nconst MAX_BUFFERED_AMOUNT = 64 * 1024;\nconst ICECOMPLETE_TIMEOUT = 5 * 1000;\nconst CHANNEL_CLOSING_TIMEOUT = 5 * 1000;\n\n// HACK: Filter trickle lines when trickle is disabled #354\nfunction filterTrickle(sdp) {\n  return sdp.replace(/a=ice-options:trickle\\s\\n/g, '');\n}\nfunction warn(message) {\n  console.warn(message);\n}\n\n/**\n * WebRTC peer connection. Same API as node core `net.Socket`, plus a few extra methods.\n * Duplex stream.\n * @param {Object} opts\n */\nclass Peer extends stream.Duplex {\n  constructor(opts) {\n    opts = Object.assign({\n      allowHalfOpen: false\n    }, opts);\n    super(opts);\n    this._id = randombytes(4).toString('hex').slice(0, 7);\n    this._debug('new peer %o', opts);\n    this.channelName = opts.initiator ? opts.channelName || randombytes(20).toString('hex') : null;\n    this.initiator = opts.initiator || false;\n    this.channelConfig = opts.channelConfig || Peer.channelConfig;\n    this.channelNegotiated = this.channelConfig.negotiated;\n    this.config = Object.assign({}, Peer.config, opts.config);\n    this.offerOptions = opts.offerOptions || {};\n    this.answerOptions = opts.answerOptions || {};\n    this.sdpTransform = opts.sdpTransform || (sdp => sdp);\n    this.streams = opts.streams || (opts.stream ? [opts.stream] : []); // support old \"stream\" option\n    this.trickle = opts.trickle !== undefined ? opts.trickle : true;\n    this.allowHalfTrickle = opts.allowHalfTrickle !== undefined ? opts.allowHalfTrickle : false;\n    this.iceCompleteTimeout = opts.iceCompleteTimeout || ICECOMPLETE_TIMEOUT;\n    this.destroyed = false;\n    this.destroying = false;\n    this._connected = false;\n    this.remoteAddress = undefined;\n    this.remoteFamily = undefined;\n    this.remotePort = undefined;\n    this.localAddress = undefined;\n    this.localFamily = undefined;\n    this.localPort = undefined;\n    this._wrtc = opts.wrtc && typeof opts.wrtc === 'object' ? opts.wrtc : getBrowserRTC();\n    if (!this._wrtc) {\n      if (typeof window === 'undefined') {\n        throw errCode(new Error('No WebRTC support: Specify `opts.wrtc` option in this environment'), 'ERR_WEBRTC_SUPPORT');\n      } else {\n        throw errCode(new Error('No WebRTC support: Not a supported browser'), 'ERR_WEBRTC_SUPPORT');\n      }\n    }\n    this._pcReady = false;\n    this._channelReady = false;\n    this._iceComplete = false; // ice candidate trickle done (got null candidate)\n    this._iceCompleteTimer = null; // send an offer/answer anyway after some timeout\n    this._channel = null;\n    this._pendingCandidates = [];\n    this._isNegotiating = false; // is this peer waiting for negotiation to complete?\n    this._firstNegotiation = true;\n    this._batchedNegotiation = false; // batch synchronous negotiations\n    this._queuedNegotiation = false; // is there a queued negotiation request?\n    this._sendersAwaitingStable = [];\n    this._senderMap = new Map();\n    this._closingInterval = null;\n    this._remoteTracks = [];\n    this._remoteStreams = [];\n    this._chunk = null;\n    this._cb = null;\n    this._interval = null;\n    try {\n      this._pc = new this._wrtc.RTCPeerConnection(this.config);\n    } catch (err) {\n      this.destroy(errCode(err, 'ERR_PC_CONSTRUCTOR'));\n      return;\n    }\n\n    // We prefer feature detection whenever possible, but sometimes that's not\n    // possible for certain implementations.\n    this._isReactNativeWebrtc = typeof this._pc._peerConnectionId === 'number';\n    this._pc.oniceconnectionstatechange = () => {\n      this._onIceStateChange();\n    };\n    this._pc.onicegatheringstatechange = () => {\n      this._onIceStateChange();\n    };\n    this._pc.onconnectionstatechange = () => {\n      this._onConnectionStateChange();\n    };\n    this._pc.onsignalingstatechange = () => {\n      this._onSignalingStateChange();\n    };\n    this._pc.onicecandidate = event => {\n      this._onIceCandidate(event);\n    };\n\n    // HACK: Fix for odd Firefox behavior, see: https://github.com/feross/simple-peer/pull/783\n    if (typeof this._pc.peerIdentity === 'object') {\n      this._pc.peerIdentity.catch(err => {\n        this.destroy(errCode(err, 'ERR_PC_PEER_IDENTITY'));\n      });\n    }\n\n    // Other spec events, unused by this implementation:\n    // - onconnectionstatechange\n    // - onicecandidateerror\n    // - onfingerprintfailure\n    // - onnegotiationneeded\n\n    if (this.initiator || this.channelNegotiated) {\n      this._setupData({\n        channel: this._pc.createDataChannel(this.channelName, this.channelConfig)\n      });\n    } else {\n      this._pc.ondatachannel = event => {\n        this._setupData(event);\n      };\n    }\n    if (this.streams) {\n      this.streams.forEach(stream => {\n        this.addStream(stream);\n      });\n    }\n    this._pc.ontrack = event => {\n      this._onTrack(event);\n    };\n    this._debug('initial negotiation');\n    this._needsNegotiation();\n    this._onFinishBound = () => {\n      this._onFinish();\n    };\n    this.once('finish', this._onFinishBound);\n  }\n  get bufferSize() {\n    return this._channel && this._channel.bufferedAmount || 0;\n  }\n\n  // HACK: it's possible channel.readyState is \"closing\" before peer.destroy() fires\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n  get connected() {\n    return this._connected && this._channel.readyState === 'open';\n  }\n  address() {\n    return {\n      port: this.localPort,\n      family: this.localFamily,\n      address: this.localAddress\n    };\n  }\n  signal(data) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot signal after peer is destroyed'), 'ERR_DESTROYED');\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data);\n      } catch (err) {\n        data = {};\n      }\n    }\n    this._debug('signal()');\n    if (data.renegotiate && this.initiator) {\n      this._debug('got request to renegotiate');\n      this._needsNegotiation();\n    }\n    if (data.transceiverRequest && this.initiator) {\n      this._debug('got request for transceiver');\n      this.addTransceiver(data.transceiverRequest.kind, data.transceiverRequest.init);\n    }\n    if (data.candidate) {\n      if (this._pc.remoteDescription && this._pc.remoteDescription.type) {\n        this._addIceCandidate(data.candidate);\n      } else {\n        this._pendingCandidates.push(data.candidate);\n      }\n    }\n    if (data.sdp) {\n      this._pc.setRemoteDescription(new this._wrtc.RTCSessionDescription(data)).then(() => {\n        if (this.destroyed) return;\n        this._pendingCandidates.forEach(candidate => {\n          this._addIceCandidate(candidate);\n        });\n        this._pendingCandidates = [];\n        if (this._pc.remoteDescription.type === 'offer') this._createAnswer();\n      }).catch(err => {\n        this.destroy(errCode(err, 'ERR_SET_REMOTE_DESCRIPTION'));\n      });\n    }\n    if (!data.sdp && !data.candidate && !data.renegotiate && !data.transceiverRequest) {\n      this.destroy(errCode(new Error('signal() called with invalid signal data'), 'ERR_SIGNALING'));\n    }\n  }\n  _addIceCandidate(candidate) {\n    const iceCandidateObj = new this._wrtc.RTCIceCandidate(candidate);\n    this._pc.addIceCandidate(iceCandidateObj).catch(err => {\n      if (!iceCandidateObj.address || iceCandidateObj.address.endsWith('.local')) {\n        warn('Ignoring unsupported ICE candidate.');\n      } else {\n        this.destroy(errCode(err, 'ERR_ADD_ICE_CANDIDATE'));\n      }\n    });\n  }\n\n  /**\n   * Send text/binary data to the remote peer.\n   * @param {ArrayBufferView|ArrayBuffer|Buffer|string|Blob} chunk\n   */\n  send(chunk) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot send after peer is destroyed'), 'ERR_DESTROYED');\n    this._channel.send(chunk);\n  }\n\n  /**\n   * Add a Transceiver to the connection.\n   * @param {String} kind\n   * @param {Object} init\n   */\n  addTransceiver(kind, init) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot addTransceiver after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('addTransceiver()');\n    if (this.initiator) {\n      try {\n        this._pc.addTransceiver(kind, init);\n        this._needsNegotiation();\n      } catch (err) {\n        this.destroy(errCode(err, 'ERR_ADD_TRANSCEIVER'));\n      }\n    } else {\n      this.emit('signal', {\n        // request initiator to renegotiate\n        type: 'transceiverRequest',\n        transceiverRequest: {\n          kind,\n          init\n        }\n      });\n    }\n  }\n\n  /**\n   * Add a MediaStream to the connection.\n   * @param {MediaStream} stream\n   */\n  addStream(stream) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot addStream after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('addStream()');\n    stream.getTracks().forEach(track => {\n      this.addTrack(track, stream);\n    });\n  }\n\n  /**\n   * Add a MediaStreamTrack to the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  addTrack(track, stream) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot addTrack after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('addTrack()');\n    const submap = this._senderMap.get(track) || new Map(); // nested Maps map [track, stream] to sender\n    let sender = submap.get(stream);\n    if (!sender) {\n      sender = this._pc.addTrack(track, stream);\n      submap.set(stream, sender);\n      this._senderMap.set(track, submap);\n      this._needsNegotiation();\n    } else if (sender.removed) {\n      throw errCode(new Error('Track has been removed. You should enable/disable tracks that you want to re-add.'), 'ERR_SENDER_REMOVED');\n    } else {\n      throw errCode(new Error('Track has already been added to that stream.'), 'ERR_SENDER_ALREADY_ADDED');\n    }\n  }\n\n  /**\n   * Replace a MediaStreamTrack by another in the connection.\n   * @param {MediaStreamTrack} oldTrack\n   * @param {MediaStreamTrack} newTrack\n   * @param {MediaStream} stream\n   */\n  replaceTrack(oldTrack, newTrack, stream) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot replaceTrack after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('replaceTrack()');\n    const submap = this._senderMap.get(oldTrack);\n    const sender = submap ? submap.get(stream) : null;\n    if (!sender) {\n      throw errCode(new Error('Cannot replace track that was never added.'), 'ERR_TRACK_NOT_ADDED');\n    }\n    if (newTrack) this._senderMap.set(newTrack, submap);\n    if (sender.replaceTrack != null) {\n      sender.replaceTrack(newTrack);\n    } else {\n      this.destroy(errCode(new Error('replaceTrack is not supported in this browser'), 'ERR_UNSUPPORTED_REPLACETRACK'));\n    }\n  }\n\n  /**\n   * Remove a MediaStreamTrack from the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  removeTrack(track, stream) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot removeTrack after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('removeSender()');\n    const submap = this._senderMap.get(track);\n    const sender = submap ? submap.get(stream) : null;\n    if (!sender) {\n      throw errCode(new Error('Cannot remove track that was never added.'), 'ERR_TRACK_NOT_ADDED');\n    }\n    try {\n      sender.removed = true;\n      this._pc.removeTrack(sender);\n    } catch (err) {\n      if (err.name === 'NS_ERROR_UNEXPECTED') {\n        this._sendersAwaitingStable.push(sender); // HACK: Firefox must wait until (signalingState === stable) https://bugzilla.mozilla.org/show_bug.cgi?id=1133874\n      } else {\n        this.destroy(errCode(err, 'ERR_REMOVE_TRACK'));\n      }\n    }\n    this._needsNegotiation();\n  }\n\n  /**\n   * Remove a MediaStream from the connection.\n   * @param {MediaStream} stream\n   */\n  removeStream(stream) {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot removeStream after peer is destroyed'), 'ERR_DESTROYED');\n    this._debug('removeSenders()');\n    stream.getTracks().forEach(track => {\n      this.removeTrack(track, stream);\n    });\n  }\n  _needsNegotiation() {\n    this._debug('_needsNegotiation');\n    if (this._batchedNegotiation) return; // batch synchronous renegotiations\n    this._batchedNegotiation = true;\n    queueMicrotask(() => {\n      this._batchedNegotiation = false;\n      if (this.initiator || !this._firstNegotiation) {\n        this._debug('starting batched negotiation');\n        this.negotiate();\n      } else {\n        this._debug('non-initiator initial negotiation request discarded');\n      }\n      this._firstNegotiation = false;\n    });\n  }\n  negotiate() {\n    if (this.destroying) return;\n    if (this.destroyed) throw errCode(new Error('cannot negotiate after peer is destroyed'), 'ERR_DESTROYED');\n    if (this.initiator) {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true;\n        this._debug('already negotiating, queueing');\n      } else {\n        this._debug('start negotiation');\n        setTimeout(() => {\n          // HACK: Chrome crashes if we immediately call createOffer\n          this._createOffer();\n        }, 0);\n      }\n    } else {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true;\n        this._debug('already negotiating, queueing');\n      } else {\n        this._debug('requesting negotiation from initiator');\n        this.emit('signal', {\n          // request initiator to renegotiate\n          type: 'renegotiate',\n          renegotiate: true\n        });\n      }\n    }\n    this._isNegotiating = true;\n  }\n\n  // TODO: Delete this method once readable-stream is updated to contain a default\n  // implementation of destroy() that automatically calls _destroy()\n  // See: https://github.com/nodejs/readable-stream/issues/283\n  destroy(err) {\n    this._destroy(err, () => {});\n  }\n  _destroy(err, cb) {\n    if (this.destroyed || this.destroying) return;\n    this.destroying = true;\n    this._debug('destroying (error: %s)', err && (err.message || err));\n    queueMicrotask(() => {\n      // allow events concurrent with the call to _destroy() to fire (see #692)\n      this.destroyed = true;\n      this.destroying = false;\n      this._debug('destroy (error: %s)', err && (err.message || err));\n      this.readable = this.writable = false;\n      if (!this._readableState.ended) this.push(null);\n      if (!this._writableState.finished) this.end();\n      this._connected = false;\n      this._pcReady = false;\n      this._channelReady = false;\n      this._remoteTracks = null;\n      this._remoteStreams = null;\n      this._senderMap = null;\n      clearInterval(this._closingInterval);\n      this._closingInterval = null;\n      clearInterval(this._interval);\n      this._interval = null;\n      this._chunk = null;\n      this._cb = null;\n      if (this._onFinishBound) this.removeListener('finish', this._onFinishBound);\n      this._onFinishBound = null;\n      if (this._channel) {\n        try {\n          this._channel.close();\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._channel.onmessage = null;\n        this._channel.onopen = null;\n        this._channel.onclose = null;\n        this._channel.onerror = null;\n      }\n      if (this._pc) {\n        try {\n          this._pc.close();\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._pc.oniceconnectionstatechange = null;\n        this._pc.onicegatheringstatechange = null;\n        this._pc.onsignalingstatechange = null;\n        this._pc.onicecandidate = null;\n        this._pc.ontrack = null;\n        this._pc.ondatachannel = null;\n      }\n      this._pc = null;\n      this._channel = null;\n      if (err) this.emit('error', err);\n      this.emit('close');\n      cb();\n    });\n  }\n  _setupData(event) {\n    if (!event.channel) {\n      // In some situations `pc.createDataChannel()` returns `undefined` (in wrtc),\n      // which is invalid behavior. Handle it gracefully.\n      // See: https://github.com/feross/simple-peer/issues/163\n      return this.destroy(errCode(new Error('Data channel event is missing `channel` property'), 'ERR_DATA_CHANNEL'));\n    }\n    this._channel = event.channel;\n    this._channel.binaryType = 'arraybuffer';\n    if (typeof this._channel.bufferedAmountLowThreshold === 'number') {\n      this._channel.bufferedAmountLowThreshold = MAX_BUFFERED_AMOUNT;\n    }\n    this.channelName = this._channel.label;\n    this._channel.onmessage = event => {\n      this._onChannelMessage(event);\n    };\n    this._channel.onbufferedamountlow = () => {\n      this._onChannelBufferedAmountLow();\n    };\n    this._channel.onopen = () => {\n      this._onChannelOpen();\n    };\n    this._channel.onclose = () => {\n      this._onChannelClose();\n    };\n    this._channel.onerror = event => {\n      const err = event.error instanceof Error ? event.error : new Error(`Datachannel error: ${event.message} ${event.filename}:${event.lineno}:${event.colno}`);\n      this.destroy(errCode(err, 'ERR_DATA_CHANNEL'));\n    };\n\n    // HACK: Chrome will sometimes get stuck in readyState \"closing\", let's check for this condition\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n    let isClosing = false;\n    this._closingInterval = setInterval(() => {\n      // No \"onclosing\" event\n      if (this._channel && this._channel.readyState === 'closing') {\n        if (isClosing) this._onChannelClose(); // closing timed out: equivalent to onclose firing\n        isClosing = true;\n      } else {\n        isClosing = false;\n      }\n    }, CHANNEL_CLOSING_TIMEOUT);\n  }\n  _read() {}\n  _write(chunk, encoding, cb) {\n    if (this.destroyed) return cb(errCode(new Error('cannot write after peer is destroyed'), 'ERR_DATA_CHANNEL'));\n    if (this._connected) {\n      try {\n        this.send(chunk);\n      } catch (err) {\n        return this.destroy(errCode(err, 'ERR_DATA_CHANNEL'));\n      }\n      if (this._channel.bufferedAmount > MAX_BUFFERED_AMOUNT) {\n        this._debug('start backpressure: bufferedAmount %d', this._channel.bufferedAmount);\n        this._cb = cb;\n      } else {\n        cb(null);\n      }\n    } else {\n      this._debug('write before connect');\n      this._chunk = chunk;\n      this._cb = cb;\n    }\n  }\n\n  // When stream finishes writing, close socket. Half open connections are not\n  // supported.\n  _onFinish() {\n    if (this.destroyed) return;\n\n    // Wait a bit before destroying so the socket flushes.\n    // TODO: is there a more reliable way to accomplish this?\n    const destroySoon = () => {\n      setTimeout(() => this.destroy(), 1000);\n    };\n    if (this._connected) {\n      destroySoon();\n    } else {\n      this.once('connect', destroySoon);\n    }\n  }\n  _startIceCompleteTimeout() {\n    if (this.destroyed) return;\n    if (this._iceCompleteTimer) return;\n    this._debug('started iceComplete timeout');\n    this._iceCompleteTimer = setTimeout(() => {\n      if (!this._iceComplete) {\n        this._iceComplete = true;\n        this._debug('iceComplete timeout completed');\n        this.emit('iceTimeout');\n        this.emit('_iceComplete');\n      }\n    }, this.iceCompleteTimeout);\n  }\n  _createOffer() {\n    if (this.destroyed) return;\n    this._pc.createOffer(this.offerOptions).then(offer => {\n      if (this.destroyed) return;\n      if (!this.trickle && !this.allowHalfTrickle) offer.sdp = filterTrickle(offer.sdp);\n      offer.sdp = this.sdpTransform(offer.sdp);\n      const sendOffer = () => {\n        if (this.destroyed) return;\n        const signal = this._pc.localDescription || offer;\n        this._debug('signal');\n        this.emit('signal', {\n          type: signal.type,\n          sdp: signal.sdp\n        });\n      };\n      const onSuccess = () => {\n        this._debug('createOffer success');\n        if (this.destroyed) return;\n        if (this.trickle || this._iceComplete) sendOffer();else this.once('_iceComplete', sendOffer); // wait for candidates\n      };\n      const onError = err => {\n        this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'));\n      };\n      this._pc.setLocalDescription(offer).then(onSuccess).catch(onError);\n    }).catch(err => {\n      this.destroy(errCode(err, 'ERR_CREATE_OFFER'));\n    });\n  }\n  _requestMissingTransceivers() {\n    if (this._pc.getTransceivers) {\n      this._pc.getTransceivers().forEach(transceiver => {\n        if (!transceiver.mid && transceiver.sender.track && !transceiver.requested) {\n          transceiver.requested = true; // HACK: Safari returns negotiated transceivers with a null mid\n          this.addTransceiver(transceiver.sender.track.kind);\n        }\n      });\n    }\n  }\n  _createAnswer() {\n    if (this.destroyed) return;\n    this._pc.createAnswer(this.answerOptions).then(answer => {\n      if (this.destroyed) return;\n      if (!this.trickle && !this.allowHalfTrickle) answer.sdp = filterTrickle(answer.sdp);\n      answer.sdp = this.sdpTransform(answer.sdp);\n      const sendAnswer = () => {\n        if (this.destroyed) return;\n        const signal = this._pc.localDescription || answer;\n        this._debug('signal');\n        this.emit('signal', {\n          type: signal.type,\n          sdp: signal.sdp\n        });\n        if (!this.initiator) this._requestMissingTransceivers();\n      };\n      const onSuccess = () => {\n        if (this.destroyed) return;\n        if (this.trickle || this._iceComplete) sendAnswer();else this.once('_iceComplete', sendAnswer);\n      };\n      const onError = err => {\n        this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'));\n      };\n      this._pc.setLocalDescription(answer).then(onSuccess).catch(onError);\n    }).catch(err => {\n      this.destroy(errCode(err, 'ERR_CREATE_ANSWER'));\n    });\n  }\n  _onConnectionStateChange() {\n    if (this.destroyed) return;\n    if (this._pc.connectionState === 'failed') {\n      this.destroy(errCode(new Error('Connection failed.'), 'ERR_CONNECTION_FAILURE'));\n    }\n  }\n  _onIceStateChange() {\n    if (this.destroyed) return;\n    const iceConnectionState = this._pc.iceConnectionState;\n    const iceGatheringState = this._pc.iceGatheringState;\n    this._debug('iceStateChange (connection: %s) (gathering: %s)', iceConnectionState, iceGatheringState);\n    this.emit('iceStateChange', iceConnectionState, iceGatheringState);\n    if (iceConnectionState === 'connected' || iceConnectionState === 'completed') {\n      this._pcReady = true;\n      this._maybeReady();\n    }\n    if (iceConnectionState === 'failed') {\n      this.destroy(errCode(new Error('Ice connection failed.'), 'ERR_ICE_CONNECTION_FAILURE'));\n    }\n    if (iceConnectionState === 'closed') {\n      this.destroy(errCode(new Error('Ice connection closed.'), 'ERR_ICE_CONNECTION_CLOSED'));\n    }\n  }\n  getStats(cb) {\n    // statreports can come with a value array instead of properties\n    const flattenValues = report => {\n      if (Object.prototype.toString.call(report.values) === '[object Array]') {\n        report.values.forEach(value => {\n          Object.assign(report, value);\n        });\n      }\n      return report;\n    };\n\n    // Promise-based getStats() (standard)\n    if (this._pc.getStats.length === 0 || this._isReactNativeWebrtc) {\n      this._pc.getStats().then(res => {\n        const reports = [];\n        res.forEach(report => {\n          reports.push(flattenValues(report));\n        });\n        cb(null, reports);\n      }, err => cb(err));\n\n      // Single-parameter callback-based getStats() (non-standard)\n    } else if (this._pc.getStats.length > 0) {\n      this._pc.getStats(res => {\n        // If we destroy connection in `connect` callback this code might happen to run when actual connection is already closed\n        if (this.destroyed) return;\n        const reports = [];\n        res.result().forEach(result => {\n          const report = {};\n          result.names().forEach(name => {\n            report[name] = result.stat(name);\n          });\n          report.id = result.id;\n          report.type = result.type;\n          report.timestamp = result.timestamp;\n          reports.push(flattenValues(report));\n        });\n        cb(null, reports);\n      }, err => cb(err));\n\n      // Unknown browser, skip getStats() since it's anyone's guess which style of\n      // getStats() they implement.\n    } else {\n      cb(null, []);\n    }\n  }\n  _maybeReady() {\n    this._debug('maybeReady pc %s channel %s', this._pcReady, this._channelReady);\n    if (this._connected || this._connecting || !this._pcReady || !this._channelReady) return;\n    this._connecting = true;\n\n    // HACK: We can't rely on order here, for details see https://github.com/js-platform/node-webrtc/issues/339\n    const findCandidatePair = () => {\n      if (this.destroyed) return;\n      this.getStats((err, items) => {\n        if (this.destroyed) return;\n\n        // Treat getStats error as non-fatal. It's not essential.\n        if (err) items = [];\n        const remoteCandidates = {};\n        const localCandidates = {};\n        const candidatePairs = {};\n        let foundSelectedCandidatePair = false;\n        items.forEach(item => {\n          // TODO: Once all browsers support the hyphenated stats report types, remove\n          // the non-hypenated ones\n          if (item.type === 'remotecandidate' || item.type === 'remote-candidate') {\n            remoteCandidates[item.id] = item;\n          }\n          if (item.type === 'localcandidate' || item.type === 'local-candidate') {\n            localCandidates[item.id] = item;\n          }\n          if (item.type === 'candidatepair' || item.type === 'candidate-pair') {\n            candidatePairs[item.id] = item;\n          }\n        });\n        const setSelectedCandidatePair = selectedCandidatePair => {\n          foundSelectedCandidatePair = true;\n          let local = localCandidates[selectedCandidatePair.localCandidateId];\n          if (local && (local.ip || local.address)) {\n            // Spec\n            this.localAddress = local.ip || local.address;\n            this.localPort = Number(local.port);\n          } else if (local && local.ipAddress) {\n            // Firefox\n            this.localAddress = local.ipAddress;\n            this.localPort = Number(local.portNumber);\n          } else if (typeof selectedCandidatePair.googLocalAddress === 'string') {\n            // TODO: remove this once Chrome 58 is released\n            local = selectedCandidatePair.googLocalAddress.split(':');\n            this.localAddress = local[0];\n            this.localPort = Number(local[1]);\n          }\n          if (this.localAddress) {\n            this.localFamily = this.localAddress.includes(':') ? 'IPv6' : 'IPv4';\n          }\n          let remote = remoteCandidates[selectedCandidatePair.remoteCandidateId];\n          if (remote && (remote.ip || remote.address)) {\n            // Spec\n            this.remoteAddress = remote.ip || remote.address;\n            this.remotePort = Number(remote.port);\n          } else if (remote && remote.ipAddress) {\n            // Firefox\n            this.remoteAddress = remote.ipAddress;\n            this.remotePort = Number(remote.portNumber);\n          } else if (typeof selectedCandidatePair.googRemoteAddress === 'string') {\n            // TODO: remove this once Chrome 58 is released\n            remote = selectedCandidatePair.googRemoteAddress.split(':');\n            this.remoteAddress = remote[0];\n            this.remotePort = Number(remote[1]);\n          }\n          if (this.remoteAddress) {\n            this.remoteFamily = this.remoteAddress.includes(':') ? 'IPv6' : 'IPv4';\n          }\n          this._debug('connect local: %s:%s remote: %s:%s', this.localAddress, this.localPort, this.remoteAddress, this.remotePort);\n        };\n        items.forEach(item => {\n          // Spec-compliant\n          if (item.type === 'transport' && item.selectedCandidatePairId) {\n            setSelectedCandidatePair(candidatePairs[item.selectedCandidatePairId]);\n          }\n\n          // Old implementations\n          if (item.type === 'googCandidatePair' && item.googActiveConnection === 'true' || (item.type === 'candidatepair' || item.type === 'candidate-pair') && item.selected) {\n            setSelectedCandidatePair(item);\n          }\n        });\n\n        // Ignore candidate pair selection in browsers like Safari 11 that do not have any local or remote candidates\n        // But wait until at least 1 candidate pair is available\n        if (!foundSelectedCandidatePair && (!Object.keys(candidatePairs).length || Object.keys(localCandidates).length)) {\n          setTimeout(findCandidatePair, 100);\n          return;\n        } else {\n          this._connecting = false;\n          this._connected = true;\n        }\n        if (this._chunk) {\n          try {\n            this.send(this._chunk);\n          } catch (err) {\n            return this.destroy(errCode(err, 'ERR_DATA_CHANNEL'));\n          }\n          this._chunk = null;\n          this._debug('sent chunk from \"write before connect\"');\n          const cb = this._cb;\n          this._cb = null;\n          cb(null);\n        }\n\n        // If `bufferedAmountLowThreshold` and 'onbufferedamountlow' are unsupported,\n        // fallback to using setInterval to implement backpressure.\n        if (typeof this._channel.bufferedAmountLowThreshold !== 'number') {\n          this._interval = setInterval(() => this._onInterval(), 150);\n          if (this._interval.unref) this._interval.unref();\n        }\n        this._debug('connect');\n        this.emit('connect');\n      });\n    };\n    findCandidatePair();\n  }\n  _onInterval() {\n    if (!this._cb || !this._channel || this._channel.bufferedAmount > MAX_BUFFERED_AMOUNT) {\n      return;\n    }\n    this._onChannelBufferedAmountLow();\n  }\n  _onSignalingStateChange() {\n    if (this.destroyed) return;\n    if (this._pc.signalingState === 'stable') {\n      this._isNegotiating = false;\n\n      // HACK: Firefox doesn't yet support removing tracks when signalingState !== 'stable'\n      this._debug('flushing sender queue', this._sendersAwaitingStable);\n      this._sendersAwaitingStable.forEach(sender => {\n        this._pc.removeTrack(sender);\n        this._queuedNegotiation = true;\n      });\n      this._sendersAwaitingStable = [];\n      if (this._queuedNegotiation) {\n        this._debug('flushing negotiation queue');\n        this._queuedNegotiation = false;\n        this._needsNegotiation(); // negotiate again\n      } else {\n        this._debug('negotiated');\n        this.emit('negotiated');\n      }\n    }\n    this._debug('signalingStateChange %s', this._pc.signalingState);\n    this.emit('signalingStateChange', this._pc.signalingState);\n  }\n  _onIceCandidate(event) {\n    if (this.destroyed) return;\n    if (event.candidate && this.trickle) {\n      this.emit('signal', {\n        type: 'candidate',\n        candidate: {\n          candidate: event.candidate.candidate,\n          sdpMLineIndex: event.candidate.sdpMLineIndex,\n          sdpMid: event.candidate.sdpMid\n        }\n      });\n    } else if (!event.candidate && !this._iceComplete) {\n      this._iceComplete = true;\n      this.emit('_iceComplete');\n    }\n    // as soon as we've received one valid candidate start timeout\n    if (event.candidate) {\n      this._startIceCompleteTimeout();\n    }\n  }\n  _onChannelMessage(event) {\n    if (this.destroyed) return;\n    let data = event.data;\n    if (data instanceof ArrayBuffer) data = Buffer.from(data);\n    this.push(data);\n  }\n  _onChannelBufferedAmountLow() {\n    if (this.destroyed || !this._cb) return;\n    this._debug('ending backpressure: bufferedAmount %d', this._channel.bufferedAmount);\n    const cb = this._cb;\n    this._cb = null;\n    cb(null);\n  }\n  _onChannelOpen() {\n    if (this._connected || this.destroyed) return;\n    this._debug('on channel open');\n    this._channelReady = true;\n    this._maybeReady();\n  }\n  _onChannelClose() {\n    if (this.destroyed) return;\n    this._debug('on channel close');\n    this.destroy();\n  }\n  _onTrack(event) {\n    if (this.destroyed) return;\n    event.streams.forEach(eventStream => {\n      this._debug('on track');\n      this.emit('track', event.track, eventStream);\n      this._remoteTracks.push({\n        track: event.track,\n        stream: eventStream\n      });\n      if (this._remoteStreams.some(remoteStream => {\n        return remoteStream.id === eventStream.id;\n      })) return; // Only fire one 'stream' event, even though there may be multiple tracks per stream\n\n      this._remoteStreams.push(eventStream);\n      queueMicrotask(() => {\n        this._debug('on stream');\n        this.emit('stream', eventStream); // ensure all tracks have been added\n      });\n    });\n  }\n  _debug() {\n    const args = [].slice.call(arguments);\n    args[0] = '[' + this._id + '] ' + args[0];\n    debug.apply(null, args);\n  }\n}\nPeer.WEBRTC_SUPPORT = !!getBrowserRTC();\n\n/**\n * Expose peer and data channel config for overriding all Peer\n * instances. Otherwise, just set opts.config or opts.channelConfig\n * when constructing a Peer.\n */\nPeer.config = {\n  iceServers: [{\n    urls: ['stun:stun.l.google.com:19302', 'stun:global.stun.twilio.com:3478']\n  }],\n  sdpSemantics: 'unified-plan'\n};\nPeer.channelConfig = {};\nmodule.exports = Peer;", "map": {"version": 3, "names": ["debug", "require", "getBrowserRTC", "randombytes", "stream", "queueMicrotask", "errCode", "<PERSON><PERSON><PERSON>", "MAX_BUFFERED_AMOUNT", "ICECOMPLETE_TIMEOUT", "CHANNEL_CLOSING_TIMEOUT", "filterTrickle", "sdp", "replace", "warn", "message", "console", "<PERSON><PERSON>", "Duplex", "constructor", "opts", "Object", "assign", "allowHalfOpen", "_id", "toString", "slice", "_debug", "channelName", "initiator", "channelConfig", "channelNegotiated", "negotiated", "config", "offerOptions", "answerOptions", "sdpTransform", "streams", "trickle", "undefined", "allowHalfTrickle", "iceCompleteTimeout", "destroyed", "destroying", "_connected", "remoteAddress", "remoteFamily", "remotePort", "localAddress", "localFamily", "localPort", "_wrtc", "wrtc", "window", "Error", "_pc<PERSON>eady", "_channelReady", "_iceComplete", "_iceCompleteTimer", "_channel", "_pendingCandidates", "_isNegotiating", "_firstNegotiation", "_batchedNegotiation", "_queuedNegotiation", "_sendersAwaitingStable", "_senderMap", "Map", "_closingInterval", "_remoteTracks", "_remoteStreams", "_chunk", "_cb", "_interval", "_pc", "RTCPeerConnection", "err", "destroy", "_isReactNativeWebrtc", "_peerConnectionId", "oniceconnectionstatechange", "_onIceStateChange", "onicegatheringstatechange", "onconnectionstatechange", "_onConnectionStateChange", "onsignalingstatechange", "_onSignalingStateChange", "onicecandidate", "event", "_onIceCandidate", "peerIdentity", "catch", "_setupData", "channel", "createDataChannel", "ondatachannel", "for<PERSON>ach", "addStream", "ontrack", "_onTrack", "_needsNegotiation", "_onFinishBound", "_onFinish", "once", "bufferSize", "bufferedAmount", "connected", "readyState", "address", "port", "family", "signal", "data", "JSON", "parse", "renegotiate", "transceiverRequest", "addTransceiver", "kind", "init", "candidate", "remoteDescription", "type", "_addIceCandidate", "push", "setRemoteDescription", "RTCSessionDescription", "then", "_createAnswer", "iceCandidateObj", "RTCIceCandidate", "addIceCandidate", "endsWith", "send", "chunk", "emit", "getTracks", "track", "addTrack", "submap", "get", "sender", "set", "removed", "replaceTrack", "oldTrack", "newTrack", "removeTrack", "name", "removeStream", "negotiate", "setTimeout", "_createOffer", "_destroy", "cb", "readable", "writable", "_readableState", "ended", "_writableState", "finished", "end", "clearInterval", "removeListener", "close", "onmessage", "onopen", "onclose", "onerror", "binaryType", "bufferedAmountLowThreshold", "label", "_onChannelMessage", "onbufferedamountlow", "_onChannelBufferedAmountLow", "_onChannelOpen", "_onChannelClose", "error", "filename", "lineno", "colno", "isClosing", "setInterval", "_read", "_write", "encoding", "destroySoon", "_startIceCompleteTimeout", "createOffer", "offer", "sendOffer", "localDescription", "onSuccess", "onError", "setLocalDescription", "_requestMissingTransceivers", "getTransceivers", "transceiver", "mid", "requested", "createAnswer", "answer", "sendAnswer", "connectionState", "iceConnectionState", "iceGatheringState", "_<PERSON><PERSON><PERSON><PERSON>", "getStats", "flattenV<PERSON>ues", "report", "prototype", "call", "values", "value", "length", "res", "reports", "result", "names", "stat", "id", "timestamp", "_connecting", "findCandidatePair", "items", "remoteCandidates", "localCandidates", "candidate<PERSON><PERSON><PERSON>", "foundSelectedCandidatePair", "item", "setSelectedCandidatePair", "selectedCandidatePair", "local", "localCandidateId", "ip", "Number", "ip<PERSON><PERSON><PERSON>", "portNumber", "googLocalAddress", "split", "includes", "remote", "remoteCandidateId", "googRemoteAddress", "selectedCandidatePairId", "googActiveConnection", "selected", "keys", "_onInterval", "unref", "signalingState", "sdpMLineIndex", "sdpMid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "eventStream", "some", "remoteStream", "args", "arguments", "apply", "WEBRTC_SUPPORT", "iceServers", "urls", "sdpSemantics", "module", "exports"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/simple-peer/index.js"], "sourcesContent": ["/*! simple-peer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nconst debug = require('debug')('simple-peer')\nconst getBrowserRTC = require('get-browser-rtc')\nconst randombytes = require('randombytes')\nconst stream = require('readable-stream')\nconst queueMicrotask = require('queue-microtask') // TODO: remove when Node 10 is not supported\nconst errCode = require('err-code')\nconst { Buffer } = require('buffer')\n\nconst MAX_BUFFERED_AMOUNT = 64 * 1024\nconst ICECOMPLETE_TIMEOUT = 5 * 1000\nconst CHANNEL_CLOSING_TIMEOUT = 5 * 1000\n\n// HACK: Filter trickle lines when trickle is disabled #354\nfunction filterTrickle (sdp) {\n  return sdp.replace(/a=ice-options:trickle\\s\\n/g, '')\n}\n\nfunction warn (message) {\n  console.warn(message)\n}\n\n/**\n * WebRTC peer connection. Same API as node core `net.Socket`, plus a few extra methods.\n * Duplex stream.\n * @param {Object} opts\n */\nclass Peer extends stream.Duplex {\n  constructor (opts) {\n    opts = Object.assign({\n      allowHalfOpen: false\n    }, opts)\n\n    super(opts)\n\n    this._id = randombytes(4).toString('hex').slice(0, 7)\n    this._debug('new peer %o', opts)\n\n    this.channelName = opts.initiator\n      ? opts.channelName || randombytes(20).toString('hex')\n      : null\n\n    this.initiator = opts.initiator || false\n    this.channelConfig = opts.channelConfig || Peer.channelConfig\n    this.channelNegotiated = this.channelConfig.negotiated\n    this.config = Object.assign({}, Peer.config, opts.config)\n    this.offerOptions = opts.offerOptions || {}\n    this.answerOptions = opts.answerOptions || {}\n    this.sdpTransform = opts.sdpTransform || (sdp => sdp)\n    this.streams = opts.streams || (opts.stream ? [opts.stream] : []) // support old \"stream\" option\n    this.trickle = opts.trickle !== undefined ? opts.trickle : true\n    this.allowHalfTrickle = opts.allowHalfTrickle !== undefined ? opts.allowHalfTrickle : false\n    this.iceCompleteTimeout = opts.iceCompleteTimeout || ICECOMPLETE_TIMEOUT\n\n    this.destroyed = false\n    this.destroying = false\n    this._connected = false\n\n    this.remoteAddress = undefined\n    this.remoteFamily = undefined\n    this.remotePort = undefined\n    this.localAddress = undefined\n    this.localFamily = undefined\n    this.localPort = undefined\n\n    this._wrtc = (opts.wrtc && typeof opts.wrtc === 'object')\n      ? opts.wrtc\n      : getBrowserRTC()\n\n    if (!this._wrtc) {\n      if (typeof window === 'undefined') {\n        throw errCode(new Error('No WebRTC support: Specify `opts.wrtc` option in this environment'), 'ERR_WEBRTC_SUPPORT')\n      } else {\n        throw errCode(new Error('No WebRTC support: Not a supported browser'), 'ERR_WEBRTC_SUPPORT')\n      }\n    }\n\n    this._pcReady = false\n    this._channelReady = false\n    this._iceComplete = false // ice candidate trickle done (got null candidate)\n    this._iceCompleteTimer = null // send an offer/answer anyway after some timeout\n    this._channel = null\n    this._pendingCandidates = []\n\n    this._isNegotiating = false // is this peer waiting for negotiation to complete?\n    this._firstNegotiation = true\n    this._batchedNegotiation = false // batch synchronous negotiations\n    this._queuedNegotiation = false // is there a queued negotiation request?\n    this._sendersAwaitingStable = []\n    this._senderMap = new Map()\n    this._closingInterval = null\n\n    this._remoteTracks = []\n    this._remoteStreams = []\n\n    this._chunk = null\n    this._cb = null\n    this._interval = null\n\n    try {\n      this._pc = new (this._wrtc.RTCPeerConnection)(this.config)\n    } catch (err) {\n      this.destroy(errCode(err, 'ERR_PC_CONSTRUCTOR'))\n      return\n    }\n\n    // We prefer feature detection whenever possible, but sometimes that's not\n    // possible for certain implementations.\n    this._isReactNativeWebrtc = typeof this._pc._peerConnectionId === 'number'\n\n    this._pc.oniceconnectionstatechange = () => {\n      this._onIceStateChange()\n    }\n    this._pc.onicegatheringstatechange = () => {\n      this._onIceStateChange()\n    }\n    this._pc.onconnectionstatechange = () => {\n      this._onConnectionStateChange()\n    }\n    this._pc.onsignalingstatechange = () => {\n      this._onSignalingStateChange()\n    }\n    this._pc.onicecandidate = event => {\n      this._onIceCandidate(event)\n    }\n\n    // HACK: Fix for odd Firefox behavior, see: https://github.com/feross/simple-peer/pull/783\n    if (typeof this._pc.peerIdentity === 'object') {\n      this._pc.peerIdentity.catch(err => {\n        this.destroy(errCode(err, 'ERR_PC_PEER_IDENTITY'))\n      })\n    }\n\n    // Other spec events, unused by this implementation:\n    // - onconnectionstatechange\n    // - onicecandidateerror\n    // - onfingerprintfailure\n    // - onnegotiationneeded\n\n    if (this.initiator || this.channelNegotiated) {\n      this._setupData({\n        channel: this._pc.createDataChannel(this.channelName, this.channelConfig)\n      })\n    } else {\n      this._pc.ondatachannel = event => {\n        this._setupData(event)\n      }\n    }\n\n    if (this.streams) {\n      this.streams.forEach(stream => {\n        this.addStream(stream)\n      })\n    }\n    this._pc.ontrack = event => {\n      this._onTrack(event)\n    }\n\n    this._debug('initial negotiation')\n    this._needsNegotiation()\n\n    this._onFinishBound = () => {\n      this._onFinish()\n    }\n    this.once('finish', this._onFinishBound)\n  }\n\n  get bufferSize () {\n    return (this._channel && this._channel.bufferedAmount) || 0\n  }\n\n  // HACK: it's possible channel.readyState is \"closing\" before peer.destroy() fires\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n  get connected () {\n    return (this._connected && this._channel.readyState === 'open')\n  }\n\n  address () {\n    return { port: this.localPort, family: this.localFamily, address: this.localAddress }\n  }\n\n  signal (data) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot signal after peer is destroyed'), 'ERR_DESTROYED')\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data)\n      } catch (err) {\n        data = {}\n      }\n    }\n    this._debug('signal()')\n\n    if (data.renegotiate && this.initiator) {\n      this._debug('got request to renegotiate')\n      this._needsNegotiation()\n    }\n    if (data.transceiverRequest && this.initiator) {\n      this._debug('got request for transceiver')\n      this.addTransceiver(data.transceiverRequest.kind, data.transceiverRequest.init)\n    }\n    if (data.candidate) {\n      if (this._pc.remoteDescription && this._pc.remoteDescription.type) {\n        this._addIceCandidate(data.candidate)\n      } else {\n        this._pendingCandidates.push(data.candidate)\n      }\n    }\n    if (data.sdp) {\n      this._pc.setRemoteDescription(new (this._wrtc.RTCSessionDescription)(data))\n        .then(() => {\n          if (this.destroyed) return\n\n          this._pendingCandidates.forEach(candidate => {\n            this._addIceCandidate(candidate)\n          })\n          this._pendingCandidates = []\n\n          if (this._pc.remoteDescription.type === 'offer') this._createAnswer()\n        })\n        .catch(err => {\n          this.destroy(errCode(err, 'ERR_SET_REMOTE_DESCRIPTION'))\n        })\n    }\n    if (!data.sdp && !data.candidate && !data.renegotiate && !data.transceiverRequest) {\n      this.destroy(errCode(new Error('signal() called with invalid signal data'), 'ERR_SIGNALING'))\n    }\n  }\n\n  _addIceCandidate (candidate) {\n    const iceCandidateObj = new this._wrtc.RTCIceCandidate(candidate)\n    this._pc.addIceCandidate(iceCandidateObj)\n      .catch(err => {\n        if (!iceCandidateObj.address || iceCandidateObj.address.endsWith('.local')) {\n          warn('Ignoring unsupported ICE candidate.')\n        } else {\n          this.destroy(errCode(err, 'ERR_ADD_ICE_CANDIDATE'))\n        }\n      })\n  }\n\n  /**\n   * Send text/binary data to the remote peer.\n   * @param {ArrayBufferView|ArrayBuffer|Buffer|string|Blob} chunk\n   */\n  send (chunk) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot send after peer is destroyed'), 'ERR_DESTROYED')\n    this._channel.send(chunk)\n  }\n\n  /**\n   * Add a Transceiver to the connection.\n   * @param {String} kind\n   * @param {Object} init\n   */\n  addTransceiver (kind, init) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addTransceiver after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addTransceiver()')\n\n    if (this.initiator) {\n      try {\n        this._pc.addTransceiver(kind, init)\n        this._needsNegotiation()\n      } catch (err) {\n        this.destroy(errCode(err, 'ERR_ADD_TRANSCEIVER'))\n      }\n    } else {\n      this.emit('signal', { // request initiator to renegotiate\n        type: 'transceiverRequest',\n        transceiverRequest: { kind, init }\n      })\n    }\n  }\n\n  /**\n   * Add a MediaStream to the connection.\n   * @param {MediaStream} stream\n   */\n  addStream (stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addStream after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addStream()')\n\n    stream.getTracks().forEach(track => {\n      this.addTrack(track, stream)\n    })\n  }\n\n  /**\n   * Add a MediaStreamTrack to the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  addTrack (track, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addTrack()')\n\n    const submap = this._senderMap.get(track) || new Map() // nested Maps map [track, stream] to sender\n    let sender = submap.get(stream)\n    if (!sender) {\n      sender = this._pc.addTrack(track, stream)\n      submap.set(stream, sender)\n      this._senderMap.set(track, submap)\n      this._needsNegotiation()\n    } else if (sender.removed) {\n      throw errCode(new Error('Track has been removed. You should enable/disable tracks that you want to re-add.'), 'ERR_SENDER_REMOVED')\n    } else {\n      throw errCode(new Error('Track has already been added to that stream.'), 'ERR_SENDER_ALREADY_ADDED')\n    }\n  }\n\n  /**\n   * Replace a MediaStreamTrack by another in the connection.\n   * @param {MediaStreamTrack} oldTrack\n   * @param {MediaStreamTrack} newTrack\n   * @param {MediaStream} stream\n   */\n  replaceTrack (oldTrack, newTrack, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot replaceTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('replaceTrack()')\n\n    const submap = this._senderMap.get(oldTrack)\n    const sender = submap ? submap.get(stream) : null\n    if (!sender) {\n      throw errCode(new Error('Cannot replace track that was never added.'), 'ERR_TRACK_NOT_ADDED')\n    }\n    if (newTrack) this._senderMap.set(newTrack, submap)\n\n    if (sender.replaceTrack != null) {\n      sender.replaceTrack(newTrack)\n    } else {\n      this.destroy(errCode(new Error('replaceTrack is not supported in this browser'), 'ERR_UNSUPPORTED_REPLACETRACK'))\n    }\n  }\n\n  /**\n   * Remove a MediaStreamTrack from the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  removeTrack (track, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot removeTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('removeSender()')\n\n    const submap = this._senderMap.get(track)\n    const sender = submap ? submap.get(stream) : null\n    if (!sender) {\n      throw errCode(new Error('Cannot remove track that was never added.'), 'ERR_TRACK_NOT_ADDED')\n    }\n    try {\n      sender.removed = true\n      this._pc.removeTrack(sender)\n    } catch (err) {\n      if (err.name === 'NS_ERROR_UNEXPECTED') {\n        this._sendersAwaitingStable.push(sender) // HACK: Firefox must wait until (signalingState === stable) https://bugzilla.mozilla.org/show_bug.cgi?id=1133874\n      } else {\n        this.destroy(errCode(err, 'ERR_REMOVE_TRACK'))\n      }\n    }\n    this._needsNegotiation()\n  }\n\n  /**\n   * Remove a MediaStream from the connection.\n   * @param {MediaStream} stream\n   */\n  removeStream (stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot removeStream after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('removeSenders()')\n\n    stream.getTracks().forEach(track => {\n      this.removeTrack(track, stream)\n    })\n  }\n\n  _needsNegotiation () {\n    this._debug('_needsNegotiation')\n    if (this._batchedNegotiation) return // batch synchronous renegotiations\n    this._batchedNegotiation = true\n    queueMicrotask(() => {\n      this._batchedNegotiation = false\n      if (this.initiator || !this._firstNegotiation) {\n        this._debug('starting batched negotiation')\n        this.negotiate()\n      } else {\n        this._debug('non-initiator initial negotiation request discarded')\n      }\n      this._firstNegotiation = false\n    })\n  }\n\n  negotiate () {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot negotiate after peer is destroyed'), 'ERR_DESTROYED')\n\n    if (this.initiator) {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true\n        this._debug('already negotiating, queueing')\n      } else {\n        this._debug('start negotiation')\n        setTimeout(() => { // HACK: Chrome crashes if we immediately call createOffer\n          this._createOffer()\n        }, 0)\n      }\n    } else {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true\n        this._debug('already negotiating, queueing')\n      } else {\n        this._debug('requesting negotiation from initiator')\n        this.emit('signal', { // request initiator to renegotiate\n          type: 'renegotiate',\n          renegotiate: true\n        })\n      }\n    }\n    this._isNegotiating = true\n  }\n\n  // TODO: Delete this method once readable-stream is updated to contain a default\n  // implementation of destroy() that automatically calls _destroy()\n  // See: https://github.com/nodejs/readable-stream/issues/283\n  destroy (err) {\n    this._destroy(err, () => {})\n  }\n\n  _destroy (err, cb) {\n    if (this.destroyed || this.destroying) return\n    this.destroying = true\n\n    this._debug('destroying (error: %s)', err && (err.message || err))\n\n    queueMicrotask(() => { // allow events concurrent with the call to _destroy() to fire (see #692)\n      this.destroyed = true\n      this.destroying = false\n\n      this._debug('destroy (error: %s)', err && (err.message || err))\n\n      this.readable = this.writable = false\n\n      if (!this._readableState.ended) this.push(null)\n      if (!this._writableState.finished) this.end()\n\n      this._connected = false\n      this._pcReady = false\n      this._channelReady = false\n      this._remoteTracks = null\n      this._remoteStreams = null\n      this._senderMap = null\n\n      clearInterval(this._closingInterval)\n      this._closingInterval = null\n\n      clearInterval(this._interval)\n      this._interval = null\n      this._chunk = null\n      this._cb = null\n\n      if (this._onFinishBound) this.removeListener('finish', this._onFinishBound)\n      this._onFinishBound = null\n\n      if (this._channel) {\n        try {\n          this._channel.close()\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._channel.onmessage = null\n        this._channel.onopen = null\n        this._channel.onclose = null\n        this._channel.onerror = null\n      }\n      if (this._pc) {\n        try {\n          this._pc.close()\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._pc.oniceconnectionstatechange = null\n        this._pc.onicegatheringstatechange = null\n        this._pc.onsignalingstatechange = null\n        this._pc.onicecandidate = null\n        this._pc.ontrack = null\n        this._pc.ondatachannel = null\n      }\n      this._pc = null\n      this._channel = null\n\n      if (err) this.emit('error', err)\n      this.emit('close')\n      cb()\n    })\n  }\n\n  _setupData (event) {\n    if (!event.channel) {\n      // In some situations `pc.createDataChannel()` returns `undefined` (in wrtc),\n      // which is invalid behavior. Handle it gracefully.\n      // See: https://github.com/feross/simple-peer/issues/163\n      return this.destroy(errCode(new Error('Data channel event is missing `channel` property'), 'ERR_DATA_CHANNEL'))\n    }\n\n    this._channel = event.channel\n    this._channel.binaryType = 'arraybuffer'\n\n    if (typeof this._channel.bufferedAmountLowThreshold === 'number') {\n      this._channel.bufferedAmountLowThreshold = MAX_BUFFERED_AMOUNT\n    }\n\n    this.channelName = this._channel.label\n\n    this._channel.onmessage = event => {\n      this._onChannelMessage(event)\n    }\n    this._channel.onbufferedamountlow = () => {\n      this._onChannelBufferedAmountLow()\n    }\n    this._channel.onopen = () => {\n      this._onChannelOpen()\n    }\n    this._channel.onclose = () => {\n      this._onChannelClose()\n    }\n    this._channel.onerror = event => {\n      const err = event.error instanceof Error\n        ? event.error\n        : new Error(`Datachannel error: ${event.message} ${event.filename}:${event.lineno}:${event.colno}`)\n      this.destroy(errCode(err, 'ERR_DATA_CHANNEL'))\n    }\n\n    // HACK: Chrome will sometimes get stuck in readyState \"closing\", let's check for this condition\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n    let isClosing = false\n    this._closingInterval = setInterval(() => { // No \"onclosing\" event\n      if (this._channel && this._channel.readyState === 'closing') {\n        if (isClosing) this._onChannelClose() // closing timed out: equivalent to onclose firing\n        isClosing = true\n      } else {\n        isClosing = false\n      }\n    }, CHANNEL_CLOSING_TIMEOUT)\n  }\n\n  _read () {}\n\n  _write (chunk, encoding, cb) {\n    if (this.destroyed) return cb(errCode(new Error('cannot write after peer is destroyed'), 'ERR_DATA_CHANNEL'))\n\n    if (this._connected) {\n      try {\n        this.send(chunk)\n      } catch (err) {\n        return this.destroy(errCode(err, 'ERR_DATA_CHANNEL'))\n      }\n      if (this._channel.bufferedAmount > MAX_BUFFERED_AMOUNT) {\n        this._debug('start backpressure: bufferedAmount %d', this._channel.bufferedAmount)\n        this._cb = cb\n      } else {\n        cb(null)\n      }\n    } else {\n      this._debug('write before connect')\n      this._chunk = chunk\n      this._cb = cb\n    }\n  }\n\n  // When stream finishes writing, close socket. Half open connections are not\n  // supported.\n  _onFinish () {\n    if (this.destroyed) return\n\n    // Wait a bit before destroying so the socket flushes.\n    // TODO: is there a more reliable way to accomplish this?\n    const destroySoon = () => {\n      setTimeout(() => this.destroy(), 1000)\n    }\n\n    if (this._connected) {\n      destroySoon()\n    } else {\n      this.once('connect', destroySoon)\n    }\n  }\n\n  _startIceCompleteTimeout () {\n    if (this.destroyed) return\n    if (this._iceCompleteTimer) return\n    this._debug('started iceComplete timeout')\n    this._iceCompleteTimer = setTimeout(() => {\n      if (!this._iceComplete) {\n        this._iceComplete = true\n        this._debug('iceComplete timeout completed')\n        this.emit('iceTimeout')\n        this.emit('_iceComplete')\n      }\n    }, this.iceCompleteTimeout)\n  }\n\n  _createOffer () {\n    if (this.destroyed) return\n\n    this._pc.createOffer(this.offerOptions)\n      .then(offer => {\n        if (this.destroyed) return\n        if (!this.trickle && !this.allowHalfTrickle) offer.sdp = filterTrickle(offer.sdp)\n        offer.sdp = this.sdpTransform(offer.sdp)\n\n        const sendOffer = () => {\n          if (this.destroyed) return\n          const signal = this._pc.localDescription || offer\n          this._debug('signal')\n          this.emit('signal', {\n            type: signal.type,\n            sdp: signal.sdp\n          })\n        }\n\n        const onSuccess = () => {\n          this._debug('createOffer success')\n          if (this.destroyed) return\n          if (this.trickle || this._iceComplete) sendOffer()\n          else this.once('_iceComplete', sendOffer) // wait for candidates\n        }\n\n        const onError = err => {\n          this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'))\n        }\n\n        this._pc.setLocalDescription(offer)\n          .then(onSuccess)\n          .catch(onError)\n      })\n      .catch(err => {\n        this.destroy(errCode(err, 'ERR_CREATE_OFFER'))\n      })\n  }\n\n  _requestMissingTransceivers () {\n    if (this._pc.getTransceivers) {\n      this._pc.getTransceivers().forEach(transceiver => {\n        if (!transceiver.mid && transceiver.sender.track && !transceiver.requested) {\n          transceiver.requested = true // HACK: Safari returns negotiated transceivers with a null mid\n          this.addTransceiver(transceiver.sender.track.kind)\n        }\n      })\n    }\n  }\n\n  _createAnswer () {\n    if (this.destroyed) return\n\n    this._pc.createAnswer(this.answerOptions)\n      .then(answer => {\n        if (this.destroyed) return\n        if (!this.trickle && !this.allowHalfTrickle) answer.sdp = filterTrickle(answer.sdp)\n        answer.sdp = this.sdpTransform(answer.sdp)\n\n        const sendAnswer = () => {\n          if (this.destroyed) return\n          const signal = this._pc.localDescription || answer\n          this._debug('signal')\n          this.emit('signal', {\n            type: signal.type,\n            sdp: signal.sdp\n          })\n          if (!this.initiator) this._requestMissingTransceivers()\n        }\n\n        const onSuccess = () => {\n          if (this.destroyed) return\n          if (this.trickle || this._iceComplete) sendAnswer()\n          else this.once('_iceComplete', sendAnswer)\n        }\n\n        const onError = err => {\n          this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'))\n        }\n\n        this._pc.setLocalDescription(answer)\n          .then(onSuccess)\n          .catch(onError)\n      })\n      .catch(err => {\n        this.destroy(errCode(err, 'ERR_CREATE_ANSWER'))\n      })\n  }\n\n  _onConnectionStateChange () {\n    if (this.destroyed) return\n    if (this._pc.connectionState === 'failed') {\n      this.destroy(errCode(new Error('Connection failed.'), 'ERR_CONNECTION_FAILURE'))\n    }\n  }\n\n  _onIceStateChange () {\n    if (this.destroyed) return\n    const iceConnectionState = this._pc.iceConnectionState\n    const iceGatheringState = this._pc.iceGatheringState\n\n    this._debug(\n      'iceStateChange (connection: %s) (gathering: %s)',\n      iceConnectionState,\n      iceGatheringState\n    )\n    this.emit('iceStateChange', iceConnectionState, iceGatheringState)\n\n    if (iceConnectionState === 'connected' || iceConnectionState === 'completed') {\n      this._pcReady = true\n      this._maybeReady()\n    }\n    if (iceConnectionState === 'failed') {\n      this.destroy(errCode(new Error('Ice connection failed.'), 'ERR_ICE_CONNECTION_FAILURE'))\n    }\n    if (iceConnectionState === 'closed') {\n      this.destroy(errCode(new Error('Ice connection closed.'), 'ERR_ICE_CONNECTION_CLOSED'))\n    }\n  }\n\n  getStats (cb) {\n    // statreports can come with a value array instead of properties\n    const flattenValues = report => {\n      if (Object.prototype.toString.call(report.values) === '[object Array]') {\n        report.values.forEach(value => {\n          Object.assign(report, value)\n        })\n      }\n      return report\n    }\n\n    // Promise-based getStats() (standard)\n    if (this._pc.getStats.length === 0 || this._isReactNativeWebrtc) {\n      this._pc.getStats()\n        .then(res => {\n          const reports = []\n          res.forEach(report => {\n            reports.push(flattenValues(report))\n          })\n          cb(null, reports)\n        }, err => cb(err))\n\n    // Single-parameter callback-based getStats() (non-standard)\n    } else if (this._pc.getStats.length > 0) {\n      this._pc.getStats(res => {\n        // If we destroy connection in `connect` callback this code might happen to run when actual connection is already closed\n        if (this.destroyed) return\n\n        const reports = []\n        res.result().forEach(result => {\n          const report = {}\n          result.names().forEach(name => {\n            report[name] = result.stat(name)\n          })\n          report.id = result.id\n          report.type = result.type\n          report.timestamp = result.timestamp\n          reports.push(flattenValues(report))\n        })\n        cb(null, reports)\n      }, err => cb(err))\n\n    // Unknown browser, skip getStats() since it's anyone's guess which style of\n    // getStats() they implement.\n    } else {\n      cb(null, [])\n    }\n  }\n\n  _maybeReady () {\n    this._debug('maybeReady pc %s channel %s', this._pcReady, this._channelReady)\n    if (this._connected || this._connecting || !this._pcReady || !this._channelReady) return\n\n    this._connecting = true\n\n    // HACK: We can't rely on order here, for details see https://github.com/js-platform/node-webrtc/issues/339\n    const findCandidatePair = () => {\n      if (this.destroyed) return\n\n      this.getStats((err, items) => {\n        if (this.destroyed) return\n\n        // Treat getStats error as non-fatal. It's not essential.\n        if (err) items = []\n\n        const remoteCandidates = {}\n        const localCandidates = {}\n        const candidatePairs = {}\n        let foundSelectedCandidatePair = false\n\n        items.forEach(item => {\n          // TODO: Once all browsers support the hyphenated stats report types, remove\n          // the non-hypenated ones\n          if (item.type === 'remotecandidate' || item.type === 'remote-candidate') {\n            remoteCandidates[item.id] = item\n          }\n          if (item.type === 'localcandidate' || item.type === 'local-candidate') {\n            localCandidates[item.id] = item\n          }\n          if (item.type === 'candidatepair' || item.type === 'candidate-pair') {\n            candidatePairs[item.id] = item\n          }\n        })\n\n        const setSelectedCandidatePair = selectedCandidatePair => {\n          foundSelectedCandidatePair = true\n\n          let local = localCandidates[selectedCandidatePair.localCandidateId]\n\n          if (local && (local.ip || local.address)) {\n            // Spec\n            this.localAddress = local.ip || local.address\n            this.localPort = Number(local.port)\n          } else if (local && local.ipAddress) {\n            // Firefox\n            this.localAddress = local.ipAddress\n            this.localPort = Number(local.portNumber)\n          } else if (typeof selectedCandidatePair.googLocalAddress === 'string') {\n            // TODO: remove this once Chrome 58 is released\n            local = selectedCandidatePair.googLocalAddress.split(':')\n            this.localAddress = local[0]\n            this.localPort = Number(local[1])\n          }\n          if (this.localAddress) {\n            this.localFamily = this.localAddress.includes(':') ? 'IPv6' : 'IPv4'\n          }\n\n          let remote = remoteCandidates[selectedCandidatePair.remoteCandidateId]\n\n          if (remote && (remote.ip || remote.address)) {\n            // Spec\n            this.remoteAddress = remote.ip || remote.address\n            this.remotePort = Number(remote.port)\n          } else if (remote && remote.ipAddress) {\n            // Firefox\n            this.remoteAddress = remote.ipAddress\n            this.remotePort = Number(remote.portNumber)\n          } else if (typeof selectedCandidatePair.googRemoteAddress === 'string') {\n            // TODO: remove this once Chrome 58 is released\n            remote = selectedCandidatePair.googRemoteAddress.split(':')\n            this.remoteAddress = remote[0]\n            this.remotePort = Number(remote[1])\n          }\n          if (this.remoteAddress) {\n            this.remoteFamily = this.remoteAddress.includes(':') ? 'IPv6' : 'IPv4'\n          }\n\n          this._debug(\n            'connect local: %s:%s remote: %s:%s',\n            this.localAddress,\n            this.localPort,\n            this.remoteAddress,\n            this.remotePort\n          )\n        }\n\n        items.forEach(item => {\n          // Spec-compliant\n          if (item.type === 'transport' && item.selectedCandidatePairId) {\n            setSelectedCandidatePair(candidatePairs[item.selectedCandidatePairId])\n          }\n\n          // Old implementations\n          if (\n            (item.type === 'googCandidatePair' && item.googActiveConnection === 'true') ||\n            ((item.type === 'candidatepair' || item.type === 'candidate-pair') && item.selected)\n          ) {\n            setSelectedCandidatePair(item)\n          }\n        })\n\n        // Ignore candidate pair selection in browsers like Safari 11 that do not have any local or remote candidates\n        // But wait until at least 1 candidate pair is available\n        if (!foundSelectedCandidatePair && (!Object.keys(candidatePairs).length || Object.keys(localCandidates).length)) {\n          setTimeout(findCandidatePair, 100)\n          return\n        } else {\n          this._connecting = false\n          this._connected = true\n        }\n\n        if (this._chunk) {\n          try {\n            this.send(this._chunk)\n          } catch (err) {\n            return this.destroy(errCode(err, 'ERR_DATA_CHANNEL'))\n          }\n          this._chunk = null\n          this._debug('sent chunk from \"write before connect\"')\n\n          const cb = this._cb\n          this._cb = null\n          cb(null)\n        }\n\n        // If `bufferedAmountLowThreshold` and 'onbufferedamountlow' are unsupported,\n        // fallback to using setInterval to implement backpressure.\n        if (typeof this._channel.bufferedAmountLowThreshold !== 'number') {\n          this._interval = setInterval(() => this._onInterval(), 150)\n          if (this._interval.unref) this._interval.unref()\n        }\n\n        this._debug('connect')\n        this.emit('connect')\n      })\n    }\n    findCandidatePair()\n  }\n\n  _onInterval () {\n    if (!this._cb || !this._channel || this._channel.bufferedAmount > MAX_BUFFERED_AMOUNT) {\n      return\n    }\n    this._onChannelBufferedAmountLow()\n  }\n\n  _onSignalingStateChange () {\n    if (this.destroyed) return\n\n    if (this._pc.signalingState === 'stable') {\n      this._isNegotiating = false\n\n      // HACK: Firefox doesn't yet support removing tracks when signalingState !== 'stable'\n      this._debug('flushing sender queue', this._sendersAwaitingStable)\n      this._sendersAwaitingStable.forEach(sender => {\n        this._pc.removeTrack(sender)\n        this._queuedNegotiation = true\n      })\n      this._sendersAwaitingStable = []\n\n      if (this._queuedNegotiation) {\n        this._debug('flushing negotiation queue')\n        this._queuedNegotiation = false\n        this._needsNegotiation() // negotiate again\n      } else {\n        this._debug('negotiated')\n        this.emit('negotiated')\n      }\n    }\n\n    this._debug('signalingStateChange %s', this._pc.signalingState)\n    this.emit('signalingStateChange', this._pc.signalingState)\n  }\n\n  _onIceCandidate (event) {\n    if (this.destroyed) return\n    if (event.candidate && this.trickle) {\n      this.emit('signal', {\n        type: 'candidate',\n        candidate: {\n          candidate: event.candidate.candidate,\n          sdpMLineIndex: event.candidate.sdpMLineIndex,\n          sdpMid: event.candidate.sdpMid\n        }\n      })\n    } else if (!event.candidate && !this._iceComplete) {\n      this._iceComplete = true\n      this.emit('_iceComplete')\n    }\n    // as soon as we've received one valid candidate start timeout\n    if (event.candidate) {\n      this._startIceCompleteTimeout()\n    }\n  }\n\n  _onChannelMessage (event) {\n    if (this.destroyed) return\n    let data = event.data\n    if (data instanceof ArrayBuffer) data = Buffer.from(data)\n    this.push(data)\n  }\n\n  _onChannelBufferedAmountLow () {\n    if (this.destroyed || !this._cb) return\n    this._debug('ending backpressure: bufferedAmount %d', this._channel.bufferedAmount)\n    const cb = this._cb\n    this._cb = null\n    cb(null)\n  }\n\n  _onChannelOpen () {\n    if (this._connected || this.destroyed) return\n    this._debug('on channel open')\n    this._channelReady = true\n    this._maybeReady()\n  }\n\n  _onChannelClose () {\n    if (this.destroyed) return\n    this._debug('on channel close')\n    this.destroy()\n  }\n\n  _onTrack (event) {\n    if (this.destroyed) return\n\n    event.streams.forEach(eventStream => {\n      this._debug('on track')\n      this.emit('track', event.track, eventStream)\n\n      this._remoteTracks.push({\n        track: event.track,\n        stream: eventStream\n      })\n\n      if (this._remoteStreams.some(remoteStream => {\n        return remoteStream.id === eventStream.id\n      })) return // Only fire one 'stream' event, even though there may be multiple tracks per stream\n\n      this._remoteStreams.push(eventStream)\n      queueMicrotask(() => {\n        this._debug('on stream')\n        this.emit('stream', eventStream) // ensure all tracks have been added\n      })\n    })\n  }\n\n  _debug () {\n    const args = [].slice.call(arguments)\n    args[0] = '[' + this._id + '] ' + args[0]\n    debug.apply(null, args)\n  }\n}\n\nPeer.WEBRTC_SUPPORT = !!getBrowserRTC()\n\n/**\n * Expose peer and data channel config for overriding all Peer\n * instances. Otherwise, just set opts.config or opts.channelConfig\n * when constructing a Peer.\n */\nPeer.config = {\n  iceServers: [\n    {\n      urls: [\n        'stun:stun.l.google.com:19302',\n        'stun:global.stun.twilio.com:3478'\n      ]\n    }\n  ],\n  sdpSemantics: 'unified-plan'\n}\n\nPeer.channelConfig = {}\n\nmodule.exports = Peer\n"], "mappings": "AAAA;AACA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;AAC7C,MAAMC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAChD,MAAME,WAAW,GAAGF,OAAO,CAAC,aAAa,CAAC;AAC1C,MAAMG,MAAM,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AACzC,MAAMI,cAAc,GAAGJ,OAAO,CAAC,iBAAiB,CAAC,EAAC;AAClD,MAAMK,OAAO,GAAGL,OAAO,CAAC,UAAU,CAAC;AACnC,MAAM;EAAEM;AAAO,CAAC,GAAGN,OAAO,CAAC,QAAQ,CAAC;AAEpC,MAAMO,mBAAmB,GAAG,EAAE,GAAG,IAAI;AACrC,MAAMC,mBAAmB,GAAG,CAAC,GAAG,IAAI;AACpC,MAAMC,uBAAuB,GAAG,CAAC,GAAG,IAAI;;AAExC;AACA,SAASC,aAAaA,CAAEC,GAAG,EAAE;EAC3B,OAAOA,GAAG,CAACC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC;AACtD;AAEA,SAASC,IAAIA,CAAEC,OAAO,EAAE;EACtBC,OAAO,CAACF,IAAI,CAACC,OAAO,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,IAAI,SAASb,MAAM,CAACc,MAAM,CAAC;EAC/BC,WAAWA,CAAEC,IAAI,EAAE;IACjBA,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC;MACnBC,aAAa,EAAE;IACjB,CAAC,EAAEH,IAAI,CAAC;IAER,KAAK,CAACA,IAAI,CAAC;IAEX,IAAI,CAACI,GAAG,GAAGrB,WAAW,CAAC,CAAC,CAAC,CAACsB,QAAQ,CAAC,KAAK,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,IAAI,CAACC,MAAM,CAAC,aAAa,EAAEP,IAAI,CAAC;IAEhC,IAAI,CAACQ,WAAW,GAAGR,IAAI,CAACS,SAAS,GAC7BT,IAAI,CAACQ,WAAW,IAAIzB,WAAW,CAAC,EAAE,CAAC,CAACsB,QAAQ,CAAC,KAAK,CAAC,GACnD,IAAI;IAER,IAAI,CAACI,SAAS,GAAGT,IAAI,CAACS,SAAS,IAAI,KAAK;IACxC,IAAI,CAACC,aAAa,GAAGV,IAAI,CAACU,aAAa,IAAIb,IAAI,CAACa,aAAa;IAC7D,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU;IACtD,IAAI,CAACC,MAAM,GAAGZ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,IAAI,CAACgB,MAAM,EAAEb,IAAI,CAACa,MAAM,CAAC;IACzD,IAAI,CAACC,YAAY,GAAGd,IAAI,CAACc,YAAY,IAAI,CAAC,CAAC;IAC3C,IAAI,CAACC,aAAa,GAAGf,IAAI,CAACe,aAAa,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACC,YAAY,GAAGhB,IAAI,CAACgB,YAAY,KAAKxB,GAAG,IAAIA,GAAG,CAAC;IACrD,IAAI,CAACyB,OAAO,GAAGjB,IAAI,CAACiB,OAAO,KAAKjB,IAAI,CAAChB,MAAM,GAAG,CAACgB,IAAI,CAAChB,MAAM,CAAC,GAAG,EAAE,CAAC,EAAC;IAClE,IAAI,CAACkC,OAAO,GAAGlB,IAAI,CAACkB,OAAO,KAAKC,SAAS,GAAGnB,IAAI,CAACkB,OAAO,GAAG,IAAI;IAC/D,IAAI,CAACE,gBAAgB,GAAGpB,IAAI,CAACoB,gBAAgB,KAAKD,SAAS,GAAGnB,IAAI,CAACoB,gBAAgB,GAAG,KAAK;IAC3F,IAAI,CAACC,kBAAkB,GAAGrB,IAAI,CAACqB,kBAAkB,IAAIhC,mBAAmB;IAExE,IAAI,CAACiC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,KAAK;IAEvB,IAAI,CAACC,aAAa,GAAGN,SAAS;IAC9B,IAAI,CAACO,YAAY,GAAGP,SAAS;IAC7B,IAAI,CAACQ,UAAU,GAAGR,SAAS;IAC3B,IAAI,CAACS,YAAY,GAAGT,SAAS;IAC7B,IAAI,CAACU,WAAW,GAAGV,SAAS;IAC5B,IAAI,CAACW,SAAS,GAAGX,SAAS;IAE1B,IAAI,CAACY,KAAK,GAAI/B,IAAI,CAACgC,IAAI,IAAI,OAAOhC,IAAI,CAACgC,IAAI,KAAK,QAAQ,GACpDhC,IAAI,CAACgC,IAAI,GACTlD,aAAa,CAAC,CAAC;IAEnB,IAAI,CAAC,IAAI,CAACiD,KAAK,EAAE;MACf,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;QACjC,MAAM/C,OAAO,CAAC,IAAIgD,KAAK,CAAC,mEAAmE,CAAC,EAAE,oBAAoB,CAAC;MACrH,CAAC,MAAM;QACL,MAAMhD,OAAO,CAAC,IAAIgD,KAAK,CAAC,4CAA4C,CAAC,EAAE,oBAAoB,CAAC;MAC9F;IACF;IAEA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,KAAK,EAAC;IAC1B,IAAI,CAACC,iBAAiB,GAAG,IAAI,EAAC;IAC9B,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAACC,cAAc,GAAG,KAAK,EAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK,EAAC;IACjC,IAAI,CAACC,kBAAkB,GAAG,KAAK,EAAC;IAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IAExB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI;MACF,IAAI,CAACC,GAAG,GAAG,IAAK,IAAI,CAACvB,KAAK,CAACwB,iBAAiB,CAAE,IAAI,CAAC1C,MAAM,CAAC;IAC5D,CAAC,CAAC,OAAO2C,GAAG,EAAE;MACZ,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,oBAAoB,CAAC,CAAC;MAChD;IACF;;IAEA;IACA;IACA,IAAI,CAACE,oBAAoB,GAAG,OAAO,IAAI,CAACJ,GAAG,CAACK,iBAAiB,KAAK,QAAQ;IAE1E,IAAI,CAACL,GAAG,CAACM,0BAA0B,GAAG,MAAM;MAC1C,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC1B,CAAC;IACD,IAAI,CAACP,GAAG,CAACQ,yBAAyB,GAAG,MAAM;MACzC,IAAI,CAACD,iBAAiB,CAAC,CAAC;IAC1B,CAAC;IACD,IAAI,CAACP,GAAG,CAACS,uBAAuB,GAAG,MAAM;MACvC,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,CAACV,GAAG,CAACW,sBAAsB,GAAG,MAAM;MACtC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC,CAAC;IACD,IAAI,CAACZ,GAAG,CAACa,cAAc,GAAGC,KAAK,IAAI;MACjC,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;IAC7B,CAAC;;IAED;IACA,IAAI,OAAO,IAAI,CAACd,GAAG,CAACgB,YAAY,KAAK,QAAQ,EAAE;MAC7C,IAAI,CAAChB,GAAG,CAACgB,YAAY,CAACC,KAAK,CAACf,GAAG,IAAI;QACjC,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,sBAAsB,CAAC,CAAC;MACpD,CAAC,CAAC;IACJ;;IAEA;IACA;IACA;IACA;IACA;;IAEA,IAAI,IAAI,CAAC/C,SAAS,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC5C,IAAI,CAAC6D,UAAU,CAAC;QACdC,OAAO,EAAE,IAAI,CAACnB,GAAG,CAACoB,iBAAiB,CAAC,IAAI,CAAClE,WAAW,EAAE,IAAI,CAACE,aAAa;MAC1E,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC4C,GAAG,CAACqB,aAAa,GAAGP,KAAK,IAAI;QAChC,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;MACxB,CAAC;IACH;IAEA,IAAI,IAAI,CAACnD,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAC2D,OAAO,CAAC5F,MAAM,IAAI;QAC7B,IAAI,CAAC6F,SAAS,CAAC7F,MAAM,CAAC;MACxB,CAAC,CAAC;IACJ;IACA,IAAI,CAACsE,GAAG,CAACwB,OAAO,GAAGV,KAAK,IAAI;MAC1B,IAAI,CAACW,QAAQ,CAACX,KAAK,CAAC;IACtB,CAAC;IAED,IAAI,CAAC7D,MAAM,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACyE,iBAAiB,CAAC,CAAC;IAExB,IAAI,CAACC,cAAc,GAAG,MAAM;MAC1B,IAAI,CAACC,SAAS,CAAC,CAAC;IAClB,CAAC;IACD,IAAI,CAACC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAACF,cAAc,CAAC;EAC1C;EAEA,IAAIG,UAAUA,CAAA,EAAI;IAChB,OAAQ,IAAI,CAAC7C,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC8C,cAAc,IAAK,CAAC;EAC7D;;EAEA;EACA;EACA,IAAIC,SAASA,CAAA,EAAI;IACf,OAAQ,IAAI,CAAC9D,UAAU,IAAI,IAAI,CAACe,QAAQ,CAACgD,UAAU,KAAK,MAAM;EAChE;EAEAC,OAAOA,CAAA,EAAI;IACT,OAAO;MAAEC,IAAI,EAAE,IAAI,CAAC3D,SAAS;MAAE4D,MAAM,EAAE,IAAI,CAAC7D,WAAW;MAAE2D,OAAO,EAAE,IAAI,CAAC5D;IAAa,CAAC;EACvF;EAEA+D,MAAMA,CAAEC,IAAI,EAAE;IACZ,IAAI,IAAI,CAACrE,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,uCAAuC,CAAC,EAAE,eAAe,CAAC;IACtG,IAAI,OAAO0D,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI;QACFA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOpC,GAAG,EAAE;QACZoC,IAAI,GAAG,CAAC,CAAC;MACX;IACF;IACA,IAAI,CAACrF,MAAM,CAAC,UAAU,CAAC;IAEvB,IAAIqF,IAAI,CAACG,WAAW,IAAI,IAAI,CAACtF,SAAS,EAAE;MACtC,IAAI,CAACF,MAAM,CAAC,4BAA4B,CAAC;MACzC,IAAI,CAACyE,iBAAiB,CAAC,CAAC;IAC1B;IACA,IAAIY,IAAI,CAACI,kBAAkB,IAAI,IAAI,CAACvF,SAAS,EAAE;MAC7C,IAAI,CAACF,MAAM,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAAC0F,cAAc,CAACL,IAAI,CAACI,kBAAkB,CAACE,IAAI,EAAEN,IAAI,CAACI,kBAAkB,CAACG,IAAI,CAAC;IACjF;IACA,IAAIP,IAAI,CAACQ,SAAS,EAAE;MAClB,IAAI,IAAI,CAAC9C,GAAG,CAAC+C,iBAAiB,IAAI,IAAI,CAAC/C,GAAG,CAAC+C,iBAAiB,CAACC,IAAI,EAAE;QACjE,IAAI,CAACC,gBAAgB,CAACX,IAAI,CAACQ,SAAS,CAAC;MACvC,CAAC,MAAM;QACL,IAAI,CAAC5D,kBAAkB,CAACgE,IAAI,CAACZ,IAAI,CAACQ,SAAS,CAAC;MAC9C;IACF;IACA,IAAIR,IAAI,CAACpG,GAAG,EAAE;MACZ,IAAI,CAAC8D,GAAG,CAACmD,oBAAoB,CAAC,IAAK,IAAI,CAAC1E,KAAK,CAAC2E,qBAAqB,CAAEd,IAAI,CAAC,CAAC,CACxEe,IAAI,CAAC,MAAM;QACV,IAAI,IAAI,CAACrF,SAAS,EAAE;QAEpB,IAAI,CAACkB,kBAAkB,CAACoC,OAAO,CAACwB,SAAS,IAAI;UAC3C,IAAI,CAACG,gBAAgB,CAACH,SAAS,CAAC;QAClC,CAAC,CAAC;QACF,IAAI,CAAC5D,kBAAkB,GAAG,EAAE;QAE5B,IAAI,IAAI,CAACc,GAAG,CAAC+C,iBAAiB,CAACC,IAAI,KAAK,OAAO,EAAE,IAAI,CAACM,aAAa,CAAC,CAAC;MACvE,CAAC,CAAC,CACDrC,KAAK,CAACf,GAAG,IAAI;QACZ,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,4BAA4B,CAAC,CAAC;MAC1D,CAAC,CAAC;IACN;IACA,IAAI,CAACoC,IAAI,CAACpG,GAAG,IAAI,CAACoG,IAAI,CAACQ,SAAS,IAAI,CAACR,IAAI,CAACG,WAAW,IAAI,CAACH,IAAI,CAACI,kBAAkB,EAAE;MACjF,IAAI,CAACvC,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,0CAA0C,CAAC,EAAE,eAAe,CAAC,CAAC;IAC/F;EACF;EAEAqE,gBAAgBA,CAAEH,SAAS,EAAE;IAC3B,MAAMS,eAAe,GAAG,IAAI,IAAI,CAAC9E,KAAK,CAAC+E,eAAe,CAACV,SAAS,CAAC;IACjE,IAAI,CAAC9C,GAAG,CAACyD,eAAe,CAACF,eAAe,CAAC,CACtCtC,KAAK,CAACf,GAAG,IAAI;MACZ,IAAI,CAACqD,eAAe,CAACrB,OAAO,IAAIqB,eAAe,CAACrB,OAAO,CAACwB,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC1EtH,IAAI,CAAC,qCAAqC,CAAC;MAC7C,CAAC,MAAM;QACL,IAAI,CAAC+D,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,uBAAuB,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;EACN;;EAEA;AACF;AACA;AACA;EACEyD,IAAIA,CAAEC,KAAK,EAAE;IACX,IAAI,IAAI,CAAC3F,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,EAAE,eAAe,CAAC;IACpG,IAAI,CAACK,QAAQ,CAAC0E,IAAI,CAACC,KAAK,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EACEjB,cAAcA,CAAEC,IAAI,EAAEC,IAAI,EAAE;IAC1B,IAAI,IAAI,CAAC5E,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,+CAA+C,CAAC,EAAE,eAAe,CAAC;IAC9G,IAAI,CAAC3B,MAAM,CAAC,kBAAkB,CAAC;IAE/B,IAAI,IAAI,CAACE,SAAS,EAAE;MAClB,IAAI;QACF,IAAI,CAAC6C,GAAG,CAAC2C,cAAc,CAACC,IAAI,EAAEC,IAAI,CAAC;QACnC,IAAI,CAACnB,iBAAiB,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOxB,GAAG,EAAE;QACZ,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,qBAAqB,CAAC,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC2D,IAAI,CAAC,QAAQ,EAAE;QAAE;QACpBb,IAAI,EAAE,oBAAoB;QAC1BN,kBAAkB,EAAE;UAAEE,IAAI;UAAEC;QAAK;MACnC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;EACEtB,SAASA,CAAE7F,MAAM,EAAE;IACjB,IAAI,IAAI,CAACuC,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,0CAA0C,CAAC,EAAE,eAAe,CAAC;IACzG,IAAI,CAAC3B,MAAM,CAAC,aAAa,CAAC;IAE1BvB,MAAM,CAACoI,SAAS,CAAC,CAAC,CAACxC,OAAO,CAACyC,KAAK,IAAI;MAClC,IAAI,CAACC,QAAQ,CAACD,KAAK,EAAErI,MAAM,CAAC;IAC9B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEsI,QAAQA,CAAED,KAAK,EAAErI,MAAM,EAAE;IACvB,IAAI,IAAI,CAACuC,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,yCAAyC,CAAC,EAAE,eAAe,CAAC;IACxG,IAAI,CAAC3B,MAAM,CAAC,YAAY,CAAC;IAEzB,MAAMgH,MAAM,GAAG,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAACH,KAAK,CAAC,IAAI,IAAItE,GAAG,CAAC,CAAC,EAAC;IACvD,IAAI0E,MAAM,GAAGF,MAAM,CAACC,GAAG,CAACxI,MAAM,CAAC;IAC/B,IAAI,CAACyI,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI,CAACnE,GAAG,CAACgE,QAAQ,CAACD,KAAK,EAAErI,MAAM,CAAC;MACzCuI,MAAM,CAACG,GAAG,CAAC1I,MAAM,EAAEyI,MAAM,CAAC;MAC1B,IAAI,CAAC3E,UAAU,CAAC4E,GAAG,CAACL,KAAK,EAAEE,MAAM,CAAC;MAClC,IAAI,CAACvC,iBAAiB,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAIyC,MAAM,CAACE,OAAO,EAAE;MACzB,MAAMzI,OAAO,CAAC,IAAIgD,KAAK,CAAC,mFAAmF,CAAC,EAAE,oBAAoB,CAAC;IACrI,CAAC,MAAM;MACL,MAAMhD,OAAO,CAAC,IAAIgD,KAAK,CAAC,8CAA8C,CAAC,EAAE,0BAA0B,CAAC;IACtG;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE0F,YAAYA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE9I,MAAM,EAAE;IACxC,IAAI,IAAI,CAACuC,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,6CAA6C,CAAC,EAAE,eAAe,CAAC;IAC5G,IAAI,CAAC3B,MAAM,CAAC,gBAAgB,CAAC;IAE7B,MAAMgH,MAAM,GAAG,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAACK,QAAQ,CAAC;IAC5C,MAAMJ,MAAM,GAAGF,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACxI,MAAM,CAAC,GAAG,IAAI;IACjD,IAAI,CAACyI,MAAM,EAAE;MACX,MAAMvI,OAAO,CAAC,IAAIgD,KAAK,CAAC,4CAA4C,CAAC,EAAE,qBAAqB,CAAC;IAC/F;IACA,IAAI4F,QAAQ,EAAE,IAAI,CAAChF,UAAU,CAAC4E,GAAG,CAACI,QAAQ,EAAEP,MAAM,CAAC;IAEnD,IAAIE,MAAM,CAACG,YAAY,IAAI,IAAI,EAAE;MAC/BH,MAAM,CAACG,YAAY,CAACE,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAACrE,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,+CAA+C,CAAC,EAAE,8BAA8B,CAAC,CAAC;IACnH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE6F,WAAWA,CAAEV,KAAK,EAAErI,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACuC,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,4CAA4C,CAAC,EAAE,eAAe,CAAC;IAC3G,IAAI,CAAC3B,MAAM,CAAC,gBAAgB,CAAC;IAE7B,MAAMgH,MAAM,GAAG,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAACH,KAAK,CAAC;IACzC,MAAMI,MAAM,GAAGF,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACxI,MAAM,CAAC,GAAG,IAAI;IACjD,IAAI,CAACyI,MAAM,EAAE;MACX,MAAMvI,OAAO,CAAC,IAAIgD,KAAK,CAAC,2CAA2C,CAAC,EAAE,qBAAqB,CAAC;IAC9F;IACA,IAAI;MACFuF,MAAM,CAACE,OAAO,GAAG,IAAI;MACrB,IAAI,CAACrE,GAAG,CAACyE,WAAW,CAACN,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOjE,GAAG,EAAE;MACZ,IAAIA,GAAG,CAACwE,IAAI,KAAK,qBAAqB,EAAE;QACtC,IAAI,CAACnF,sBAAsB,CAAC2D,IAAI,CAACiB,MAAM,CAAC,EAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAChE,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,kBAAkB,CAAC,CAAC;MAChD;IACF;IACA,IAAI,CAACwB,iBAAiB,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;EACEiD,YAAYA,CAAEjJ,MAAM,EAAE;IACpB,IAAI,IAAI,CAACuC,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,6CAA6C,CAAC,EAAE,eAAe,CAAC;IAC5G,IAAI,CAAC3B,MAAM,CAAC,iBAAiB,CAAC;IAE9BvB,MAAM,CAACoI,SAAS,CAAC,CAAC,CAACxC,OAAO,CAACyC,KAAK,IAAI;MAClC,IAAI,CAACU,WAAW,CAACV,KAAK,EAAErI,MAAM,CAAC;IACjC,CAAC,CAAC;EACJ;EAEAgG,iBAAiBA,CAAA,EAAI;IACnB,IAAI,CAACzE,MAAM,CAAC,mBAAmB,CAAC;IAChC,IAAI,IAAI,CAACoC,mBAAmB,EAAE,OAAM,CAAC;IACrC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAC/B1D,cAAc,CAAC,MAAM;MACnB,IAAI,CAAC0D,mBAAmB,GAAG,KAAK;MAChC,IAAI,IAAI,CAAClC,SAAS,IAAI,CAAC,IAAI,CAACiC,iBAAiB,EAAE;QAC7C,IAAI,CAACnC,MAAM,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAAC2H,SAAS,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,IAAI,CAAC3H,MAAM,CAAC,qDAAqD,CAAC;MACpE;MACA,IAAI,CAACmC,iBAAiB,GAAG,KAAK;IAChC,CAAC,CAAC;EACJ;EAEAwF,SAASA,CAAA,EAAI;IACX,IAAI,IAAI,CAAC3G,UAAU,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE,MAAMpC,OAAO,CAAC,IAAIgD,KAAK,CAAC,0CAA0C,CAAC,EAAE,eAAe,CAAC;IAEzG,IAAI,IAAI,CAACzB,SAAS,EAAE;MAClB,IAAI,IAAI,CAACgC,cAAc,EAAE;QACvB,IAAI,CAACG,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACrC,MAAM,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACA,MAAM,CAAC,mBAAmB,CAAC;QAChC4H,UAAU,CAAC,MAAM;UAAE;UACjB,IAAI,CAACC,YAAY,CAAC,CAAC;QACrB,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,MAAM;MACL,IAAI,IAAI,CAAC3F,cAAc,EAAE;QACvB,IAAI,CAACG,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACrC,MAAM,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACA,MAAM,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAAC4G,IAAI,CAAC,QAAQ,EAAE;UAAE;UACpBb,IAAI,EAAE,aAAa;UACnBP,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;IACF;IACA,IAAI,CAACtD,cAAc,GAAG,IAAI;EAC5B;;EAEA;EACA;EACA;EACAgB,OAAOA,CAAED,GAAG,EAAE;IACZ,IAAI,CAAC6E,QAAQ,CAAC7E,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;EAC9B;EAEA6E,QAAQA,CAAE7E,GAAG,EAAE8E,EAAE,EAAE;IACjB,IAAI,IAAI,CAAChH,SAAS,IAAI,IAAI,CAACC,UAAU,EAAE;IACvC,IAAI,CAACA,UAAU,GAAG,IAAI;IAEtB,IAAI,CAAChB,MAAM,CAAC,wBAAwB,EAAEiD,GAAG,KAAKA,GAAG,CAAC7D,OAAO,IAAI6D,GAAG,CAAC,CAAC;IAElEvE,cAAc,CAAC,MAAM;MAAE;MACrB,IAAI,CAACqC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,UAAU,GAAG,KAAK;MAEvB,IAAI,CAAChB,MAAM,CAAC,qBAAqB,EAAEiD,GAAG,KAAKA,GAAG,CAAC7D,OAAO,IAAI6D,GAAG,CAAC,CAAC;MAE/D,IAAI,CAAC+E,QAAQ,GAAG,IAAI,CAACC,QAAQ,GAAG,KAAK;MAErC,IAAI,CAAC,IAAI,CAACC,cAAc,CAACC,KAAK,EAAE,IAAI,CAAClC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI,CAAC,IAAI,CAACmC,cAAc,CAACC,QAAQ,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;MAE7C,IAAI,CAACrH,UAAU,GAAG,KAAK;MACvB,IAAI,CAACW,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACa,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACJ,UAAU,GAAG,IAAI;MAEtBgG,aAAa,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;MAE5B8F,aAAa,CAAC,IAAI,CAACzF,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACF,MAAM,GAAG,IAAI;MAClB,IAAI,CAACC,GAAG,GAAG,IAAI;MAEf,IAAI,IAAI,CAAC6B,cAAc,EAAE,IAAI,CAAC8D,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC9D,cAAc,CAAC;MAC3E,IAAI,CAACA,cAAc,GAAG,IAAI;MAE1B,IAAI,IAAI,CAAC1C,QAAQ,EAAE;QACjB,IAAI;UACF,IAAI,CAACA,QAAQ,CAACyG,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC,OAAOxF,GAAG,EAAE,CAAC;;QAEf;QACA,IAAI,CAACjB,QAAQ,CAAC0G,SAAS,GAAG,IAAI;QAC9B,IAAI,CAAC1G,QAAQ,CAAC2G,MAAM,GAAG,IAAI;QAC3B,IAAI,CAAC3G,QAAQ,CAAC4G,OAAO,GAAG,IAAI;QAC5B,IAAI,CAAC5G,QAAQ,CAAC6G,OAAO,GAAG,IAAI;MAC9B;MACA,IAAI,IAAI,CAAC9F,GAAG,EAAE;QACZ,IAAI;UACF,IAAI,CAACA,GAAG,CAAC0F,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC,OAAOxF,GAAG,EAAE,CAAC;;QAEf;QACA,IAAI,CAACF,GAAG,CAACM,0BAA0B,GAAG,IAAI;QAC1C,IAAI,CAACN,GAAG,CAACQ,yBAAyB,GAAG,IAAI;QACzC,IAAI,CAACR,GAAG,CAACW,sBAAsB,GAAG,IAAI;QACtC,IAAI,CAACX,GAAG,CAACa,cAAc,GAAG,IAAI;QAC9B,IAAI,CAACb,GAAG,CAACwB,OAAO,GAAG,IAAI;QACvB,IAAI,CAACxB,GAAG,CAACqB,aAAa,GAAG,IAAI;MAC/B;MACA,IAAI,CAACrB,GAAG,GAAG,IAAI;MACf,IAAI,CAACf,QAAQ,GAAG,IAAI;MAEpB,IAAIiB,GAAG,EAAE,IAAI,CAAC2D,IAAI,CAAC,OAAO,EAAE3D,GAAG,CAAC;MAChC,IAAI,CAAC2D,IAAI,CAAC,OAAO,CAAC;MAClBmB,EAAE,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA9D,UAAUA,CAAEJ,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,CAACK,OAAO,EAAE;MAClB;MACA;MACA;MACA,OAAO,IAAI,CAAChB,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,kDAAkD,CAAC,EAAE,kBAAkB,CAAC,CAAC;IACjH;IAEA,IAAI,CAACK,QAAQ,GAAG6B,KAAK,CAACK,OAAO;IAC7B,IAAI,CAAClC,QAAQ,CAAC8G,UAAU,GAAG,aAAa;IAExC,IAAI,OAAO,IAAI,CAAC9G,QAAQ,CAAC+G,0BAA0B,KAAK,QAAQ,EAAE;MAChE,IAAI,CAAC/G,QAAQ,CAAC+G,0BAA0B,GAAGlK,mBAAmB;IAChE;IAEA,IAAI,CAACoB,WAAW,GAAG,IAAI,CAAC+B,QAAQ,CAACgH,KAAK;IAEtC,IAAI,CAAChH,QAAQ,CAAC0G,SAAS,GAAG7E,KAAK,IAAI;MACjC,IAAI,CAACoF,iBAAiB,CAACpF,KAAK,CAAC;IAC/B,CAAC;IACD,IAAI,CAAC7B,QAAQ,CAACkH,mBAAmB,GAAG,MAAM;MACxC,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAACnH,QAAQ,CAAC2G,MAAM,GAAG,MAAM;MAC3B,IAAI,CAACS,cAAc,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACpH,QAAQ,CAAC4G,OAAO,GAAG,MAAM;MAC5B,IAAI,CAACS,eAAe,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACrH,QAAQ,CAAC6G,OAAO,GAAGhF,KAAK,IAAI;MAC/B,MAAMZ,GAAG,GAAGY,KAAK,CAACyF,KAAK,YAAY3H,KAAK,GACpCkC,KAAK,CAACyF,KAAK,GACX,IAAI3H,KAAK,CAAC,sBAAsBkC,KAAK,CAACzE,OAAO,IAAIyE,KAAK,CAAC0F,QAAQ,IAAI1F,KAAK,CAAC2F,MAAM,IAAI3F,KAAK,CAAC4F,KAAK,EAAE,CAAC;MACrG,IAAI,CAACvG,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IAChD,CAAC;;IAED;IACA;IACA,IAAIyG,SAAS,GAAG,KAAK;IACrB,IAAI,CAACjH,gBAAgB,GAAGkH,WAAW,CAAC,MAAM;MAAE;MAC1C,IAAI,IAAI,CAAC3H,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACgD,UAAU,KAAK,SAAS,EAAE;QAC3D,IAAI0E,SAAS,EAAE,IAAI,CAACL,eAAe,CAAC,CAAC,EAAC;QACtCK,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM;QACLA,SAAS,GAAG,KAAK;MACnB;IACF,CAAC,EAAE3K,uBAAuB,CAAC;EAC7B;EAEA6K,KAAKA,CAAA,EAAI,CAAC;EAEVC,MAAMA,CAAElD,KAAK,EAAEmD,QAAQ,EAAE/B,EAAE,EAAE;IAC3B,IAAI,IAAI,CAAChH,SAAS,EAAE,OAAOgH,EAAE,CAACpJ,OAAO,CAAC,IAAIgD,KAAK,CAAC,sCAAsC,CAAC,EAAE,kBAAkB,CAAC,CAAC;IAE7G,IAAI,IAAI,CAACV,UAAU,EAAE;MACnB,IAAI;QACF,IAAI,CAACyF,IAAI,CAACC,KAAK,CAAC;MAClB,CAAC,CAAC,OAAO1D,GAAG,EAAE;QACZ,OAAO,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,kBAAkB,CAAC,CAAC;MACvD;MACA,IAAI,IAAI,CAACjB,QAAQ,CAAC8C,cAAc,GAAGjG,mBAAmB,EAAE;QACtD,IAAI,CAACmB,MAAM,CAAC,uCAAuC,EAAE,IAAI,CAACgC,QAAQ,CAAC8C,cAAc,CAAC;QAClF,IAAI,CAACjC,GAAG,GAAGkF,EAAE;MACf,CAAC,MAAM;QACLA,EAAE,CAAC,IAAI,CAAC;MACV;IACF,CAAC,MAAM;MACL,IAAI,CAAC/H,MAAM,CAAC,sBAAsB,CAAC;MACnC,IAAI,CAAC4C,MAAM,GAAG+D,KAAK;MACnB,IAAI,CAAC9D,GAAG,GAAGkF,EAAE;IACf;EACF;;EAEA;EACA;EACApD,SAASA,CAAA,EAAI;IACX,IAAI,IAAI,CAAC5D,SAAS,EAAE;;IAEpB;IACA;IACA,MAAMgJ,WAAW,GAAGA,CAAA,KAAM;MACxBnC,UAAU,CAAC,MAAM,IAAI,CAAC1E,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC;IAED,IAAI,IAAI,CAACjC,UAAU,EAAE;MACnB8I,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACL,IAAI,CAACnF,IAAI,CAAC,SAAS,EAAEmF,WAAW,CAAC;IACnC;EACF;EAEAC,wBAAwBA,CAAA,EAAI;IAC1B,IAAI,IAAI,CAACjJ,SAAS,EAAE;IACpB,IAAI,IAAI,CAACgB,iBAAiB,EAAE;IAC5B,IAAI,CAAC/B,MAAM,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAAC+B,iBAAiB,GAAG6F,UAAU,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAAC9F,YAAY,EAAE;QACtB,IAAI,CAACA,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC9B,MAAM,CAAC,+BAA+B,CAAC;QAC5C,IAAI,CAAC4G,IAAI,CAAC,YAAY,CAAC;QACvB,IAAI,CAACA,IAAI,CAAC,cAAc,CAAC;MAC3B;IACF,CAAC,EAAE,IAAI,CAAC9F,kBAAkB,CAAC;EAC7B;EAEA+G,YAAYA,CAAA,EAAI;IACd,IAAI,IAAI,CAAC9G,SAAS,EAAE;IAEpB,IAAI,CAACgC,GAAG,CAACkH,WAAW,CAAC,IAAI,CAAC1J,YAAY,CAAC,CACpC6F,IAAI,CAAC8D,KAAK,IAAI;MACb,IAAI,IAAI,CAACnJ,SAAS,EAAE;MACpB,IAAI,CAAC,IAAI,CAACJ,OAAO,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAEqJ,KAAK,CAACjL,GAAG,GAAGD,aAAa,CAACkL,KAAK,CAACjL,GAAG,CAAC;MACjFiL,KAAK,CAACjL,GAAG,GAAG,IAAI,CAACwB,YAAY,CAACyJ,KAAK,CAACjL,GAAG,CAAC;MAExC,MAAMkL,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI,IAAI,CAACpJ,SAAS,EAAE;QACpB,MAAMqE,MAAM,GAAG,IAAI,CAACrC,GAAG,CAACqH,gBAAgB,IAAIF,KAAK;QACjD,IAAI,CAAClK,MAAM,CAAC,QAAQ,CAAC;QACrB,IAAI,CAAC4G,IAAI,CAAC,QAAQ,EAAE;UAClBb,IAAI,EAAEX,MAAM,CAACW,IAAI;UACjB9G,GAAG,EAAEmG,MAAM,CAACnG;QACd,CAAC,CAAC;MACJ,CAAC;MAED,MAAMoL,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI,CAACrK,MAAM,CAAC,qBAAqB,CAAC;QAClC,IAAI,IAAI,CAACe,SAAS,EAAE;QACpB,IAAI,IAAI,CAACJ,OAAO,IAAI,IAAI,CAACmB,YAAY,EAAEqI,SAAS,CAAC,CAAC,MAC7C,IAAI,CAACvF,IAAI,CAAC,cAAc,EAAEuF,SAAS,CAAC,EAAC;MAC5C,CAAC;MAED,MAAMG,OAAO,GAAGrH,GAAG,IAAI;QACrB,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,2BAA2B,CAAC,CAAC;MACzD,CAAC;MAED,IAAI,CAACF,GAAG,CAACwH,mBAAmB,CAACL,KAAK,CAAC,CAChC9D,IAAI,CAACiE,SAAS,CAAC,CACfrG,KAAK,CAACsG,OAAO,CAAC;IACnB,CAAC,CAAC,CACDtG,KAAK,CAACf,GAAG,IAAI;MACZ,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IAChD,CAAC,CAAC;EACN;EAEAuH,2BAA2BA,CAAA,EAAI;IAC7B,IAAI,IAAI,CAACzH,GAAG,CAAC0H,eAAe,EAAE;MAC5B,IAAI,CAAC1H,GAAG,CAAC0H,eAAe,CAAC,CAAC,CAACpG,OAAO,CAACqG,WAAW,IAAI;QAChD,IAAI,CAACA,WAAW,CAACC,GAAG,IAAID,WAAW,CAACxD,MAAM,CAACJ,KAAK,IAAI,CAAC4D,WAAW,CAACE,SAAS,EAAE;UAC1EF,WAAW,CAACE,SAAS,GAAG,IAAI,EAAC;UAC7B,IAAI,CAAClF,cAAc,CAACgF,WAAW,CAACxD,MAAM,CAACJ,KAAK,CAACnB,IAAI,CAAC;QACpD;MACF,CAAC,CAAC;IACJ;EACF;EAEAU,aAAaA,CAAA,EAAI;IACf,IAAI,IAAI,CAACtF,SAAS,EAAE;IAEpB,IAAI,CAACgC,GAAG,CAAC8H,YAAY,CAAC,IAAI,CAACrK,aAAa,CAAC,CACtC4F,IAAI,CAAC0E,MAAM,IAAI;MACd,IAAI,IAAI,CAAC/J,SAAS,EAAE;MACpB,IAAI,CAAC,IAAI,CAACJ,OAAO,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAEiK,MAAM,CAAC7L,GAAG,GAAGD,aAAa,CAAC8L,MAAM,CAAC7L,GAAG,CAAC;MACnF6L,MAAM,CAAC7L,GAAG,GAAG,IAAI,CAACwB,YAAY,CAACqK,MAAM,CAAC7L,GAAG,CAAC;MAE1C,MAAM8L,UAAU,GAAGA,CAAA,KAAM;QACvB,IAAI,IAAI,CAAChK,SAAS,EAAE;QACpB,MAAMqE,MAAM,GAAG,IAAI,CAACrC,GAAG,CAACqH,gBAAgB,IAAIU,MAAM;QAClD,IAAI,CAAC9K,MAAM,CAAC,QAAQ,CAAC;QACrB,IAAI,CAAC4G,IAAI,CAAC,QAAQ,EAAE;UAClBb,IAAI,EAAEX,MAAM,CAACW,IAAI;UACjB9G,GAAG,EAAEmG,MAAM,CAACnG;QACd,CAAC,CAAC;QACF,IAAI,CAAC,IAAI,CAACiB,SAAS,EAAE,IAAI,CAACsK,2BAA2B,CAAC,CAAC;MACzD,CAAC;MAED,MAAMH,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI,IAAI,CAACtJ,SAAS,EAAE;QACpB,IAAI,IAAI,CAACJ,OAAO,IAAI,IAAI,CAACmB,YAAY,EAAEiJ,UAAU,CAAC,CAAC,MAC9C,IAAI,CAACnG,IAAI,CAAC,cAAc,EAAEmG,UAAU,CAAC;MAC5C,CAAC;MAED,MAAMT,OAAO,GAAGrH,GAAG,IAAI;QACrB,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,2BAA2B,CAAC,CAAC;MACzD,CAAC;MAED,IAAI,CAACF,GAAG,CAACwH,mBAAmB,CAACO,MAAM,CAAC,CACjC1E,IAAI,CAACiE,SAAS,CAAC,CACfrG,KAAK,CAACsG,OAAO,CAAC;IACnB,CAAC,CAAC,CACDtG,KAAK,CAACf,GAAG,IAAI;MACZ,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,mBAAmB,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EAEAQ,wBAAwBA,CAAA,EAAI;IAC1B,IAAI,IAAI,CAAC1C,SAAS,EAAE;IACpB,IAAI,IAAI,CAACgC,GAAG,CAACiI,eAAe,KAAK,QAAQ,EAAE;MACzC,IAAI,CAAC9H,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,oBAAoB,CAAC,EAAE,wBAAwB,CAAC,CAAC;IAClF;EACF;EAEA2B,iBAAiBA,CAAA,EAAI;IACnB,IAAI,IAAI,CAACvC,SAAS,EAAE;IACpB,MAAMkK,kBAAkB,GAAG,IAAI,CAAClI,GAAG,CAACkI,kBAAkB;IACtD,MAAMC,iBAAiB,GAAG,IAAI,CAACnI,GAAG,CAACmI,iBAAiB;IAEpD,IAAI,CAAClL,MAAM,CACT,iDAAiD,EACjDiL,kBAAkB,EAClBC,iBACF,CAAC;IACD,IAAI,CAACtE,IAAI,CAAC,gBAAgB,EAAEqE,kBAAkB,EAAEC,iBAAiB,CAAC;IAElE,IAAID,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,WAAW,EAAE;MAC5E,IAAI,CAACrJ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACuJ,WAAW,CAAC,CAAC;IACpB;IACA,IAAIF,kBAAkB,KAAK,QAAQ,EAAE;MACnC,IAAI,CAAC/H,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,wBAAwB,CAAC,EAAE,4BAA4B,CAAC,CAAC;IAC1F;IACA,IAAIsJ,kBAAkB,KAAK,QAAQ,EAAE;MACnC,IAAI,CAAC/H,OAAO,CAACvE,OAAO,CAAC,IAAIgD,KAAK,CAAC,wBAAwB,CAAC,EAAE,2BAA2B,CAAC,CAAC;IACzF;EACF;EAEAyJ,QAAQA,CAAErD,EAAE,EAAE;IACZ;IACA,MAAMsD,aAAa,GAAGC,MAAM,IAAI;MAC9B,IAAI5L,MAAM,CAAC6L,SAAS,CAACzL,QAAQ,CAAC0L,IAAI,CAACF,MAAM,CAACG,MAAM,CAAC,KAAK,gBAAgB,EAAE;QACtEH,MAAM,CAACG,MAAM,CAACpH,OAAO,CAACqH,KAAK,IAAI;UAC7BhM,MAAM,CAACC,MAAM,CAAC2L,MAAM,EAAEI,KAAK,CAAC;QAC9B,CAAC,CAAC;MACJ;MACA,OAAOJ,MAAM;IACf,CAAC;;IAED;IACA,IAAI,IAAI,CAACvI,GAAG,CAACqI,QAAQ,CAACO,MAAM,KAAK,CAAC,IAAI,IAAI,CAACxI,oBAAoB,EAAE;MAC/D,IAAI,CAACJ,GAAG,CAACqI,QAAQ,CAAC,CAAC,CAChBhF,IAAI,CAACwF,GAAG,IAAI;QACX,MAAMC,OAAO,GAAG,EAAE;QAClBD,GAAG,CAACvH,OAAO,CAACiH,MAAM,IAAI;UACpBO,OAAO,CAAC5F,IAAI,CAACoF,aAAa,CAACC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC;QACFvD,EAAE,CAAC,IAAI,EAAE8D,OAAO,CAAC;MACnB,CAAC,EAAE5I,GAAG,IAAI8E,EAAE,CAAC9E,GAAG,CAAC,CAAC;;MAEtB;IACA,CAAC,MAAM,IAAI,IAAI,CAACF,GAAG,CAACqI,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC5I,GAAG,CAACqI,QAAQ,CAACQ,GAAG,IAAI;QACvB;QACA,IAAI,IAAI,CAAC7K,SAAS,EAAE;QAEpB,MAAM8K,OAAO,GAAG,EAAE;QAClBD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACzH,OAAO,CAACyH,MAAM,IAAI;UAC7B,MAAMR,MAAM,GAAG,CAAC,CAAC;UACjBQ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC1H,OAAO,CAACoD,IAAI,IAAI;YAC7B6D,MAAM,CAAC7D,IAAI,CAAC,GAAGqE,MAAM,CAACE,IAAI,CAACvE,IAAI,CAAC;UAClC,CAAC,CAAC;UACF6D,MAAM,CAACW,EAAE,GAAGH,MAAM,CAACG,EAAE;UACrBX,MAAM,CAACvF,IAAI,GAAG+F,MAAM,CAAC/F,IAAI;UACzBuF,MAAM,CAACY,SAAS,GAAGJ,MAAM,CAACI,SAAS;UACnCL,OAAO,CAAC5F,IAAI,CAACoF,aAAa,CAACC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC;QACFvD,EAAE,CAAC,IAAI,EAAE8D,OAAO,CAAC;MACnB,CAAC,EAAE5I,GAAG,IAAI8E,EAAE,CAAC9E,GAAG,CAAC,CAAC;;MAEpB;MACA;IACA,CAAC,MAAM;MACL8E,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;IACd;EACF;EAEAoD,WAAWA,CAAA,EAAI;IACb,IAAI,CAACnL,MAAM,CAAC,6BAA6B,EAAE,IAAI,CAAC4B,QAAQ,EAAE,IAAI,CAACC,aAAa,CAAC;IAC7E,IAAI,IAAI,CAACZ,UAAU,IAAI,IAAI,CAACkL,WAAW,IAAI,CAAC,IAAI,CAACvK,QAAQ,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;IAElF,IAAI,CAACsK,WAAW,GAAG,IAAI;;IAEvB;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI,IAAI,CAACrL,SAAS,EAAE;MAEpB,IAAI,CAACqK,QAAQ,CAAC,CAACnI,GAAG,EAAEoJ,KAAK,KAAK;QAC5B,IAAI,IAAI,CAACtL,SAAS,EAAE;;QAEpB;QACA,IAAIkC,GAAG,EAAEoJ,KAAK,GAAG,EAAE;QAEnB,MAAMC,gBAAgB,GAAG,CAAC,CAAC;QAC3B,MAAMC,eAAe,GAAG,CAAC,CAAC;QAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIC,0BAA0B,GAAG,KAAK;QAEtCJ,KAAK,CAAChI,OAAO,CAACqI,IAAI,IAAI;UACpB;UACA;UACA,IAAIA,IAAI,CAAC3G,IAAI,KAAK,iBAAiB,IAAI2G,IAAI,CAAC3G,IAAI,KAAK,kBAAkB,EAAE;YACvEuG,gBAAgB,CAACI,IAAI,CAACT,EAAE,CAAC,GAAGS,IAAI;UAClC;UACA,IAAIA,IAAI,CAAC3G,IAAI,KAAK,gBAAgB,IAAI2G,IAAI,CAAC3G,IAAI,KAAK,iBAAiB,EAAE;YACrEwG,eAAe,CAACG,IAAI,CAACT,EAAE,CAAC,GAAGS,IAAI;UACjC;UACA,IAAIA,IAAI,CAAC3G,IAAI,KAAK,eAAe,IAAI2G,IAAI,CAAC3G,IAAI,KAAK,gBAAgB,EAAE;YACnEyG,cAAc,CAACE,IAAI,CAACT,EAAE,CAAC,GAAGS,IAAI;UAChC;QACF,CAAC,CAAC;QAEF,MAAMC,wBAAwB,GAAGC,qBAAqB,IAAI;UACxDH,0BAA0B,GAAG,IAAI;UAEjC,IAAII,KAAK,GAAGN,eAAe,CAACK,qBAAqB,CAACE,gBAAgB,CAAC;UAEnE,IAAID,KAAK,KAAKA,KAAK,CAACE,EAAE,IAAIF,KAAK,CAAC5H,OAAO,CAAC,EAAE;YACxC;YACA,IAAI,CAAC5D,YAAY,GAAGwL,KAAK,CAACE,EAAE,IAAIF,KAAK,CAAC5H,OAAO;YAC7C,IAAI,CAAC1D,SAAS,GAAGyL,MAAM,CAACH,KAAK,CAAC3H,IAAI,CAAC;UACrC,CAAC,MAAM,IAAI2H,KAAK,IAAIA,KAAK,CAACI,SAAS,EAAE;YACnC;YACA,IAAI,CAAC5L,YAAY,GAAGwL,KAAK,CAACI,SAAS;YACnC,IAAI,CAAC1L,SAAS,GAAGyL,MAAM,CAACH,KAAK,CAACK,UAAU,CAAC;UAC3C,CAAC,MAAM,IAAI,OAAON,qBAAqB,CAACO,gBAAgB,KAAK,QAAQ,EAAE;YACrE;YACAN,KAAK,GAAGD,qBAAqB,CAACO,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC;YACzD,IAAI,CAAC/L,YAAY,GAAGwL,KAAK,CAAC,CAAC,CAAC;YAC5B,IAAI,CAACtL,SAAS,GAAGyL,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;UACnC;UACA,IAAI,IAAI,CAACxL,YAAY,EAAE;YACrB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACD,YAAY,CAACgM,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;UACtE;UAEA,IAAIC,MAAM,GAAGhB,gBAAgB,CAACM,qBAAqB,CAACW,iBAAiB,CAAC;UAEtE,IAAID,MAAM,KAAKA,MAAM,CAACP,EAAE,IAAIO,MAAM,CAACrI,OAAO,CAAC,EAAE;YAC3C;YACA,IAAI,CAAC/D,aAAa,GAAGoM,MAAM,CAACP,EAAE,IAAIO,MAAM,CAACrI,OAAO;YAChD,IAAI,CAAC7D,UAAU,GAAG4L,MAAM,CAACM,MAAM,CAACpI,IAAI,CAAC;UACvC,CAAC,MAAM,IAAIoI,MAAM,IAAIA,MAAM,CAACL,SAAS,EAAE;YACrC;YACA,IAAI,CAAC/L,aAAa,GAAGoM,MAAM,CAACL,SAAS;YACrC,IAAI,CAAC7L,UAAU,GAAG4L,MAAM,CAACM,MAAM,CAACJ,UAAU,CAAC;UAC7C,CAAC,MAAM,IAAI,OAAON,qBAAqB,CAACY,iBAAiB,KAAK,QAAQ,EAAE;YACtE;YACAF,MAAM,GAAGV,qBAAqB,CAACY,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC;YAC3D,IAAI,CAAClM,aAAa,GAAGoM,MAAM,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAClM,UAAU,GAAG4L,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;UACrC;UACA,IAAI,IAAI,CAACpM,aAAa,EAAE;YACtB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,aAAa,CAACmM,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;UACxE;UAEA,IAAI,CAACrN,MAAM,CACT,oCAAoC,EACpC,IAAI,CAACqB,YAAY,EACjB,IAAI,CAACE,SAAS,EACd,IAAI,CAACL,aAAa,EAClB,IAAI,CAACE,UACP,CAAC;QACH,CAAC;QAEDiL,KAAK,CAAChI,OAAO,CAACqI,IAAI,IAAI;UACpB;UACA,IAAIA,IAAI,CAAC3G,IAAI,KAAK,WAAW,IAAI2G,IAAI,CAACe,uBAAuB,EAAE;YAC7Dd,wBAAwB,CAACH,cAAc,CAACE,IAAI,CAACe,uBAAuB,CAAC,CAAC;UACxE;;UAEA;UACA,IACGf,IAAI,CAAC3G,IAAI,KAAK,mBAAmB,IAAI2G,IAAI,CAACgB,oBAAoB,KAAK,MAAM,IACzE,CAAChB,IAAI,CAAC3G,IAAI,KAAK,eAAe,IAAI2G,IAAI,CAAC3G,IAAI,KAAK,gBAAgB,KAAK2G,IAAI,CAACiB,QAAS,EACpF;YACAhB,wBAAwB,CAACD,IAAI,CAAC;UAChC;QACF,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAACD,0BAA0B,KAAK,CAAC/M,MAAM,CAACkO,IAAI,CAACpB,cAAc,CAAC,CAACb,MAAM,IAAIjM,MAAM,CAACkO,IAAI,CAACrB,eAAe,CAAC,CAACZ,MAAM,CAAC,EAAE;UAC/G/D,UAAU,CAACwE,iBAAiB,EAAE,GAAG,CAAC;UAClC;QACF,CAAC,MAAM;UACL,IAAI,CAACD,WAAW,GAAG,KAAK;UACxB,IAAI,CAAClL,UAAU,GAAG,IAAI;QACxB;QAEA,IAAI,IAAI,CAAC2B,MAAM,EAAE;UACf,IAAI;YACF,IAAI,CAAC8D,IAAI,CAAC,IAAI,CAAC9D,MAAM,CAAC;UACxB,CAAC,CAAC,OAAOK,GAAG,EAAE;YACZ,OAAO,IAAI,CAACC,OAAO,CAACvE,OAAO,CAACsE,GAAG,EAAE,kBAAkB,CAAC,CAAC;UACvD;UACA,IAAI,CAACL,MAAM,GAAG,IAAI;UAClB,IAAI,CAAC5C,MAAM,CAAC,wCAAwC,CAAC;UAErD,MAAM+H,EAAE,GAAG,IAAI,CAAClF,GAAG;UACnB,IAAI,CAACA,GAAG,GAAG,IAAI;UACfkF,EAAE,CAAC,IAAI,CAAC;QACV;;QAEA;QACA;QACA,IAAI,OAAO,IAAI,CAAC/F,QAAQ,CAAC+G,0BAA0B,KAAK,QAAQ,EAAE;UAChE,IAAI,CAACjG,SAAS,GAAG6G,WAAW,CAAC,MAAM,IAAI,CAACkE,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;UAC3D,IAAI,IAAI,CAAC/K,SAAS,CAACgL,KAAK,EAAE,IAAI,CAAChL,SAAS,CAACgL,KAAK,CAAC,CAAC;QAClD;QAEA,IAAI,CAAC9N,MAAM,CAAC,SAAS,CAAC;QACtB,IAAI,CAAC4G,IAAI,CAAC,SAAS,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IACDwF,iBAAiB,CAAC,CAAC;EACrB;EAEAyB,WAAWA,CAAA,EAAI;IACb,IAAI,CAAC,IAAI,CAAChL,GAAG,IAAI,CAAC,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC8C,cAAc,GAAGjG,mBAAmB,EAAE;MACrF;IACF;IACA,IAAI,CAACsK,2BAA2B,CAAC,CAAC;EACpC;EAEAxF,uBAAuBA,CAAA,EAAI;IACzB,IAAI,IAAI,CAAC5C,SAAS,EAAE;IAEpB,IAAI,IAAI,CAACgC,GAAG,CAACgL,cAAc,KAAK,QAAQ,EAAE;MACxC,IAAI,CAAC7L,cAAc,GAAG,KAAK;;MAE3B;MACA,IAAI,CAAClC,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAACsC,sBAAsB,CAAC;MACjE,IAAI,CAACA,sBAAsB,CAAC+B,OAAO,CAAC6C,MAAM,IAAI;QAC5C,IAAI,CAACnE,GAAG,CAACyE,WAAW,CAACN,MAAM,CAAC;QAC5B,IAAI,CAAC7E,kBAAkB,GAAG,IAAI;MAChC,CAAC,CAAC;MACF,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAEhC,IAAI,IAAI,CAACD,kBAAkB,EAAE;QAC3B,IAAI,CAACrC,MAAM,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACqC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACoC,iBAAiB,CAAC,CAAC,EAAC;MAC3B,CAAC,MAAM;QACL,IAAI,CAACzE,MAAM,CAAC,YAAY,CAAC;QACzB,IAAI,CAAC4G,IAAI,CAAC,YAAY,CAAC;MACzB;IACF;IAEA,IAAI,CAAC5G,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAAC+C,GAAG,CAACgL,cAAc,CAAC;IAC/D,IAAI,CAACnH,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC7D,GAAG,CAACgL,cAAc,CAAC;EAC5D;EAEAjK,eAAeA,CAAED,KAAK,EAAE;IACtB,IAAI,IAAI,CAAC9C,SAAS,EAAE;IACpB,IAAI8C,KAAK,CAACgC,SAAS,IAAI,IAAI,CAAClF,OAAO,EAAE;MACnC,IAAI,CAACiG,IAAI,CAAC,QAAQ,EAAE;QAClBb,IAAI,EAAE,WAAW;QACjBF,SAAS,EAAE;UACTA,SAAS,EAAEhC,KAAK,CAACgC,SAAS,CAACA,SAAS;UACpCmI,aAAa,EAAEnK,KAAK,CAACgC,SAAS,CAACmI,aAAa;UAC5CC,MAAM,EAAEpK,KAAK,CAACgC,SAAS,CAACoI;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACpK,KAAK,CAACgC,SAAS,IAAI,CAAC,IAAI,CAAC/D,YAAY,EAAE;MACjD,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC8E,IAAI,CAAC,cAAc,CAAC;IAC3B;IACA;IACA,IAAI/C,KAAK,CAACgC,SAAS,EAAE;MACnB,IAAI,CAACmE,wBAAwB,CAAC,CAAC;IACjC;EACF;EAEAf,iBAAiBA,CAAEpF,KAAK,EAAE;IACxB,IAAI,IAAI,CAAC9C,SAAS,EAAE;IACpB,IAAIsE,IAAI,GAAGxB,KAAK,CAACwB,IAAI;IACrB,IAAIA,IAAI,YAAY6I,WAAW,EAAE7I,IAAI,GAAGzG,MAAM,CAACuP,IAAI,CAAC9I,IAAI,CAAC;IACzD,IAAI,CAACY,IAAI,CAACZ,IAAI,CAAC;EACjB;EAEA8D,2BAA2BA,CAAA,EAAI;IAC7B,IAAI,IAAI,CAACpI,SAAS,IAAI,CAAC,IAAI,CAAC8B,GAAG,EAAE;IACjC,IAAI,CAAC7C,MAAM,CAAC,wCAAwC,EAAE,IAAI,CAACgC,QAAQ,CAAC8C,cAAc,CAAC;IACnF,MAAMiD,EAAE,GAAG,IAAI,CAAClF,GAAG;IACnB,IAAI,CAACA,GAAG,GAAG,IAAI;IACfkF,EAAE,CAAC,IAAI,CAAC;EACV;EAEAqB,cAAcA,CAAA,EAAI;IAChB,IAAI,IAAI,CAACnI,UAAU,IAAI,IAAI,CAACF,SAAS,EAAE;IACvC,IAAI,CAACf,MAAM,CAAC,iBAAiB,CAAC;IAC9B,IAAI,CAAC6B,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsJ,WAAW,CAAC,CAAC;EACpB;EAEA9B,eAAeA,CAAA,EAAI;IACjB,IAAI,IAAI,CAACtI,SAAS,EAAE;IACpB,IAAI,CAACf,MAAM,CAAC,kBAAkB,CAAC;IAC/B,IAAI,CAACkD,OAAO,CAAC,CAAC;EAChB;EAEAsB,QAAQA,CAAEX,KAAK,EAAE;IACf,IAAI,IAAI,CAAC9C,SAAS,EAAE;IAEpB8C,KAAK,CAACnD,OAAO,CAAC2D,OAAO,CAAC+J,WAAW,IAAI;MACnC,IAAI,CAACpO,MAAM,CAAC,UAAU,CAAC;MACvB,IAAI,CAAC4G,IAAI,CAAC,OAAO,EAAE/C,KAAK,CAACiD,KAAK,EAAEsH,WAAW,CAAC;MAE5C,IAAI,CAAC1L,aAAa,CAACuD,IAAI,CAAC;QACtBa,KAAK,EAAEjD,KAAK,CAACiD,KAAK;QAClBrI,MAAM,EAAE2P;MACV,CAAC,CAAC;MAEF,IAAI,IAAI,CAACzL,cAAc,CAAC0L,IAAI,CAACC,YAAY,IAAI;QAC3C,OAAOA,YAAY,CAACrC,EAAE,KAAKmC,WAAW,CAACnC,EAAE;MAC3C,CAAC,CAAC,EAAE,OAAM,CAAC;;MAEX,IAAI,CAACtJ,cAAc,CAACsD,IAAI,CAACmI,WAAW,CAAC;MACrC1P,cAAc,CAAC,MAAM;QACnB,IAAI,CAACsB,MAAM,CAAC,WAAW,CAAC;QACxB,IAAI,CAAC4G,IAAI,CAAC,QAAQ,EAAEwH,WAAW,CAAC,EAAC;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEApO,MAAMA,CAAA,EAAI;IACR,MAAMuO,IAAI,GAAG,EAAE,CAACxO,KAAK,CAACyL,IAAI,CAACgD,SAAS,CAAC;IACrCD,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC1O,GAAG,GAAG,IAAI,GAAG0O,IAAI,CAAC,CAAC,CAAC;IACzClQ,KAAK,CAACoQ,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;EACzB;AACF;AAEAjP,IAAI,CAACoP,cAAc,GAAG,CAAC,CAACnQ,aAAa,CAAC,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACAe,IAAI,CAACgB,MAAM,GAAG;EACZqO,UAAU,EAAE,CACV;IACEC,IAAI,EAAE,CACJ,8BAA8B,EAC9B,kCAAkC;EAEtC,CAAC,CACF;EACDC,YAAY,EAAE;AAChB,CAAC;AAEDvP,IAAI,CAACa,aAAa,GAAG,CAAC,CAAC;AAEvB2O,MAAM,CAACC,OAAO,GAAGzP,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}