import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const ResearchRepository = () => {
  const [papers, setPapers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [selectedPaper, setSelectedPaper] = useState(null);
  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  const [newPaper, setNewPaper] = useState({
    title: '',
    abstract: '',
    category: '',
    keywords: '',
    content: ''
  });
  const { user, token, isAuthenticated, isVerified } = useAuth();

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

  useEffect(() => {
    fetchPapers();
  }, []);

  const fetchPapers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/research`);
      setPapers(response.data.data || []);
    } catch (err) {
      setError('Failed to fetch research papers');
      console.error('Error fetching papers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitPaper = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError('Please login to submit a paper');
      return;
    }

    try {
      const keywordsArray = newPaper.keywords.split(',').map(k => k.trim()).filter(k => k);

      const response = await axios.post(
        `${API_URL}/api/research`,
        {
          ...newPaper,
          keywords: keywordsArray
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        setNewPaper({ title: '', abstract: '', category: '', keywords: '', content: '' });
        setShowSubmissionForm(false);
        fetchPapers(); // Refresh the list
      }
    } catch (err) {
      setError('Failed to submit paper');
      console.error('Error submitting paper:', err);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const filteredPapers = papers.filter(paper =>
    filter === 'all' || paper.status === filter
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading research papers...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchPapers}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Paper submission form
  if (showSubmissionForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setShowSubmissionForm(false)}
              className="text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Repository
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Submit Research Paper</h1>
            <p className="text-gray-600">Submit your political research for peer review and publication.</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <form onSubmit={handleSubmitPaper}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Paper Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={newPaper.title}
                  onChange={(e) => setNewPaper({ ...newPaper, title: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter the title of your research paper"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  id="category"
                  value={newPaper.category}
                  onChange={(e) => setNewPaper({ ...newPaper, category: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a category</option>
                  <option value="Political Theory">Political Theory</option>
                  <option value="Comparative Politics">Comparative Politics</option>
                  <option value="International Relations">International Relations</option>
                  <option value="Public Policy">Public Policy</option>
                  <option value="Electoral Systems">Electoral Systems</option>
                  <option value="Political Communication">Political Communication</option>
                  <option value="Political Economy">Political Economy</option>
                  <option value="Constitutional Law">Constitutional Law</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-2">
                  Abstract *
                </label>
                <textarea
                  id="abstract"
                  value={newPaper.abstract}
                  onChange={(e) => setNewPaper({ ...newPaper, abstract: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="6"
                  placeholder="Provide a comprehensive abstract of your research (250-500 words)"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="keywords" className="block text-sm font-medium text-gray-700 mb-2">
                  Keywords *
                </label>
                <input
                  type="text"
                  id="keywords"
                  value={newPaper.keywords}
                  onChange={(e) => setNewPaper({ ...newPaper, keywords: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter keywords separated by commas (e.g., democracy, voting, elections)"
                  required
                />
                <p className="text-sm text-gray-500 mt-1">Separate keywords with commas</p>
              </div>

              <div className="mb-6">
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Paper Content *
                </label>
                <textarea
                  id="content"
                  value={newPaper.content}
                  onChange={(e) => setNewPaper({ ...newPaper, content: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="20"
                  placeholder="Paste the full content of your research paper here..."
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowSubmissionForm(false)}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Submit for Review
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Research Repository</h1>
          <p className="text-lg text-gray-600">
            Access peer-reviewed political research papers and submit your own work
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Filters</h2>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Papers</option>
                  <option value="published">Published</option>
                  <option value="under_review">Under Review</option>
                  <option value="submitted">Submitted</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>

              {isAuthenticated ? (
                <button
                  onClick={() => setShowSubmissionForm(true)}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mb-4"
                >
                  Submit Paper
                </button>
              ) : (
                <a
                  href="/login"
                  className="w-full bg-gray-400 text-white py-2 px-4 rounded-md text-center block mb-4"
                  title="Login required"
                >
                  Login to Submit
                </a>
              )}

              <div className="text-sm text-gray-600">
                <h3 className="font-medium mb-2">Submission Guidelines</h3>
                <ul className="space-y-1">
                  <li>• Original research only</li>
                  <li>• Peer review required</li>
                  <li>• APA citation format</li>
                  <li>• Maximum 10,000 words</li>
                  {!isVerified && isAuthenticated && (
                    <li className="text-orange-600">• Verified status recommended</li>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Research Papers ({filteredPapers.length})
                  </h2>
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {filteredPapers.length === 0 ? (
                  <div className="p-12 text-center">
                    <div className="text-gray-400 text-6xl mb-4">📄</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No research papers found</h3>
                    <p className="text-gray-600 mb-4">
                      {filter === 'all'
                        ? 'Be the first to submit a research paper!'
                        : `No papers with status "${filter.replace('_', ' ')}" found.`
                      }
                    </p>
                    {isAuthenticated && filter === 'all' && (
                      <button
                        onClick={() => setShowSubmissionForm(true)}
                        className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                      >
                        Submit First Paper
                      </button>
                    )}
                  </div>
                ) : (
                  filteredPapers.map((paper) => (
                    <div key={paper.id} className="p-6 hover:bg-gray-50">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-lg font-medium text-gray-900 flex-1 mr-4">
                          {paper.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`}>
                          {paper.status.replace('_', ' ')}
                        </span>
                      </div>

                      <p className="text-gray-600 mb-3 line-clamp-3">
                        {paper.abstract}
                      </p>

                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <span><strong>Author:</strong> {paper.author_username}</span>
                        <span><strong>Category:</strong> {paper.category}</span>
                        <span><strong>Submitted:</strong> {formatDate(paper.created_at)}</span>
                        {paper.status === 'published' && paper.published_at && (
                          <span><strong>Published:</strong> {formatDate(paper.published_at)}</span>
                        )}
                      </div>

                      {paper.keywords && paper.keywords.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {paper.keywords.map((keyword, index) => (
                            <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                              {keyword}
                            </span>
                          ))}
                        </div>
                      )}

                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          {paper.download_count > 0 && <span>{paper.download_count} downloads</span>}
                          {paper.citation_count > 0 && <span>{paper.citation_count} citations</span>}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setSelectedPaper(paper)}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            View Details
                          </button>
                          {paper.status === 'published' && (
                            <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                              Download
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchRepository;
