require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const http = require('http');

// Import database and auth modules
const { testConnection, userQueries, articleQueries } = require('./lib/database');
const {
  generateToken,
  hashPassword,
  comparePassword,
  verifyToken,
  requireVerified,
  requireAdmin,
  optionalAuth,
  authRateLimit,
  validateEmail,
  validatePassword,
  validateUsername
} = require('./middleware/auth');

const app = express();
const server = http.createServer(app);
const port = process.env.PORT || 3000;

// Import WebSocket manager
const WebSocketManager = require('./lib/websocket');

// Import route modules
const discussionsRouter = require('./routes/discussions');
const commentsRouter = require('./routes/comments');
const articlesRouter = require('./routes/articles');
const researchRouter = require('./routes/research');
const debatesRouter = require('./routes/debates');
const verificationRouter = require('./routes/verification');
const feedRouter = require('./routes/feed');
const feedCommentsRouter = require('./routes/feed-comments');

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://your-frontend-domain.com']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:3003'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Politica API!',
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

// Test PostgreSQL connection
app.get('/health/db', async (req, res) => {
  try {
    const isConnected = await testConnection();

    if (isConnected) {
      res.json({
        status: 'healthy',
        message: 'PostgreSQL database connection successful',
        database: process.env.DB_NAME,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: 'Database connection failed'
      });
    }
  } catch (err) {
    res.status(500).json({
      status: 'error',
      message: 'Database connection failed',
      error: err.message
    });
  }
});

// Legacy articles endpoint - redirects to new route
app.get('/api/articles-legacy', optionalAuth, async (req, res) => {
  try {
    const articles = await articleQueries.getPublished();

    res.json({
      data: articles,
      count: articles.length,
      message: 'Articles retrieved successfully'
    });
  } catch (err) {
    console.error('Error fetching articles:', err);
    res.status(500).json({ error: 'Failed to fetch articles' });
  }
});

// User registration
app.post('/api/auth/register',
  rateLimit(authRateLimit),
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
    body('username').isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/)
  ],
  async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email, password, username } = req.body;

      // Additional validation
      if (!validateEmail(email)) {
        return res.status(400).json({ error: 'Invalid email format' });
      }

      if (!validatePassword(password)) {
        return res.status(400).json({
          error: 'Password must be at least 8 characters with uppercase, lowercase, and number'
        });
      }

      if (!validateUsername(username)) {
        return res.status(400).json({
          error: 'Username must be 3-30 characters, alphanumeric and underscores only'
        });
      }

      // Check if user already exists
      const existingUserByEmail = await userQueries.findByEmail(email);
      if (existingUserByEmail) {
        return res.status(400).json({ error: 'Email already registered' });
      }

      const existingUserByUsername = await userQueries.findByUsername(username);
      if (existingUserByUsername) {
        return res.status(400).json({ error: 'Username already taken' });
      }

      // Hash password and create user
      const passwordHash = await hashPassword(password);
      const newUser = await userQueries.createUser({
        username,
        email,
        passwordHash
      });

      // Generate token
      const token = generateToken(newUser);

      res.status(201).json({
        message: 'User registered successfully',
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          role: newUser.role,
          verified_status: newUser.verified_status
        },
        token
      });
    } catch (err) {
      console.error('Registration error:', err);
      res.status(500).json({ error: 'Registration failed' });
    }
  }
);

// User login
app.post('/api/auth/login',
  rateLimit(authRateLimit),
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty()
  ],
  async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email, password } = req.body;

      // Find user by email
      const user = await userQueries.findByEmail(email);
      if (!user) {
        return res.status(401).json({ error: 'Invalid email or password' });
      }

      // Check password
      const isValidPassword = await comparePassword(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({ error: 'Invalid email or password' });
      }

      // Generate token
      const token = generateToken(user);

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          verified_status: user.verified_status
        },
        token
      });
    } catch (err) {
      console.error('Login error:', err);
      res.status(500).json({ error: 'Login failed' });
    }
  }
);

// Get user profile
app.get('/api/auth/profile', verifyToken, async (req, res) => {
  try {
    res.json({
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        role: req.user.role,
        verified_status: req.user.verified_status,
        bio: req.user.bio,
        avatar_url: req.user.avatar_url,
        created_at: req.user.created_at
      }
    });
  } catch (err) {
    console.error('Profile error:', err);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Update user profile
app.put('/api/auth/profile', verifyToken, async (req, res) => {
  try {
    const { username, bio } = req.body;
    const updates = {};

    if (username && username !== req.user.username) {
      if (!validateUsername(username)) {
        return res.status(400).json({
          error: 'Username must be 3-30 characters, alphanumeric and underscores only'
        });
      }

      const existingUser = await userQueries.findByUsername(username);
      if (existingUser && existingUser.id !== req.user.id) {
        return res.status(400).json({ error: 'Username already taken' });
      }

      updates.username = username;
    }

    if (bio !== undefined) {
      updates.bio = bio;
    }

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    const updatedUser = await userQueries.updateProfile(req.user.id, updates);

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role,
        verified_status: updatedUser.verified_status,
        bio: updatedUser.bio,
        avatar_url: updatedUser.avatar_url
      }
    });
  } catch (err) {
    console.error('Profile update error:', err);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Mount route modules
app.use('/api/discussions', discussionsRouter);
app.use('/api/comments', commentsRouter);
app.use('/api/articles', articlesRouter);
app.use('/api/research', researchRouter);
app.use('/api/debates', debatesRouter);
app.use('/api/verification', verificationRouter);
app.use('/api/feed', feedRouter);
app.use('/api/feed-comments', feedCommentsRouter);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server with database connection test
const startServer = async () => {
  try {
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Failed to connect to database. Server not started.');
      process.exit(1);
    }

    // Initialize WebSocket manager
    const wsManager = new WebSocketManager(server);

    // Make WebSocket manager available to routes
    app.set('wsManager', wsManager);

    server.listen(port, () => {
      console.log(`🚀 Politica server running at http://localhost:${port}`);
      console.log(`🔌 WebSocket server enabled for real-time features`);
      console.log(`📊 Health check: http://localhost:${port}/health/db`);
      console.log(`📚 API documentation: http://localhost:${port}/api/articles`);
      console.log(`🔐 Database: ${process.env.DB_NAME} on ${process.env.DB_HOST}:${process.env.DB_PORT}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

module.exports = app;
