{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\components\\\\VideoDebate.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport Peer from 'simple-peer';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoDebate = ({\n  debateId,\n  isVisible,\n  onClose,\n  debate\n}) => {\n  _s();\n  const [localStream, setLocalStream] = useState(null);\n  const [peers, setPeers] = useState({});\n  const [isVideoEnabled, setIsVideoEnabled] = useState(true);\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [isScreenSharing, setIsScreenSharing] = useState(false);\n  const [isModerator, setIsModerator] = useState(false);\n  const [speakingQueue, setSpeakingQueue] = useState([]);\n  const [currentSpeaker, setCurrentSpeaker] = useState(null);\n  const [handRaised, setHandRaised] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('stopped');\n  const localVideoRef = useRef(null);\n  const peersRef = useRef({});\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  const {\n    socket,\n    isConnected\n  } = useWebSocket();\n  const {\n    user\n  } = useAuth();\n\n  // Initialize video stream\n  useEffect(() => {\n    if (isVisible && !localStream) {\n      initializeMedia();\n    }\n    return () => {\n      if (localStream) {\n        localStream.getTracks().forEach(track => track.stop());\n      }\n      Object.values(peersRef.current).forEach(peer => {\n        if (peer.destroy) peer.destroy();\n      });\n    };\n  }, [isVisible]);\n\n  // Check if user is moderator\n  useEffect(() => {\n    if (debate && user) {\n      setIsModerator(debate.moderator_id === user.id);\n    }\n  }, [debate, user]);\n\n  // WebSocket event listeners\n  useEffect(() => {\n    if (!socket || !isVisible) return;\n    socket.on('video-signal', handleVideoSignal);\n    socket.on('user-joined-video', handleUserJoinedVideo);\n    socket.on('user-left-video', handleUserLeftVideo);\n    socket.on('speaking-queue-updated', setSpeakingQueue);\n    socket.on('current-speaker-changed', setCurrentSpeaker);\n    socket.on('hand-raised', handleHandRaised);\n    socket.on('hand-lowered', handleHandLowered);\n    socket.on('recording-status-changed', setRecordingStatus);\n    return () => {\n      socket.off('video-signal');\n      socket.off('user-joined-video');\n      socket.off('user-left-video');\n      socket.off('speaking-queue-updated');\n      socket.off('current-speaker-changed');\n      socket.off('hand-raised');\n      socket.off('hand-lowered');\n      socket.off('recording-status-changed');\n    };\n  }, [socket, isVisible]);\n  const initializeMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: 640,\n          height: 480\n        },\n        audio: true\n      });\n      setLocalStream(stream);\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n\n      // Join video room\n      socket.emit('join-video-debate', {\n        debateId,\n        userId: user.id\n      });\n    } catch (error) {\n      console.error('Error accessing media devices:', error);\n      alert('Unable to access camera/microphone. Please check permissions.');\n    }\n  };\n  const createPeer = useCallback((initiator, stream, socketId) => {\n    const peer = new Peer({\n      initiator,\n      trickle: false,\n      stream\n    });\n    peer.on('signal', signal => {\n      socket.emit('video-signal', {\n        signal,\n        to: socketId,\n        from: socket.id,\n        debateId\n      });\n    });\n    peer.on('stream', remoteStream => {\n      setPeers(prev => ({\n        ...prev,\n        [socketId]: {\n          ...prev[socketId],\n          stream: remoteStream\n        }\n      }));\n    });\n    peer.on('error', err => {\n      console.error('Peer error:', err);\n    });\n    return peer;\n  }, [socket, debateId]);\n  const handleVideoSignal = useCallback(data => {\n    if (data.to === socket.id) {\n      if (!peersRef.current[data.from]) {\n        const peer = createPeer(false, localStream, data.from);\n        peersRef.current[data.from] = peer;\n      }\n      peersRef.current[data.from].signal(data.signal);\n    }\n  }, [createPeer, localStream, socket]);\n  const handleUserJoinedVideo = useCallback(data => {\n    if (data.socketId !== socket.id && localStream) {\n      const peer = createPeer(true, localStream, data.socketId);\n      peersRef.current[data.socketId] = peer;\n      setPeers(prev => ({\n        ...prev,\n        [data.socketId]: {\n          userId: data.userId,\n          username: data.username,\n          peer,\n          stream: null\n        }\n      }));\n    }\n  }, [createPeer, localStream, socket]);\n  const handleUserLeftVideo = useCallback(data => {\n    if (peersRef.current[data.socketId]) {\n      peersRef.current[data.socketId].destroy();\n      delete peersRef.current[data.socketId];\n    }\n    setPeers(prev => {\n      const newPeers = {\n        ...prev\n      };\n      delete newPeers[data.socketId];\n      return newPeers;\n    });\n  }, []);\n  const toggleVideo = () => {\n    if (localStream) {\n      const videoTrack = localStream.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoEnabled(videoTrack.enabled);\n        socket.emit('video-toggle', {\n          debateId,\n          enabled: videoTrack.enabled\n        });\n      }\n    }\n  };\n  const toggleAudio = () => {\n    if (localStream) {\n      const audioTrack = localStream.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioEnabled(audioTrack.enabled);\n        socket.emit('audio-toggle', {\n          debateId,\n          enabled: audioTrack.enabled\n        });\n      }\n    }\n  };\n  const startScreenShare = async () => {\n    try {\n      const screenStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Replace video track in all peer connections\n      const videoTrack = screenStream.getVideoTracks()[0];\n      Object.values(peersRef.current).forEach(peer => {\n        const sender = peer._pc.getSenders().find(s => s.track && s.track.kind === 'video');\n        if (sender) {\n          sender.replaceTrack(videoTrack);\n        }\n      });\n\n      // Update local video\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = screenStream;\n      }\n      setIsScreenSharing(true);\n\n      // Handle screen share end\n      videoTrack.onended = () => {\n        stopScreenShare();\n      };\n      socket.emit('screen-share-started', {\n        debateId\n      });\n    } catch (error) {\n      console.error('Error starting screen share:', error);\n    }\n  };\n  const stopScreenShare = async () => {\n    try {\n      // Get camera stream back\n      const cameraStream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n\n      // Replace screen share track with camera track\n      const videoTrack = cameraStream.getVideoTracks()[0];\n      Object.values(peersRef.current).forEach(peer => {\n        const sender = peer._pc.getSenders().find(s => s.track && s.track.kind === 'video');\n        if (sender) {\n          sender.replaceTrack(videoTrack);\n        }\n      });\n\n      // Update local video\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = cameraStream;\n      }\n      setLocalStream(cameraStream);\n      setIsScreenSharing(false);\n      socket.emit('screen-share-stopped', {\n        debateId\n      });\n    } catch (error) {\n      console.error('Error stopping screen share:', error);\n    }\n  };\n  const raiseHand = () => {\n    setHandRaised(true);\n    socket.emit('raise-hand', {\n      debateId,\n      userId: user.id,\n      username: user.username\n    });\n  };\n  const lowerHand = () => {\n    setHandRaised(false);\n    socket.emit('lower-hand', {\n      debateId,\n      userId: user.id\n    });\n  };\n  const handleHandRaised = data => {\n    setSpeakingQueue(prev => [...prev, data]);\n  };\n  const handleHandLowered = data => {\n    setSpeakingQueue(prev => prev.filter(item => item.userId !== data.userId));\n  };\n  const giveFloorTo = userId => {\n    if (isModerator) {\n      socket.emit('give-floor', {\n        debateId,\n        userId\n      });\n      setSpeakingQueue(prev => prev.filter(item => item.userId !== userId));\n    }\n  };\n  const startRecording = () => {\n    if (isModerator && localStream) {\n      try {\n        const mediaRecorder = new MediaRecorder(localStream);\n        mediaRecorderRef.current = mediaRecorder;\n        recordedChunksRef.current = [];\n        mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            recordedChunksRef.current.push(event.data);\n          }\n        };\n        mediaRecorder.onstop = () => {\n          const blob = new Blob(recordedChunksRef.current, {\n            type: 'video/webm'\n          });\n          const url = URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `debate-${debateId}-${new Date().toISOString()}.webm`;\n          a.click();\n        };\n        mediaRecorder.start();\n        setRecordingStatus('recording');\n        socket.emit('recording-started', {\n          debateId\n        });\n      } catch (error) {\n        console.error('Error starting recording:', error);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && recordingStatus === 'recording') {\n      mediaRecorderRef.current.stop();\n      setRecordingStatus('stopped');\n      socket.emit('recording-stopped', {\n        debateId\n      });\n    }\n  };\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100vw',\n      height: '100vh',\n      backgroundColor: '#000',\n      zIndex: 2000,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#1f2937',\n        color: 'white',\n        padding: '1rem',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0,\n            fontSize: '1.25rem'\n          },\n          children: [\"\\uD83C\\uDFA5 \", (debate === null || debate === void 0 ? void 0 : debate.title) || 'Live Video Debate']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            opacity: 0.8,\n            marginTop: '0.25rem'\n          },\n          children: [Object.keys(peers).length + 1, \" participants \\u2022\", recordingStatus === 'recording' && ' 🔴 Recording • ', currentSpeaker && ` Speaking: ${currentSpeaker.username}`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        style: {\n          background: '#ef4444',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Leave Debate\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'grid',\n        gridTemplateColumns: Object.keys(peers).length > 3 ? 'repeat(3, 1fr)' : 'repeat(2, 1fr)',\n        gap: '1rem',\n        padding: '1rem',\n        overflow: 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          backgroundColor: '#374151',\n          borderRadius: '8px',\n          overflow: 'hidden',\n          border: (currentSpeaker === null || currentSpeaker === void 0 ? void 0 : currentSpeaker.userId) === user.id ? '3px solid #10b981' : 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          ref: localVideoRef,\n          autoPlay: true,\n          muted: true,\n          playsInline: true,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '0.5rem',\n            left: '0.5rem',\n            backgroundColor: 'rgba(0,0,0,0.7)',\n            color: 'white',\n            padding: '0.25rem 0.5rem',\n            borderRadius: '4px',\n            fontSize: '0.875rem'\n          },\n          children: [user === null || user === void 0 ? void 0 : user.username, \" (You) \", handRaised && '✋']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), !isVideoEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            color: 'white',\n            fontSize: '3rem'\n          },\n          children: \"\\uD83D\\uDCF9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), Object.entries(peers).map(([socketId, peerData]) => /*#__PURE__*/_jsxDEV(RemoteVideo, {\n        peerData: peerData,\n        currentSpeaker: currentSpeaker,\n        isModerator: isModerator,\n        onGiveFloor: giveFloorTo\n      }, socketId, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#1f2937',\n        padding: '1rem',\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '1rem',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleVideo,\n        style: {\n          backgroundColor: isVideoEnabled ? '#374151' : '#ef4444',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem',\n          borderRadius: '50%',\n          cursor: 'pointer',\n          fontSize: '1.25rem'\n        },\n        children: isVideoEnabled ? '📹' : '📹'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleAudio,\n        style: {\n          backgroundColor: isAudioEnabled ? '#374151' : '#ef4444',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem',\n          borderRadius: '50%',\n          cursor: 'pointer',\n          fontSize: '1.25rem'\n        },\n        children: isAudioEnabled ? '🎤' : '🔇'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: isScreenSharing ? stopScreenShare : startScreenShare,\n        style: {\n          backgroundColor: isScreenSharing ? '#10b981' : '#374151',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem',\n          borderRadius: '50%',\n          cursor: 'pointer',\n          fontSize: '1.25rem'\n        },\n        children: \"\\uD83D\\uDDA5\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handRaised ? lowerHand : raiseHand,\n        style: {\n          backgroundColor: handRaised ? '#f59e0b' : '#374151',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem',\n          borderRadius: '50%',\n          cursor: 'pointer',\n          fontSize: '1.25rem'\n        },\n        children: \"\\u270B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), isModerator && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: recordingStatus === 'recording' ? stopRecording : startRecording,\n        style: {\n          backgroundColor: recordingStatus === 'recording' ? '#ef4444' : '#374151',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem 1rem',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          fontSize: '0.875rem'\n        },\n        children: recordingStatus === 'recording' ? '⏹️ Stop Recording' : '🔴 Start Recording'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this), speakingQueue.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '5rem',\n        right: '1rem',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '1rem',\n        borderRadius: '8px',\n        minWidth: '200px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 0.5rem 0'\n        },\n        children: \"Speaking Queue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 11\n      }, this), speakingQueue.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '0.25rem 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [index + 1, \". \", item.username]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 15\n        }, this), isModerator && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => giveFloorTo(item.userId),\n          style: {\n            background: '#10b981',\n            color: 'white',\n            border: 'none',\n            padding: '0.25rem 0.5rem',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '0.75rem'\n          },\n          children: \"Give Floor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 17\n        }, this)]\n      }, item.userId, true, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 5\n  }, this);\n};\n\n// Remote Video Component\n_s(VideoDebate, \"GnHtmb9iG7GgM4E60Yi+1r6DzRM=\", false, function () {\n  return [useWebSocket, useAuth];\n});\n_c = VideoDebate;\nconst RemoteVideo = ({\n  peerData,\n  currentSpeaker,\n  isModerator,\n  onGiveFloor\n}) => {\n  _s2();\n  const videoRef = useRef(null);\n  useEffect(() => {\n    if (peerData.stream && videoRef.current) {\n      videoRef.current.srcObject = peerData.stream;\n    }\n  }, [peerData.stream]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      backgroundColor: '#374151',\n      borderRadius: '8px',\n      overflow: 'hidden',\n      border: (currentSpeaker === null || currentSpeaker === void 0 ? void 0 : currentSpeaker.userId) === peerData.userId ? '3px solid #10b981' : 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      ref: videoRef,\n      autoPlay: true,\n      playsInline: true,\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '0.5rem',\n        left: '0.5rem',\n        backgroundColor: 'rgba(0,0,0,0.7)',\n        color: 'white',\n        padding: '0.25rem 0.5rem',\n        borderRadius: '4px',\n        fontSize: '0.875rem'\n      },\n      children: peerData.username\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 589,\n    columnNumber: 5\n  }, this);\n};\n_s2(RemoteVideo, \"PdMsmLAy5JKU3vCrhAlqGYQfKuA=\");\n_c2 = RemoteVideo;\nexport default VideoDebate;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoDebate\");\n$RefreshReg$(_c2, \"RemoteVideo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "<PERSON><PERSON>", "useWebSocket", "useAuth", "jsxDEV", "_jsxDEV", "VideoDebate", "debateId", "isVisible", "onClose", "debate", "_s", "localStream", "setLocalStream", "peers", "setPeers", "isVideoEnabled", "setIsVideoEnabled", "isAudioEnabled", "setIsAudioEnabled", "isScreenSharing", "setIsScreenSharing", "isModerator", "setIsModerator", "speakingQueue", "setSpeakingQueue", "currentSpeaker", "setCurrentSpeaker", "handRaised", "setHandRaised", "recordingStatus", "setRecordingStatus", "localVideoRef", "peersRef", "mediaRecorderRef", "recordedChunksRef", "socket", "isConnected", "user", "initializeMedia", "getTracks", "for<PERSON>ach", "track", "stop", "Object", "values", "current", "peer", "destroy", "moderator_id", "id", "on", "handleVideoSignal", "handleUserJoinedVideo", "handleUserLeftVideo", "handleHandRaised", "handleHandLowered", "off", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "width", "height", "audio", "srcObject", "emit", "userId", "error", "console", "alert", "createPeer", "initiator", "socketId", "trickle", "signal", "to", "from", "remoteStream", "prev", "err", "data", "username", "newPeers", "toggleVideo", "videoTrack", "getVideoTracks", "enabled", "toggleAudio", "audioTrack", "getAudioTracks", "startScreenShare", "screenStream", "getDisplayMedia", "sender", "_pc", "getSenders", "find", "s", "kind", "replaceTrack", "onended", "stopScreenShare", "cameraStream", "raiseHand", "lowerHand", "filter", "item", "giveFloorTo", "startRecording", "mediaRecorder", "MediaRecorder", "ondataavailable", "event", "size", "push", "onstop", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "Date", "toISOString", "click", "start", "stopRecording", "style", "position", "top", "left", "backgroundColor", "zIndex", "display", "flexDirection", "children", "color", "padding", "justifyContent", "alignItems", "margin", "fontSize", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "opacity", "marginTop", "keys", "length", "onClick", "background", "border", "borderRadius", "cursor", "flex", "gridTemplateColumns", "gap", "overflow", "ref", "autoPlay", "muted", "playsInline", "objectFit", "bottom", "transform", "entries", "map", "peerData", "RemoteVideo", "onGiveFloor", "flexWrap", "right", "min<PERSON><PERSON><PERSON>", "index", "_c", "_s2", "videoRef", "_c2", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/components/VideoDebate.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport Peer from 'simple-peer';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst VideoDebate = ({ debateId, isVisible, onClose, debate }) => {\n  const [localStream, setLocalStream] = useState(null);\n  const [peers, setPeers] = useState({});\n  const [isVideoEnabled, setIsVideoEnabled] = useState(true);\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [isScreenSharing, setIsScreenSharing] = useState(false);\n  const [isModerator, setIsModerator] = useState(false);\n  const [speakingQueue, setSpeakingQueue] = useState([]);\n  const [currentSpeaker, setCurrentSpeaker] = useState(null);\n  const [handRaised, setHandRaised] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('stopped');\n  \n  const localVideoRef = useRef(null);\n  const peersRef = useRef({});\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  \n  const { socket, isConnected } = useWebSocket();\n  const { user } = useAuth();\n\n  // Initialize video stream\n  useEffect(() => {\n    if (isVisible && !localStream) {\n      initializeMedia();\n    }\n    \n    return () => {\n      if (localStream) {\n        localStream.getTracks().forEach(track => track.stop());\n      }\n      Object.values(peersRef.current).forEach(peer => {\n        if (peer.destroy) peer.destroy();\n      });\n    };\n  }, [isVisible]);\n\n  // Check if user is moderator\n  useEffect(() => {\n    if (debate && user) {\n      setIsModerator(debate.moderator_id === user.id);\n    }\n  }, [debate, user]);\n\n  // WebSocket event listeners\n  useEffect(() => {\n    if (!socket || !isVisible) return;\n\n    socket.on('video-signal', handleVideoSignal);\n    socket.on('user-joined-video', handleUserJoinedVideo);\n    socket.on('user-left-video', handleUserLeftVideo);\n    socket.on('speaking-queue-updated', setSpeakingQueue);\n    socket.on('current-speaker-changed', setCurrentSpeaker);\n    socket.on('hand-raised', handleHandRaised);\n    socket.on('hand-lowered', handleHandLowered);\n    socket.on('recording-status-changed', setRecordingStatus);\n\n    return () => {\n      socket.off('video-signal');\n      socket.off('user-joined-video');\n      socket.off('user-left-video');\n      socket.off('speaking-queue-updated');\n      socket.off('current-speaker-changed');\n      socket.off('hand-raised');\n      socket.off('hand-lowered');\n      socket.off('recording-status-changed');\n    };\n  }, [socket, isVisible]);\n\n  const initializeMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { width: 640, height: 480 },\n        audio: true\n      });\n      \n      setLocalStream(stream);\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      \n      // Join video room\n      socket.emit('join-video-debate', { debateId, userId: user.id });\n      \n    } catch (error) {\n      console.error('Error accessing media devices:', error);\n      alert('Unable to access camera/microphone. Please check permissions.');\n    }\n  };\n\n  const createPeer = useCallback((initiator, stream, socketId) => {\n    const peer = new Peer({\n      initiator,\n      trickle: false,\n      stream\n    });\n\n    peer.on('signal', signal => {\n      socket.emit('video-signal', {\n        signal,\n        to: socketId,\n        from: socket.id,\n        debateId\n      });\n    });\n\n    peer.on('stream', remoteStream => {\n      setPeers(prev => ({\n        ...prev,\n        [socketId]: { ...prev[socketId], stream: remoteStream }\n      }));\n    });\n\n    peer.on('error', err => {\n      console.error('Peer error:', err);\n    });\n\n    return peer;\n  }, [socket, debateId]);\n\n  const handleVideoSignal = useCallback((data) => {\n    if (data.to === socket.id) {\n      if (!peersRef.current[data.from]) {\n        const peer = createPeer(false, localStream, data.from);\n        peersRef.current[data.from] = peer;\n      }\n      peersRef.current[data.from].signal(data.signal);\n    }\n  }, [createPeer, localStream, socket]);\n\n  const handleUserJoinedVideo = useCallback((data) => {\n    if (data.socketId !== socket.id && localStream) {\n      const peer = createPeer(true, localStream, data.socketId);\n      peersRef.current[data.socketId] = peer;\n      \n      setPeers(prev => ({\n        ...prev,\n        [data.socketId]: { \n          userId: data.userId, \n          username: data.username, \n          peer,\n          stream: null \n        }\n      }));\n    }\n  }, [createPeer, localStream, socket]);\n\n  const handleUserLeftVideo = useCallback((data) => {\n    if (peersRef.current[data.socketId]) {\n      peersRef.current[data.socketId].destroy();\n      delete peersRef.current[data.socketId];\n    }\n    \n    setPeers(prev => {\n      const newPeers = { ...prev };\n      delete newPeers[data.socketId];\n      return newPeers;\n    });\n  }, []);\n\n  const toggleVideo = () => {\n    if (localStream) {\n      const videoTrack = localStream.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoEnabled(videoTrack.enabled);\n        \n        socket.emit('video-toggle', {\n          debateId,\n          enabled: videoTrack.enabled\n        });\n      }\n    }\n  };\n\n  const toggleAudio = () => {\n    if (localStream) {\n      const audioTrack = localStream.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioEnabled(audioTrack.enabled);\n        \n        socket.emit('audio-toggle', {\n          debateId,\n          enabled: audioTrack.enabled\n        });\n      }\n    }\n  };\n\n  const startScreenShare = async () => {\n    try {\n      const screenStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n      \n      // Replace video track in all peer connections\n      const videoTrack = screenStream.getVideoTracks()[0];\n      Object.values(peersRef.current).forEach(peer => {\n        const sender = peer._pc.getSenders().find(s => \n          s.track && s.track.kind === 'video'\n        );\n        if (sender) {\n          sender.replaceTrack(videoTrack);\n        }\n      });\n      \n      // Update local video\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = screenStream;\n      }\n      \n      setIsScreenSharing(true);\n      \n      // Handle screen share end\n      videoTrack.onended = () => {\n        stopScreenShare();\n      };\n      \n      socket.emit('screen-share-started', { debateId });\n      \n    } catch (error) {\n      console.error('Error starting screen share:', error);\n    }\n  };\n\n  const stopScreenShare = async () => {\n    try {\n      // Get camera stream back\n      const cameraStream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      \n      // Replace screen share track with camera track\n      const videoTrack = cameraStream.getVideoTracks()[0];\n      Object.values(peersRef.current).forEach(peer => {\n        const sender = peer._pc.getSenders().find(s => \n          s.track && s.track.kind === 'video'\n        );\n        if (sender) {\n          sender.replaceTrack(videoTrack);\n        }\n      });\n      \n      // Update local video\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = cameraStream;\n      }\n      \n      setLocalStream(cameraStream);\n      setIsScreenSharing(false);\n      \n      socket.emit('screen-share-stopped', { debateId });\n      \n    } catch (error) {\n      console.error('Error stopping screen share:', error);\n    }\n  };\n\n  const raiseHand = () => {\n    setHandRaised(true);\n    socket.emit('raise-hand', { debateId, userId: user.id, username: user.username });\n  };\n\n  const lowerHand = () => {\n    setHandRaised(false);\n    socket.emit('lower-hand', { debateId, userId: user.id });\n  };\n\n  const handleHandRaised = (data) => {\n    setSpeakingQueue(prev => [...prev, data]);\n  };\n\n  const handleHandLowered = (data) => {\n    setSpeakingQueue(prev => prev.filter(item => item.userId !== data.userId));\n  };\n\n  const giveFloorTo = (userId) => {\n    if (isModerator) {\n      socket.emit('give-floor', { debateId, userId });\n      setSpeakingQueue(prev => prev.filter(item => item.userId !== userId));\n    }\n  };\n\n  const startRecording = () => {\n    if (isModerator && localStream) {\n      try {\n        const mediaRecorder = new MediaRecorder(localStream);\n        mediaRecorderRef.current = mediaRecorder;\n        recordedChunksRef.current = [];\n        \n        mediaRecorder.ondataavailable = (event) => {\n          if (event.data.size > 0) {\n            recordedChunksRef.current.push(event.data);\n          }\n        };\n        \n        mediaRecorder.onstop = () => {\n          const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' });\n          const url = URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `debate-${debateId}-${new Date().toISOString()}.webm`;\n          a.click();\n        };\n        \n        mediaRecorder.start();\n        setRecordingStatus('recording');\n        socket.emit('recording-started', { debateId });\n        \n      } catch (error) {\n        console.error('Error starting recording:', error);\n      }\n    }\n  };\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && recordingStatus === 'recording') {\n      mediaRecorderRef.current.stop();\n      setRecordingStatus('stopped');\n      socket.emit('recording-stopped', { debateId });\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100vw',\n      height: '100vh',\n      backgroundColor: '#000',\n      zIndex: 2000,\n      display: 'flex',\n      flexDirection: 'column'\n    }}>\n      {/* Header */}\n      <div style={{\n        backgroundColor: '#1f2937',\n        color: 'white',\n        padding: '1rem',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      }}>\n        <div>\n          <h2 style={{ margin: 0, fontSize: '1.25rem' }}>\n            🎥 {debate?.title || 'Live Video Debate'}\n          </h2>\n          <div style={{ fontSize: '0.875rem', opacity: 0.8, marginTop: '0.25rem' }}>\n            {Object.keys(peers).length + 1} participants • \n            {recordingStatus === 'recording' && ' 🔴 Recording • '}\n            {currentSpeaker && ` Speaking: ${currentSpeaker.username}`}\n          </div>\n        </div>\n        \n        <button\n          onClick={onClose}\n          style={{\n            background: '#ef4444',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Leave Debate\n        </button>\n      </div>\n\n      {/* Video Grid */}\n      <div style={{\n        flex: 1,\n        display: 'grid',\n        gridTemplateColumns: Object.keys(peers).length > 3 ? 'repeat(3, 1fr)' : 'repeat(2, 1fr)',\n        gap: '1rem',\n        padding: '1rem',\n        overflow: 'auto'\n      }}>\n        {/* Local Video */}\n        <div style={{\n          position: 'relative',\n          backgroundColor: '#374151',\n          borderRadius: '8px',\n          overflow: 'hidden',\n          border: currentSpeaker?.userId === user.id ? '3px solid #10b981' : 'none'\n        }}>\n          <video\n            ref={localVideoRef}\n            autoPlay\n            muted\n            playsInline\n            style={{\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover'\n            }}\n          />\n          <div style={{\n            position: 'absolute',\n            bottom: '0.5rem',\n            left: '0.5rem',\n            backgroundColor: 'rgba(0,0,0,0.7)',\n            color: 'white',\n            padding: '0.25rem 0.5rem',\n            borderRadius: '4px',\n            fontSize: '0.875rem'\n          }}>\n            {user?.username} (You) {handRaised && '✋'}\n          </div>\n          {!isVideoEnabled && (\n            <div style={{\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              color: 'white',\n              fontSize: '3rem'\n            }}>\n              📹\n            </div>\n          )}\n        </div>\n\n        {/* Remote Videos */}\n        {Object.entries(peers).map(([socketId, peerData]) => (\n          <RemoteVideo\n            key={socketId}\n            peerData={peerData}\n            currentSpeaker={currentSpeaker}\n            isModerator={isModerator}\n            onGiveFloor={giveFloorTo}\n          />\n        ))}\n      </div>\n\n      {/* Controls */}\n      <div style={{\n        backgroundColor: '#1f2937',\n        padding: '1rem',\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '1rem',\n        flexWrap: 'wrap'\n      }}>\n        <button\n          onClick={toggleVideo}\n          style={{\n            backgroundColor: isVideoEnabled ? '#374151' : '#ef4444',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem',\n            borderRadius: '50%',\n            cursor: 'pointer',\n            fontSize: '1.25rem'\n          }}\n        >\n          {isVideoEnabled ? '📹' : '📹'}\n        </button>\n\n        <button\n          onClick={toggleAudio}\n          style={{\n            backgroundColor: isAudioEnabled ? '#374151' : '#ef4444',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem',\n            borderRadius: '50%',\n            cursor: 'pointer',\n            fontSize: '1.25rem'\n          }}\n        >\n          {isAudioEnabled ? '🎤' : '🔇'}\n        </button>\n\n        <button\n          onClick={isScreenSharing ? stopScreenShare : startScreenShare}\n          style={{\n            backgroundColor: isScreenSharing ? '#10b981' : '#374151',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem',\n            borderRadius: '50%',\n            cursor: 'pointer',\n            fontSize: '1.25rem'\n          }}\n        >\n          🖥️\n        </button>\n\n        <button\n          onClick={handRaised ? lowerHand : raiseHand}\n          style={{\n            backgroundColor: handRaised ? '#f59e0b' : '#374151',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem',\n            borderRadius: '50%',\n            cursor: 'pointer',\n            fontSize: '1.25rem'\n          }}\n        >\n          ✋\n        </button>\n\n        {isModerator && (\n          <button\n            onClick={recordingStatus === 'recording' ? stopRecording : startRecording}\n            style={{\n              backgroundColor: recordingStatus === 'recording' ? '#ef4444' : '#374151',\n              color: 'white',\n              border: 'none',\n              padding: '0.75rem 1rem',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '0.875rem'\n            }}\n          >\n            {recordingStatus === 'recording' ? '⏹️ Stop Recording' : '🔴 Start Recording'}\n          </button>\n        )}\n      </div>\n\n      {/* Speaking Queue */}\n      {speakingQueue.length > 0 && (\n        <div style={{\n          position: 'absolute',\n          top: '5rem',\n          right: '1rem',\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          color: 'white',\n          padding: '1rem',\n          borderRadius: '8px',\n          minWidth: '200px'\n        }}>\n          <h4 style={{ margin: '0 0 0.5rem 0' }}>Speaking Queue</h4>\n          {speakingQueue.map((item, index) => (\n            <div key={item.userId} style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '0.25rem 0'\n            }}>\n              <span>{index + 1}. {item.username}</span>\n              {isModerator && (\n                <button\n                  onClick={() => giveFloorTo(item.userId)}\n                  style={{\n                    background: '#10b981',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    cursor: 'pointer',\n                    fontSize: '0.75rem'\n                  }}\n                >\n                  Give Floor\n                </button>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Remote Video Component\nconst RemoteVideo = ({ peerData, currentSpeaker, isModerator, onGiveFloor }) => {\n  const videoRef = useRef(null);\n\n  useEffect(() => {\n    if (peerData.stream && videoRef.current) {\n      videoRef.current.srcObject = peerData.stream;\n    }\n  }, [peerData.stream]);\n\n  return (\n    <div style={{\n      position: 'relative',\n      backgroundColor: '#374151',\n      borderRadius: '8px',\n      overflow: 'hidden',\n      border: currentSpeaker?.userId === peerData.userId ? '3px solid #10b981' : 'none'\n    }}>\n      <video\n        ref={videoRef}\n        autoPlay\n        playsInline\n        style={{\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        }}\n      />\n      <div style={{\n        position: 'absolute',\n        bottom: '0.5rem',\n        left: '0.5rem',\n        backgroundColor: 'rgba(0,0,0,0.7)',\n        color: 'white',\n        padding: '0.25rem 0.5rem',\n        borderRadius: '4px',\n        fontSize: '0.875rem'\n      }}>\n        {peerData.username}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoDebate;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EAEjE,MAAMmC,aAAa,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkC,QAAQ,GAAGlC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,MAAMmC,gBAAgB,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMoC,iBAAiB,GAAGpC,MAAM,CAAC,EAAE,CAAC;EAEpC,MAAM;IAAEqC,MAAM;IAAEC;EAAY,CAAC,GAAGnC,YAAY,CAAC,CAAC;EAC9C,MAAM;IAAEoC;EAAK,CAAC,GAAGnC,OAAO,CAAC,CAAC;;EAE1B;EACAL,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,IAAI,CAACI,WAAW,EAAE;MAC7B2B,eAAe,CAAC,CAAC;IACnB;IAEA,OAAO,MAAM;MACX,IAAI3B,WAAW,EAAE;QACfA,WAAW,CAAC4B,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACxD;MACAC,MAAM,CAACC,MAAM,CAACZ,QAAQ,CAACa,OAAO,CAAC,CAACL,OAAO,CAACM,IAAI,IAAI;QAC9C,IAAIA,IAAI,CAACC,OAAO,EAAED,IAAI,CAACC,OAAO,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACxC,SAAS,CAAC,CAAC;;EAEf;EACAV,SAAS,CAAC,MAAM;IACd,IAAIY,MAAM,IAAI4B,IAAI,EAAE;MAClBf,cAAc,CAACb,MAAM,CAACuC,YAAY,KAAKX,IAAI,CAACY,EAAE,CAAC;IACjD;EACF,CAAC,EAAE,CAACxC,MAAM,EAAE4B,IAAI,CAAC,CAAC;;EAElB;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,MAAM,IAAI,CAAC5B,SAAS,EAAE;IAE3B4B,MAAM,CAACe,EAAE,CAAC,cAAc,EAAEC,iBAAiB,CAAC;IAC5ChB,MAAM,CAACe,EAAE,CAAC,mBAAmB,EAAEE,qBAAqB,CAAC;IACrDjB,MAAM,CAACe,EAAE,CAAC,iBAAiB,EAAEG,mBAAmB,CAAC;IACjDlB,MAAM,CAACe,EAAE,CAAC,wBAAwB,EAAE1B,gBAAgB,CAAC;IACrDW,MAAM,CAACe,EAAE,CAAC,yBAAyB,EAAExB,iBAAiB,CAAC;IACvDS,MAAM,CAACe,EAAE,CAAC,aAAa,EAAEI,gBAAgB,CAAC;IAC1CnB,MAAM,CAACe,EAAE,CAAC,cAAc,EAAEK,iBAAiB,CAAC;IAC5CpB,MAAM,CAACe,EAAE,CAAC,0BAA0B,EAAEpB,kBAAkB,CAAC;IAEzD,OAAO,MAAM;MACXK,MAAM,CAACqB,GAAG,CAAC,cAAc,CAAC;MAC1BrB,MAAM,CAACqB,GAAG,CAAC,mBAAmB,CAAC;MAC/BrB,MAAM,CAACqB,GAAG,CAAC,iBAAiB,CAAC;MAC7BrB,MAAM,CAACqB,GAAG,CAAC,wBAAwB,CAAC;MACpCrB,MAAM,CAACqB,GAAG,CAAC,yBAAyB,CAAC;MACrCrB,MAAM,CAACqB,GAAG,CAAC,aAAa,CAAC;MACzBrB,MAAM,CAACqB,GAAG,CAAC,cAAc,CAAC;MAC1BrB,MAAM,CAACqB,GAAG,CAAC,0BAA0B,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,CAACrB,MAAM,EAAE5B,SAAS,CAAC,CAAC;EAEvB,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMmB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAC;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFpD,cAAc,CAAC6C,MAAM,CAAC;MACtB,IAAI1B,aAAa,CAACc,OAAO,EAAE;QACzBd,aAAa,CAACc,OAAO,CAACoB,SAAS,GAAGR,MAAM;MAC1C;;MAEA;MACAtB,MAAM,CAAC+B,IAAI,CAAC,mBAAmB,EAAE;QAAE5D,QAAQ;QAAE6D,MAAM,EAAE9B,IAAI,CAACY;MAAG,CAAC,CAAC;IAEjE,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDE,KAAK,CAAC,+DAA+D,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,UAAU,GAAGxE,WAAW,CAAC,CAACyE,SAAS,EAAEf,MAAM,EAAEgB,QAAQ,KAAK;IAC9D,MAAM3B,IAAI,GAAG,IAAI9C,IAAI,CAAC;MACpBwE,SAAS;MACTE,OAAO,EAAE,KAAK;MACdjB;IACF,CAAC,CAAC;IAEFX,IAAI,CAACI,EAAE,CAAC,QAAQ,EAAEyB,MAAM,IAAI;MAC1BxC,MAAM,CAAC+B,IAAI,CAAC,cAAc,EAAE;QAC1BS,MAAM;QACNC,EAAE,EAAEH,QAAQ;QACZI,IAAI,EAAE1C,MAAM,CAACc,EAAE;QACf3C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFwC,IAAI,CAACI,EAAE,CAAC,QAAQ,EAAE4B,YAAY,IAAI;MAChChE,QAAQ,CAACiE,IAAI,KAAK;QAChB,GAAGA,IAAI;QACP,CAACN,QAAQ,GAAG;UAAE,GAAGM,IAAI,CAACN,QAAQ,CAAC;UAAEhB,MAAM,EAAEqB;QAAa;MACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEFhC,IAAI,CAACI,EAAE,CAAC,OAAO,EAAE8B,GAAG,IAAI;MACtBX,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEY,GAAG,CAAC;IACnC,CAAC,CAAC;IAEF,OAAOlC,IAAI;EACb,CAAC,EAAE,CAACX,MAAM,EAAE7B,QAAQ,CAAC,CAAC;EAEtB,MAAM6C,iBAAiB,GAAGpD,WAAW,CAAEkF,IAAI,IAAK;IAC9C,IAAIA,IAAI,CAACL,EAAE,KAAKzC,MAAM,CAACc,EAAE,EAAE;MACzB,IAAI,CAACjB,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACJ,IAAI,CAAC,EAAE;QAChC,MAAM/B,IAAI,GAAGyB,UAAU,CAAC,KAAK,EAAE5D,WAAW,EAAEsE,IAAI,CAACJ,IAAI,CAAC;QACtD7C,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACJ,IAAI,CAAC,GAAG/B,IAAI;MACpC;MACAd,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACJ,IAAI,CAAC,CAACF,MAAM,CAACM,IAAI,CAACN,MAAM,CAAC;IACjD;EACF,CAAC,EAAE,CAACJ,UAAU,EAAE5D,WAAW,EAAEwB,MAAM,CAAC,CAAC;EAErC,MAAMiB,qBAAqB,GAAGrD,WAAW,CAAEkF,IAAI,IAAK;IAClD,IAAIA,IAAI,CAACR,QAAQ,KAAKtC,MAAM,CAACc,EAAE,IAAItC,WAAW,EAAE;MAC9C,MAAMmC,IAAI,GAAGyB,UAAU,CAAC,IAAI,EAAE5D,WAAW,EAAEsE,IAAI,CAACR,QAAQ,CAAC;MACzDzC,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACR,QAAQ,CAAC,GAAG3B,IAAI;MAEtChC,QAAQ,CAACiE,IAAI,KAAK;QAChB,GAAGA,IAAI;QACP,CAACE,IAAI,CAACR,QAAQ,GAAG;UACfN,MAAM,EAAEc,IAAI,CAACd,MAAM;UACnBe,QAAQ,EAAED,IAAI,CAACC,QAAQ;UACvBpC,IAAI;UACJW,MAAM,EAAE;QACV;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACc,UAAU,EAAE5D,WAAW,EAAEwB,MAAM,CAAC,CAAC;EAErC,MAAMkB,mBAAmB,GAAGtD,WAAW,CAAEkF,IAAI,IAAK;IAChD,IAAIjD,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACR,QAAQ,CAAC,EAAE;MACnCzC,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACR,QAAQ,CAAC,CAAC1B,OAAO,CAAC,CAAC;MACzC,OAAOf,QAAQ,CAACa,OAAO,CAACoC,IAAI,CAACR,QAAQ,CAAC;IACxC;IAEA3D,QAAQ,CAACiE,IAAI,IAAI;MACf,MAAMI,QAAQ,GAAG;QAAE,GAAGJ;MAAK,CAAC;MAC5B,OAAOI,QAAQ,CAACF,IAAI,CAACR,QAAQ,CAAC;MAC9B,OAAOU,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIzE,WAAW,EAAE;MACf,MAAM0E,UAAU,GAAG1E,WAAW,CAAC2E,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;QACxCvE,iBAAiB,CAACqE,UAAU,CAACE,OAAO,CAAC;QAErCpD,MAAM,CAAC+B,IAAI,CAAC,cAAc,EAAE;UAC1B5D,QAAQ;UACRiF,OAAO,EAAEF,UAAU,CAACE;QACtB,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI7E,WAAW,EAAE;MACf,MAAM8E,UAAU,GAAG9E,WAAW,CAAC+E,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,IAAID,UAAU,EAAE;QACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;QACxCrE,iBAAiB,CAACuE,UAAU,CAACF,OAAO,CAAC;QAErCpD,MAAM,CAAC+B,IAAI,CAAC,cAAc,EAAE;UAC1B5D,QAAQ;UACRiF,OAAO,EAAEE,UAAU,CAACF;QACtB,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,YAAY,GAAG,MAAMlC,SAAS,CAACC,YAAY,CAACkC,eAAe,CAAC;QAChEhC,KAAK,EAAE,IAAI;QACXG,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMqB,UAAU,GAAGO,YAAY,CAACN,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD3C,MAAM,CAACC,MAAM,CAACZ,QAAQ,CAACa,OAAO,CAAC,CAACL,OAAO,CAACM,IAAI,IAAI;QAC9C,MAAMgD,MAAM,GAAGhD,IAAI,CAACiD,GAAG,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAACC,CAAC,IACzCA,CAAC,CAACzD,KAAK,IAAIyD,CAAC,CAACzD,KAAK,CAAC0D,IAAI,KAAK,OAC9B,CAAC;QACD,IAAIL,MAAM,EAAE;UACVA,MAAM,CAACM,YAAY,CAACf,UAAU,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,IAAItD,aAAa,CAACc,OAAO,EAAE;QACzBd,aAAa,CAACc,OAAO,CAACoB,SAAS,GAAG2B,YAAY;MAChD;MAEAxE,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAiE,UAAU,CAACgB,OAAO,GAAG,MAAM;QACzBC,eAAe,CAAC,CAAC;MACnB,CAAC;MAEDnE,MAAM,CAAC+B,IAAI,CAAC,sBAAsB,EAAE;QAAE5D;MAAS,CAAC,CAAC;IAEnD,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMkC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,MAAM7C,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAC7DC,KAAK,EAAE,IAAI;QACXG,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMqB,UAAU,GAAGkB,YAAY,CAACjB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD3C,MAAM,CAACC,MAAM,CAACZ,QAAQ,CAACa,OAAO,CAAC,CAACL,OAAO,CAACM,IAAI,IAAI;QAC9C,MAAMgD,MAAM,GAAGhD,IAAI,CAACiD,GAAG,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAACC,CAAC,IACzCA,CAAC,CAACzD,KAAK,IAAIyD,CAAC,CAACzD,KAAK,CAAC0D,IAAI,KAAK,OAC9B,CAAC;QACD,IAAIL,MAAM,EAAE;UACVA,MAAM,CAACM,YAAY,CAACf,UAAU,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,IAAItD,aAAa,CAACc,OAAO,EAAE;QACzBd,aAAa,CAACc,OAAO,CAACoB,SAAS,GAAGsC,YAAY;MAChD;MAEA3F,cAAc,CAAC2F,YAAY,CAAC;MAC5BnF,kBAAkB,CAAC,KAAK,CAAC;MAEzBe,MAAM,CAAC+B,IAAI,CAAC,sBAAsB,EAAE;QAAE5D;MAAS,CAAC,CAAC;IAEnD,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMoC,SAAS,GAAGA,CAAA,KAAM;IACtB5E,aAAa,CAAC,IAAI,CAAC;IACnBO,MAAM,CAAC+B,IAAI,CAAC,YAAY,EAAE;MAAE5D,QAAQ;MAAE6D,MAAM,EAAE9B,IAAI,CAACY,EAAE;MAAEiC,QAAQ,EAAE7C,IAAI,CAAC6C;IAAS,CAAC,CAAC;EACnF,CAAC;EAED,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtB7E,aAAa,CAAC,KAAK,CAAC;IACpBO,MAAM,CAAC+B,IAAI,CAAC,YAAY,EAAE;MAAE5D,QAAQ;MAAE6D,MAAM,EAAE9B,IAAI,CAACY;IAAG,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMK,gBAAgB,GAAI2B,IAAI,IAAK;IACjCzD,gBAAgB,CAACuD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,IAAI,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM1B,iBAAiB,GAAI0B,IAAI,IAAK;IAClCzD,gBAAgB,CAACuD,IAAI,IAAIA,IAAI,CAAC2B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxC,MAAM,KAAKc,IAAI,CAACd,MAAM,CAAC,CAAC;EAC5E,CAAC;EAED,MAAMyC,WAAW,GAAIzC,MAAM,IAAK;IAC9B,IAAI9C,WAAW,EAAE;MACfc,MAAM,CAAC+B,IAAI,CAAC,YAAY,EAAE;QAAE5D,QAAQ;QAAE6D;MAAO,CAAC,CAAC;MAC/C3C,gBAAgB,CAACuD,IAAI,IAAIA,IAAI,CAAC2B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxC,MAAM,KAAKA,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;EAED,MAAM0C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxF,WAAW,IAAIV,WAAW,EAAE;MAC9B,IAAI;QACF,MAAMmG,aAAa,GAAG,IAAIC,aAAa,CAACpG,WAAW,CAAC;QACpDsB,gBAAgB,CAACY,OAAO,GAAGiE,aAAa;QACxC5E,iBAAiB,CAACW,OAAO,GAAG,EAAE;QAE9BiE,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;UACzC,IAAIA,KAAK,CAAChC,IAAI,CAACiC,IAAI,GAAG,CAAC,EAAE;YACvBhF,iBAAiB,CAACW,OAAO,CAACsE,IAAI,CAACF,KAAK,CAAChC,IAAI,CAAC;UAC5C;QACF,CAAC;QAED6B,aAAa,CAACM,MAAM,GAAG,MAAM;UAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACpF,iBAAiB,CAACW,OAAO,EAAE;YAAE0E,IAAI,EAAE;UAAa,CAAC,CAAC;UACxE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;UACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;UACZG,CAAC,CAACI,QAAQ,GAAG,UAAUzH,QAAQ,IAAI,IAAI0H,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,OAAO;UAClEN,CAAC,CAACO,KAAK,CAAC,CAAC;QACX,CAAC;QAEDpB,aAAa,CAACqB,KAAK,CAAC,CAAC;QACrBrG,kBAAkB,CAAC,WAAW,CAAC;QAC/BK,MAAM,CAAC+B,IAAI,CAAC,mBAAmB,EAAE;UAAE5D;QAAS,CAAC,CAAC;MAEhD,CAAC,CAAC,OAAO8D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC;EAED,MAAMgE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAInG,gBAAgB,CAACY,OAAO,IAAIhB,eAAe,KAAK,WAAW,EAAE;MAC/DI,gBAAgB,CAACY,OAAO,CAACH,IAAI,CAAC,CAAC;MAC/BZ,kBAAkB,CAAC,SAAS,CAAC;MAC7BK,MAAM,CAAC+B,IAAI,CAAC,mBAAmB,EAAE;QAAE5D;MAAS,CAAC,CAAC;IAChD;EACF,CAAC;EAED,IAAI,CAACC,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEH,OAAA;IAAKiI,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACP1E,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,OAAO;MACf0E,eAAe,EAAE,MAAM;MACvBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEAzI,OAAA;MAAKiI,KAAK,EAAE;QACVI,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfJ,OAAO,EAAE,MAAM;QACfK,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE;MACd,CAAE;MAAAJ,QAAA,gBACAzI,OAAA;QAAAyI,QAAA,gBACEzI,OAAA;UAAIiI,KAAK,EAAE;YAAEa,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAU,CAAE;UAAAN,QAAA,GAAC,eAC1C,EAAC,CAAApI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2I,KAAK,KAAI,mBAAmB;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACLpJ,OAAA;UAAKiI,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,OAAO,EAAE,GAAG;YAAEC,SAAS,EAAE;UAAU,CAAE;UAAAb,QAAA,GACtElG,MAAM,CAACgH,IAAI,CAAC9I,KAAK,CAAC,CAAC+I,MAAM,GAAG,CAAC,EAAC,sBAC/B,EAAC/H,eAAe,KAAK,WAAW,IAAI,kBAAkB,EACrDJ,cAAc,IAAI,cAAcA,cAAc,CAACyD,QAAQ,EAAE;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpJ,OAAA;QACEyJ,OAAO,EAAErJ,OAAQ;QACjB6H,KAAK,EAAE;UACLyB,UAAU,EAAE,SAAS;UACrBhB,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,aAAa;UACtBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAApB,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpJ,OAAA;MAAKiI,KAAK,EAAE;QACV6B,IAAI,EAAE,CAAC;QACPvB,OAAO,EAAE,MAAM;QACfwB,mBAAmB,EAAExH,MAAM,CAACgH,IAAI,CAAC9I,KAAK,CAAC,CAAC+I,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,gBAAgB;QACxFQ,GAAG,EAAE,MAAM;QACXrB,OAAO,EAAE,MAAM;QACfsB,QAAQ,EAAE;MACZ,CAAE;MAAAxB,QAAA,gBAEAzI,OAAA;QAAKiI,KAAK,EAAE;UACVC,QAAQ,EAAE,UAAU;UACpBG,eAAe,EAAE,SAAS;UAC1BuB,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,QAAQ;UAClBN,MAAM,EAAE,CAAAtI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0C,MAAM,MAAK9B,IAAI,CAACY,EAAE,GAAG,mBAAmB,GAAG;QACrE,CAAE;QAAA4F,QAAA,gBACAzI,OAAA;UACEkK,GAAG,EAAEvI,aAAc;UACnBwI,QAAQ;UACRC,KAAK;UACLC,WAAW;UACXpC,KAAK,EAAE;YACLvE,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACd2G,SAAS,EAAE;UACb;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpJ,OAAA;UAAKiI,KAAK,EAAE;YACVC,QAAQ,EAAE,UAAU;YACpBqC,MAAM,EAAE,QAAQ;YAChBnC,IAAI,EAAE,QAAQ;YACdC,eAAe,EAAE,iBAAiB;YAClCK,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,gBAAgB;YACzBiB,YAAY,EAAE,KAAK;YACnBb,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,GACCxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,QAAQ,EAAC,SAAO,EAACvD,UAAU,IAAI,GAAG;QAAA;UAAA0H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,EACL,CAACzI,cAAc,iBACdX,OAAA;UAAKiI,KAAK,EAAE;YACVC,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,KAAK;YACXoC,SAAS,EAAE,uBAAuB;YAClC9B,KAAK,EAAE,OAAO;YACdK,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL7G,MAAM,CAACkI,OAAO,CAAChK,KAAK,CAAC,CAACiK,GAAG,CAAC,CAAC,CAACrG,QAAQ,EAAEsG,QAAQ,CAAC,kBAC9C3K,OAAA,CAAC4K,WAAW;QAEVD,QAAQ,EAAEA,QAAS;QACnBtJ,cAAc,EAAEA,cAAe;QAC/BJ,WAAW,EAAEA,WAAY;QACzB4J,WAAW,EAAErE;MAAY,GAJpBnC,QAAQ;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKd,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpJ,OAAA;MAAKiI,KAAK,EAAE;QACVI,eAAe,EAAE,SAAS;QAC1BM,OAAO,EAAE,MAAM;QACfJ,OAAO,EAAE,MAAM;QACfK,cAAc,EAAE,QAAQ;QACxBoB,GAAG,EAAE,MAAM;QACXc,QAAQ,EAAE;MACZ,CAAE;MAAArC,QAAA,gBACAzI,OAAA;QACEyJ,OAAO,EAAEzE,WAAY;QACrBiD,KAAK,EAAE;UACLI,eAAe,EAAE1H,cAAc,GAAG,SAAS,GAAG,SAAS;UACvD+H,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,SAAS;UAClBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBd,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EAED9H,cAAc,GAAG,IAAI,GAAG;MAAI;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAETpJ,OAAA;QACEyJ,OAAO,EAAErE,WAAY;QACrB6C,KAAK,EAAE;UACLI,eAAe,EAAExH,cAAc,GAAG,SAAS,GAAG,SAAS;UACvD6H,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,SAAS;UAClBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBd,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EAED5H,cAAc,GAAG,IAAI,GAAG;MAAI;QAAAoI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAETpJ,OAAA;QACEyJ,OAAO,EAAE1I,eAAe,GAAGmF,eAAe,GAAGX,gBAAiB;QAC9D0C,KAAK,EAAE;UACLI,eAAe,EAAEtH,eAAe,GAAG,SAAS,GAAG,SAAS;UACxD2H,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,SAAS;UAClBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBd,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpJ,OAAA;QACEyJ,OAAO,EAAElI,UAAU,GAAG8E,SAAS,GAAGD,SAAU;QAC5C6B,KAAK,EAAE;UACLI,eAAe,EAAE9G,UAAU,GAAG,SAAS,GAAG,SAAS;UACnDmH,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,SAAS;UAClBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBd,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERnI,WAAW,iBACVjB,OAAA;QACEyJ,OAAO,EAAEhI,eAAe,KAAK,WAAW,GAAGuG,aAAa,GAAGvB,cAAe;QAC1EwB,KAAK,EAAE;UACLI,eAAe,EAAE5G,eAAe,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;UACxEiH,KAAK,EAAE,OAAO;UACdiB,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE,cAAc;UACvBiB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBd,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EAEDhH,eAAe,KAAK,WAAW,GAAG,mBAAmB,GAAG;MAAoB;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjI,aAAa,CAACqI,MAAM,GAAG,CAAC,iBACvBxJ,OAAA;MAAKiI,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACX4C,KAAK,EAAE,MAAM;QACb1C,eAAe,EAAE,iBAAiB;QAClCK,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfiB,YAAY,EAAE,KAAK;QACnBoB,QAAQ,EAAE;MACZ,CAAE;MAAAvC,QAAA,gBACAzI,OAAA;QAAIiI,KAAK,EAAE;UAAEa,MAAM,EAAE;QAAe,CAAE;QAAAL,QAAA,EAAC;MAAc;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACzDjI,aAAa,CAACuJ,GAAG,CAAC,CAACnE,IAAI,EAAE0E,KAAK,kBAC7BjL,OAAA;QAAuBiI,KAAK,EAAE;UAC5BM,OAAO,EAAE,MAAM;UACfK,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBF,OAAO,EAAE;QACX,CAAE;QAAAF,QAAA,gBACAzI,OAAA;UAAAyI,QAAA,GAAOwC,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC1E,IAAI,CAACzB,QAAQ;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxCnI,WAAW,iBACVjB,OAAA;UACEyJ,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAACD,IAAI,CAACxC,MAAM,CAAE;UACxCkE,KAAK,EAAE;YACLyB,UAAU,EAAE,SAAS;YACrBhB,KAAK,EAAE,OAAO;YACdiB,MAAM,EAAE,MAAM;YACdhB,OAAO,EAAE,gBAAgB;YACzBiB,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBd,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA,GAtBO7C,IAAI,CAACxC,MAAM;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBhB,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA9I,EAAA,CA5jBML,WAAW;EAAA,QAiBiBJ,YAAY,EAC3BC,OAAO;AAAA;AAAAoL,EAAA,GAlBpBjL,WAAW;AA6jBjB,MAAM2K,WAAW,GAAGA,CAAC;EAAED,QAAQ;EAAEtJ,cAAc;EAAEJ,WAAW;EAAE4J;AAAY,CAAC,KAAK;EAAAM,GAAA;EAC9E,MAAMC,QAAQ,GAAG1L,MAAM,CAAC,IAAI,CAAC;EAE7BD,SAAS,CAAC,MAAM;IACd,IAAIkL,QAAQ,CAACtH,MAAM,IAAI+H,QAAQ,CAAC3I,OAAO,EAAE;MACvC2I,QAAQ,CAAC3I,OAAO,CAACoB,SAAS,GAAG8G,QAAQ,CAACtH,MAAM;IAC9C;EACF,CAAC,EAAE,CAACsH,QAAQ,CAACtH,MAAM,CAAC,CAAC;EAErB,oBACErD,OAAA;IAAKiI,KAAK,EAAE;MACVC,QAAQ,EAAE,UAAU;MACpBG,eAAe,EAAE,SAAS;MAC1BuB,YAAY,EAAE,KAAK;MACnBK,QAAQ,EAAE,QAAQ;MAClBN,MAAM,EAAE,CAAAtI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0C,MAAM,MAAK4G,QAAQ,CAAC5G,MAAM,GAAG,mBAAmB,GAAG;IAC7E,CAAE;IAAA0E,QAAA,gBACAzI,OAAA;MACEkK,GAAG,EAAEkB,QAAS;MACdjB,QAAQ;MACRE,WAAW;MACXpC,KAAK,EAAE;QACLvE,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACd2G,SAAS,EAAE;MACb;IAAE;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFpJ,OAAA;MAAKiI,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBqC,MAAM,EAAE,QAAQ;QAChBnC,IAAI,EAAE,QAAQ;QACdC,eAAe,EAAE,iBAAiB;QAClCK,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,gBAAgB;QACzBiB,YAAY,EAAE,KAAK;QACnBb,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACCkC,QAAQ,CAAC7F;IAAQ;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+B,GAAA,CAzCIP,WAAW;AAAAS,GAAA,GAAXT,WAAW;AA2CjB,eAAe3K,WAAW;AAAC,IAAAiL,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}