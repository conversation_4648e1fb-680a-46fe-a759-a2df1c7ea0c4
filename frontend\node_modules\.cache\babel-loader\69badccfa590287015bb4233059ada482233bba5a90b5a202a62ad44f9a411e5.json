{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\LiveDebates.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport DebateChat from '../components/DebateChat';\nimport VideoDebate from '../components/VideoDebate';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LiveDebates = () => {\n  _s();\n  const [debates, setDebates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedDebate, setSelectedDebate] = useState(null);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [activeChatDebateId, setActiveChatDebateId] = useState(null);\n  const [activeVideoDebateId, setActiveVideoDebateId] = useState(null);\n  const [activeVideoDebate, setActiveVideoDebate] = useState(null);\n  const [newDebate, setNewDebate] = useState({\n    title: '',\n    description: '',\n    topic: '',\n    scheduled_at: ''\n  });\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isVerified\n  } = useAuth();\n  const {\n    isConnected,\n    activeDebates,\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    getDebateParticipantCount,\n    getDebateMessages\n  } = useWebSocket();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  const fetchDebates = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/debates`);\n      setDebates(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch debates');\n      console.error('Error fetching debates:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n  useEffect(() => {\n    fetchDebates();\n  }, [fetchDebates]);\n  const handleCreateDebate = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a debate');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/debates`, newDebate, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewDebate({\n          title: '',\n          description: '',\n          topic: '',\n          scheduled_at: ''\n        });\n        setShowCreateForm(false);\n        fetchDebates(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create debate');\n      console.error('Error creating debate:', err);\n    }\n  };\n  const handleJoinDebate = async debateId => {\n    if (!isAuthenticated) {\n      setError('Please login to join a debate');\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_URL}/api/debates/${debateId}/join`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        // Join WebSocket room for real-time features\n        joinDebate(debateId);\n        fetchDebates(); // Refresh to show updated participant list\n      }\n    } catch (err) {\n      setError('Failed to join debate');\n      console.error('Error joining debate:', err);\n    }\n  };\n  const handleJoinLiveDebate = debateId => {\n    if (!isAuthenticated) {\n      setError('Please login to join a live debate');\n      return;\n    }\n\n    // Join WebSocket room for real-time features\n    joinDebate(debateId);\n\n    // Open chat for live debate\n    setActiveChatDebateId(debateId);\n  };\n  const handleLeaveDebate = debateId => {\n    leaveDebate(debateId);\n    if (activeChatDebateId === debateId) {\n      setActiveChatDebateId(null);\n    }\n  };\n  const handleJoinVideoDebate = debate => {\n    if (!isAuthenticated) {\n      setError('Please login to join a video debate');\n      return;\n    }\n\n    // Check if browser supports required features\n    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n      setError('Your browser does not support video calling. Please use a modern browser.');\n      return;\n    }\n    setActiveVideoDebateId(debate.id);\n    setActiveVideoDebate(debate);\n\n    // Also join the regular debate room for chat\n    joinDebate(debate.id);\n  };\n  const handleLeaveVideoDebate = () => {\n    if (activeVideoDebateId) {\n      leaveDebate(activeVideoDebateId);\n      setActiveVideoDebateId(null);\n      setActiveVideoDebate(null);\n\n      // Also close chat if it's open for the same debate\n      if (activeChatDebateId === activeVideoDebateId) {\n        setActiveChatDebateId(null);\n      }\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'live':\n        return 'bg-red-100 text-red-800';\n      case 'scheduled':\n        return 'bg-blue-100 text-blue-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'live':\n        return '🔴';\n      case 'scheduled':\n        return '📅';\n      case 'completed':\n        return '✅';\n      default:\n        return '📅';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading debates...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDebates,\n          className: \"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Create debate form\n  if (showCreateForm) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCreateForm(false),\n            className: \"text-blue-600 hover:text-blue-800 mb-4\",\n            children: \"\\u2190 Back to Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Schedule New Debate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Create a new live debate for the community.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateDebate,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"title\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Debate Title *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"title\",\n                value: newDebate.title,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  title: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter a compelling debate title\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"topic\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Topic/Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"topic\",\n                value: newDebate.topic,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  topic: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a topic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Healthcare Policy\",\n                  children: \"Healthcare Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Environmental Policy\",\n                  children: \"Environmental Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Economic Policy\",\n                  children: \"Economic Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Education Policy\",\n                  children: \"Education Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Foreign Policy\",\n                  children: \"Foreign Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Social Issues\",\n                  children: \"Social Issues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Constitutional Law\",\n                  children: \"Constitutional Law\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Electoral Reform\",\n                  children: \"Electoral Reform\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                value: newDebate.description,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  description: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                rows: \"4\",\n                placeholder: \"Describe the debate topic and format\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"scheduled_at\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Scheduled Date & Time *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"datetime-local\",\n                id: \"scheduled_at\",\n                value: newDebate.scheduled_at,\n                onChange: e => setNewDebate({\n                  ...newDebate,\n                  scheduled_at: e.target.value\n                }),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowCreateForm(false),\n                className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                children: \"Schedule Debate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Live Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600\",\n            children: \"Watch and participate in live political debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), isAuthenticated && isVerified && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateForm(true),\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\",\n          children: \"Schedule Debate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: \"All Debates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: debates.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400 text-4xl mb-4\",\n                children: \"\\uD83C\\uDF99\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"No debates scheduled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"Be the first to schedule a political debate!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), isAuthenticated && isVerified && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateForm(true),\n                className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\",\n                children: \"Schedule First Debate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this) : debates.map(debate => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: debate.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(debate.status)}`,\n                  children: [getStatusIcon(debate.status), \" \", debate.status]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-3\",\n                children: debate.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Topic:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 26\n                  }, this), \" \", debate.topic]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Scheduled:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 26\n                  }, this), \" \", formatDate(debate.scheduled_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Moderator:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 26\n                  }, this), \" \", debate.moderator_username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Participants:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this), \" \", debate.participant_count, \" registered\", getDebateParticipantCount(debate.id) > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#10b981',\n                      marginLeft: '0.5rem'\n                    },\n                    children: [\"\\u2022 \", getDebateParticipantCount(debate.id), \" live now\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex space-x-2 flex-wrap gap-2\",\n                children: [debate.status === 'live' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleJoinVideoDebate(debate),\n                    className: \"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700 flex items-center gap-1\",\n                    children: \"\\uD83C\\uDFA5 Join Video\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleJoinLiveDebate(debate.id),\n                    className: \"bg-orange-600 text-white px-4 py-2 rounded-md text-sm hover:bg-orange-700\",\n                    children: \"\\uD83D\\uDD34 Join Audio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveChatDebateId(activeChatDebateId === debate.id ? null : debate.id),\n                    className: `px-4 py-2 rounded-md text-sm border ${activeChatDebateId === debate.id ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'}`,\n                    children: [\"\\uD83D\\uDCAC Chat \", getDebateMessages(debate.id).length > 0 && `(${getDebateMessages(debate.id).length})`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), debate.status === 'scheduled' && isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleJoinDebate(debate.id),\n                  className: \"bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700\",\n                  children: \"Join Debate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSelectedDebate(debate),\n                  className: \"border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50\",\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 21\n              }, this)]\n            }, debate.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: \"Live Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), debates.filter(d => d.status === 'live').length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: debates.filter(d => d.status === 'live').map(debate => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-2 border-red-200 rounded-lg p-4 bg-red-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 text-lg mr-2\",\n                  children: \"\\uD83D\\uDD34\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: debate.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-auto bg-red-600 text-white px-2 py-1 rounded text-xs\",\n                  children: \"LIVE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-3\",\n                children: debate.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2 flex-col sm:flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleJoinVideoDebate(debate),\n                  className: \"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center justify-center gap-1\",\n                  children: \"\\uD83C\\uDFA5 Join Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleJoinLiveDebate(debate.id),\n                  className: \"flex-1 bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700\",\n                  children: \"\\uD83D\\uDD34 Audio Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 21\n              }, this)]\n            }, debate.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-6xl mb-4\",\n              children: \"\\uD83D\\uDCFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-2\",\n              children: \"No Live Debates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: \"There are no debates currently live. Check back later or browse scheduled debates.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/login\",\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\",\n              children: \"Login to Participate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-12 bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-4\",\n          children: \"How Live Debates Work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Join Video or Audio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Choose between full video participation or audio-only mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Participate in Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Engage with other viewers through the live chat feature\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-bold\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Vote in Polls\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Participate in real-time polls during the debate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this), isAuthenticated && !isVerified && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-yellow-600 text-xl mr-3\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-yellow-800 mb-2\",\n              children: \"Verification Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-700\",\n              children: [\"To schedule debates and participate as a moderator, you need to be a verified user.\", /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/profile\",\n                className: \"underline ml-1\",\n                children: \"Request verification here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DebateChat, {\n        debateId: activeChatDebateId,\n        isVisible: !!activeChatDebateId,\n        onClose: () => {\n          if (activeChatDebateId) {\n            handleLeaveDebate(activeChatDebateId);\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(VideoDebate, {\n        debateId: activeVideoDebateId,\n        isVisible: !!activeVideoDebateId,\n        onClose: handleLeaveVideoDebate,\n        debate: activeVideoDebate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveDebates, \"s2VZtAZpENcNaxFYRr1O9bjsyig=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = LiveDebates;\nexport default LiveDebates;\nvar _c;\n$RefreshReg$(_c, \"LiveDebates\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "useWebSocket", "DebateChat", "VideoDebate", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LiveDebates", "_s", "debates", "setDebates", "loading", "setLoading", "error", "setError", "selectedDebate", "setSelectedDebate", "showCreateForm", "setShowCreateForm", "activeChatDebateId", "setActiveChatDebateId", "activeVideoDebateId", "setActiveVideoDebateId", "activeVideoDebate", "setActiveVideoDebate", "newDebate", "setNewDebate", "title", "description", "topic", "scheduled_at", "user", "token", "isAuthenticated", "isVerified", "isConnected", "activeDebates", "joinDebate", "leaveDebate", "sendDebateMessage", "getDebateParticipantCount", "getDebateMessages", "API_URL", "process", "env", "REACT_APP_API_URL", "fetchDebates", "response", "get", "data", "err", "console", "handleCreateDebate", "e", "preventDefault", "post", "headers", "success", "handleJoinDebate", "debateId", "handleJoinLiveDebate", "handleLeaveDebate", "handleJoinVideoDebate", "debate", "navigator", "mediaDevices", "getUserMedia", "id", "handleLeaveVideoDebate", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "getStatusColor", "status", "getStatusIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "placeholder", "required", "rows", "length", "map", "moderator_username", "participant_count", "style", "color", "marginLeft", "filter", "d", "href", "isVisible", "onClose", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/LiveDebates.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useWebSocket } from '../contexts/WebSocketContext';\nimport DebateChat from '../components/DebateChat';\nimport VideoDebate from '../components/VideoDebate';\nimport axios from 'axios';\n\nconst LiveDebates = () => {\n  const [debates, setDebates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedDebate, setSelectedDebate] = useState(null);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [activeChatDebateId, setActiveChatDebateId] = useState(null);\n  const [activeVideoDebateId, setActiveVideoDebateId] = useState(null);\n  const [activeVideoDebate, setActiveVideoDebate] = useState(null);\n  const [newDebate, setNewDebate] = useState({\n    title: '',\n    description: '',\n    topic: '',\n    scheduled_at: ''\n  });\n  const { user, token, isAuthenticated, isVerified } = useAuth();\n  const {\n    isConnected,\n    activeDebates,\n    joinDebate,\n    leaveDebate,\n    sendDebateMessage,\n    getDebateParticipantCount,\n    getDebateMessages\n  } = useWebSocket();\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  const fetchDebates = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/debates`);\n      setDebates(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch debates');\n      console.error('Error fetching debates:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [API_URL]);\n\n  useEffect(() => {\n    fetchDebates();\n  }, [fetchDebates]);\n\n  const handleCreateDebate = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to create a debate');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/debates`,\n        newDebate,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewDebate({ title: '', description: '', topic: '', scheduled_at: '' });\n        setShowCreateForm(false);\n        fetchDebates(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to create debate');\n      console.error('Error creating debate:', err);\n    }\n  };\n\n  const handleJoinDebate = async (debateId) => {\n    if (!isAuthenticated) {\n      setError('Please login to join a debate');\n      return;\n    }\n\n    try {\n      const response = await axios.post(\n        `${API_URL}/api/debates/${debateId}/join`,\n        {},\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        // Join WebSocket room for real-time features\n        joinDebate(debateId);\n        fetchDebates(); // Refresh to show updated participant list\n      }\n    } catch (err) {\n      setError('Failed to join debate');\n      console.error('Error joining debate:', err);\n    }\n  };\n\n  const handleJoinLiveDebate = (debateId) => {\n    if (!isAuthenticated) {\n      setError('Please login to join a live debate');\n      return;\n    }\n\n    // Join WebSocket room for real-time features\n    joinDebate(debateId);\n\n    // Open chat for live debate\n    setActiveChatDebateId(debateId);\n  };\n\n  const handleLeaveDebate = (debateId) => {\n    leaveDebate(debateId);\n    if (activeChatDebateId === debateId) {\n      setActiveChatDebateId(null);\n    }\n  };\n\n  const handleJoinVideoDebate = (debate) => {\n    if (!isAuthenticated) {\n      setError('Please login to join a video debate');\n      return;\n    }\n\n    // Check if browser supports required features\n    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n      setError('Your browser does not support video calling. Please use a modern browser.');\n      return;\n    }\n\n    setActiveVideoDebateId(debate.id);\n    setActiveVideoDebate(debate);\n\n    // Also join the regular debate room for chat\n    joinDebate(debate.id);\n  };\n\n  const handleLeaveVideoDebate = () => {\n    if (activeVideoDebateId) {\n      leaveDebate(activeVideoDebateId);\n      setActiveVideoDebateId(null);\n      setActiveVideoDebate(null);\n\n      // Also close chat if it's open for the same debate\n      if (activeChatDebateId === activeVideoDebateId) {\n        setActiveChatDebateId(null);\n      }\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'live':\n        return 'bg-red-100 text-red-800';\n      case 'scheduled':\n        return 'bg-blue-100 text-blue-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'live':\n        return '🔴';\n      case 'scheduled':\n        return '📅';\n      case 'completed':\n        return '✅';\n      default:\n        return '📅';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading debates...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-xl mb-4\">⚠️</div>\n          <p className=\"text-gray-600\">{error}</p>\n          <button\n            onClick={fetchDebates}\n            className=\"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Create debate form\n  if (showCreateForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setShowCreateForm(false)}\n              className=\"text-blue-600 hover:text-blue-800 mb-4\"\n            >\n              ← Back to Debates\n            </button>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Schedule New Debate</h1>\n            <p className=\"text-gray-600\">Create a new live debate for the community.</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <form onSubmit={handleCreateDebate}>\n              <div className=\"mb-6\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Debate Title *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  value={newDebate.title}\n                  onChange={(e) => setNewDebate({ ...newDebate, title: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter a compelling debate title\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"topic\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Topic/Category *\n                </label>\n                <select\n                  id=\"topic\"\n                  value={newDebate.topic}\n                  onChange={(e) => setNewDebate({ ...newDebate, topic: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">Select a topic</option>\n                  <option value=\"Healthcare Policy\">Healthcare Policy</option>\n                  <option value=\"Environmental Policy\">Environmental Policy</option>\n                  <option value=\"Economic Policy\">Economic Policy</option>\n                  <option value=\"Education Policy\">Education Policy</option>\n                  <option value=\"Foreign Policy\">Foreign Policy</option>\n                  <option value=\"Social Issues\">Social Issues</option>\n                  <option value=\"Constitutional Law\">Constitutional Law</option>\n                  <option value=\"Electoral Reform\">Electoral Reform</option>\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description *\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={newDebate.description}\n                  onChange={(e) => setNewDebate({ ...newDebate, description: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  rows=\"4\"\n                  placeholder=\"Describe the debate topic and format\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"scheduled_at\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Scheduled Date & Time *\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  id=\"scheduled_at\"\n                  value={newDebate.scheduled_at}\n                  onChange={(e) => setNewDebate({ ...newDebate, scheduled_at: e.target.value })}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowCreateForm(false)}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Schedule Debate\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Live Debates</h1>\n            <p className=\"text-lg text-gray-600\">\n              Watch and participate in live political debates\n            </p>\n          </div>\n          {isAuthenticated && isVerified && (\n            <button\n              onClick={() => setShowCreateForm(true)}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n            >\n              Schedule Debate\n            </button>\n          )}\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* All Debates */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">All Debates</h2>\n            <div className=\"space-y-4\">\n              {debates.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"text-gray-400 text-4xl mb-4\">🎙️</div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debates scheduled</h3>\n                  <p className=\"text-gray-600 mb-4\">Be the first to schedule a political debate!</p>\n                  {isAuthenticated && isVerified && (\n                    <button\n                      onClick={() => setShowCreateForm(true)}\n                      className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n                    >\n                      Schedule First Debate\n                    </button>\n                  )}\n                </div>\n              ) : (\n                debates.map((debate) => (\n                  <div key={debate.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h3 className=\"text-lg font-medium text-gray-900\">{debate.title}</h3>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(debate.status)}`}>\n                        {getStatusIcon(debate.status)} {debate.status}\n                      </span>\n                    </div>\n                    <p className=\"text-gray-600 mb-3\">{debate.description}</p>\n                    <div className=\"text-sm text-gray-500 space-y-1\">\n                      <p><strong>Topic:</strong> {debate.topic}</p>\n                      <p><strong>Scheduled:</strong> {formatDate(debate.scheduled_at)}</p>\n                      <p><strong>Moderator:</strong> {debate.moderator_username}</p>\n                      <p>\n                        <strong>Participants:</strong> {debate.participant_count} registered\n                        {getDebateParticipantCount(debate.id) > 0 && (\n                          <span style={{ color: '#10b981', marginLeft: '0.5rem' }}>\n                            • {getDebateParticipantCount(debate.id)} live now\n                          </span>\n                        )}\n                      </p>\n                    </div>\n                    <div className=\"mt-4 flex space-x-2 flex-wrap gap-2\">\n                      {debate.status === 'live' && (\n                        <>\n                          <button\n                            onClick={() => handleJoinVideoDebate(debate)}\n                            className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700 flex items-center gap-1\"\n                          >\n                            🎥 Join Video\n                          </button>\n                          <button\n                            onClick={() => handleJoinLiveDebate(debate.id)}\n                            className=\"bg-orange-600 text-white px-4 py-2 rounded-md text-sm hover:bg-orange-700\"\n                          >\n                            🔴 Join Audio\n                          </button>\n                          <button\n                            onClick={() => setActiveChatDebateId(activeChatDebateId === debate.id ? null : debate.id)}\n                            className={`px-4 py-2 rounded-md text-sm border ${\n                              activeChatDebateId === debate.id\n                                ? 'bg-blue-600 text-white border-blue-600'\n                                : 'border-gray-300 text-gray-700 hover:bg-gray-50'\n                            }`}\n                          >\n                            💬 Chat {getDebateMessages(debate.id).length > 0 && `(${getDebateMessages(debate.id).length})`}\n                          </button>\n                        </>\n                      )}\n                      {debate.status === 'scheduled' && isAuthenticated && (\n                        <button\n                          onClick={() => handleJoinDebate(debate.id)}\n                          className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700\"\n                        >\n                          Join Debate\n                        </button>\n                      )}\n                      <button\n                        onClick={() => setSelectedDebate(debate)}\n                        className=\"border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50\"\n                      >\n                        View Details\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Live Now / Featured */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Live Now</h2>\n            {debates.filter(d => d.status === 'live').length > 0 ? (\n              <div className=\"space-y-4\">\n                {debates.filter(d => d.status === 'live').map((debate) => (\n                  <div key={debate.id} className=\"border-2 border-red-200 rounded-lg p-4 bg-red-50\">\n                    <div className=\"flex items-center mb-2\">\n                      <span className=\"text-red-600 text-lg mr-2\">🔴</span>\n                      <h3 className=\"text-lg font-medium text-gray-900\">{debate.title}</h3>\n                      <span className=\"ml-auto bg-red-600 text-white px-2 py-1 rounded text-xs\">LIVE</span>\n                    </div>\n                    <p className=\"text-gray-600 mb-3\">{debate.description}</p>\n                    <div className=\"flex gap-2 flex-col sm:flex-row\">\n                      <button\n                        onClick={() => handleJoinVideoDebate(debate)}\n                        className=\"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center justify-center gap-1\"\n                      >\n                        🎥 Join Video\n                      </button>\n                      <button\n                        onClick={() => handleJoinLiveDebate(debate.id)}\n                        className=\"flex-1 bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700\"\n                      >\n                        🔴 Audio Only\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">📺</div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Live Debates</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  There are no debates currently live. Check back later or browse scheduled debates.\n                </p>\n                {!isAuthenticated && (\n                  <a href=\"/login\" className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\">\n                    Login to Participate\n                  </a>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* How It Works */}\n        <div className=\"mt-12 bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">How Live Debates Work</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">1</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Join Video or Audio</h3>\n              <p className=\"text-sm text-gray-600\">\n                Choose between full video participation or audio-only mode\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">2</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Participate in Chat</h3>\n              <p className=\"text-sm text-gray-600\">\n                Engage with other viewers through the live chat feature\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <span className=\"text-blue-600 font-bold\">3</span>\n              </div>\n              <h3 className=\"font-medium text-gray-900 mb-2\">Vote in Polls</h3>\n              <p className=\"text-sm text-gray-600\">\n                Participate in real-time polls during the debate\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Requirements Notice */}\n        {isAuthenticated && !isVerified && (\n          <div className=\"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"text-yellow-600 text-xl mr-3\">⚠️</div>\n              <div>\n                <h3 className=\"text-lg font-medium text-yellow-800 mb-2\">Verification Required</h3>\n                <p className=\"text-yellow-700\">\n                  To schedule debates and participate as a moderator, you need to be a verified user.\n                  <a href=\"/profile\" className=\"underline ml-1\">Request verification here</a>.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Real-time Debate Chat */}\n        <DebateChat\n          debateId={activeChatDebateId}\n          isVisible={!!activeChatDebateId}\n          onClose={() => {\n            if (activeChatDebateId) {\n              handleLeaveDebate(activeChatDebateId);\n            }\n          }}\n        />\n\n        {/* Video Debate Component */}\n        <VideoDebate\n          debateId={activeVideoDebateId}\n          isVisible={!!activeVideoDebateId}\n          onClose={handleLeaveVideoDebate}\n          debate={activeVideoDebate}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default LiveDebates;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC;IACzCgC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAGpC,OAAO,CAAC,CAAC;EAC9D,MAAM;IACJqC,WAAW;IACXC,aAAa;IACbC,UAAU;IACVC,WAAW;IACXC,iBAAiB;IACjBC,yBAAyB;IACzBC;EACF,CAAC,GAAG1C,YAAY,CAAC,CAAC;EAElB,MAAM2C,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExE,MAAMC,YAAY,GAAGjD,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFe,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmC,QAAQ,GAAG,MAAM7C,KAAK,CAAC8C,GAAG,CAAC,GAAGN,OAAO,cAAc,CAAC;MAC1DhC,UAAU,CAACqC,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZpC,QAAQ,CAAC,yBAAyB,CAAC;MACnCqC,OAAO,CAACtC,KAAK,CAAC,yBAAyB,EAAEqC,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAC8B,OAAO,CAAC,CAAC;EAEb9C,SAAS,CAAC,MAAM;IACdkD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMM,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACrB,eAAe,EAAE;MACpBnB,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAM7C,KAAK,CAACqD,IAAI,CAC/B,GAAGb,OAAO,cAAc,EACxBjB,SAAS,EACT;QACE+B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUxB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIe,QAAQ,CAACE,IAAI,CAACQ,OAAO,EAAE;QACzB/B,YAAY,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAC,CAAC;QACzEZ,iBAAiB,CAAC,KAAK,CAAC;QACxB4B,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZpC,QAAQ,CAAC,yBAAyB,CAAC;MACnCqC,OAAO,CAACtC,KAAK,CAAC,wBAAwB,EAAEqC,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAG,MAAOC,QAAQ,IAAK;IAC3C,IAAI,CAAC1B,eAAe,EAAE;MACpBnB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAM7C,KAAK,CAACqD,IAAI,CAC/B,GAAGb,OAAO,gBAAgBiB,QAAQ,OAAO,EACzC,CAAC,CAAC,EACF;QACEH,OAAO,EAAE;UACP,eAAe,EAAE,UAAUxB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIe,QAAQ,CAACE,IAAI,CAACQ,OAAO,EAAE;QACzB;QACApB,UAAU,CAACsB,QAAQ,CAAC;QACpBb,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZpC,QAAQ,CAAC,uBAAuB,CAAC;MACjCqC,OAAO,CAACtC,KAAK,CAAC,uBAAuB,EAAEqC,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMU,oBAAoB,GAAID,QAAQ,IAAK;IACzC,IAAI,CAAC1B,eAAe,EAAE;MACpBnB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;;IAEA;IACAuB,UAAU,CAACsB,QAAQ,CAAC;;IAEpB;IACAvC,qBAAqB,CAACuC,QAAQ,CAAC;EACjC,CAAC;EAED,MAAME,iBAAiB,GAAIF,QAAQ,IAAK;IACtCrB,WAAW,CAACqB,QAAQ,CAAC;IACrB,IAAIxC,kBAAkB,KAAKwC,QAAQ,EAAE;MACnCvC,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAM0C,qBAAqB,GAAIC,MAAM,IAAK;IACxC,IAAI,CAAC9B,eAAe,EAAE;MACpBnB,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAI,CAACkD,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;MACnEpD,QAAQ,CAAC,2EAA2E,CAAC;MACrF;IACF;IAEAQ,sBAAsB,CAACyC,MAAM,CAACI,EAAE,CAAC;IACjC3C,oBAAoB,CAACuC,MAAM,CAAC;;IAE5B;IACA1B,UAAU,CAAC0B,MAAM,CAACI,EAAE,CAAC;EACvB,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI/C,mBAAmB,EAAE;MACvBiB,WAAW,CAACjB,mBAAmB,CAAC;MAChCC,sBAAsB,CAAC,IAAI,CAAC;MAC5BE,oBAAoB,CAAC,IAAI,CAAC;;MAE1B;MACA,IAAIL,kBAAkB,KAAKE,mBAAmB,EAAE;QAC9CD,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IACF;EACF,CAAC;EAED,MAAMiD,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAGF,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACvE,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,yBAAyB;MAClC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,aAAa,GAAID,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,WAAW;QACd,OAAO,GAAG;MACZ;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK0E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE3E,OAAA;QAAK0E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3E,OAAA;UAAK0E,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F/E,OAAA;UAAG0E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItE,KAAK,EAAE;IACT,oBACET,OAAA;MAAK0E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE3E,OAAA;QAAK0E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3E,OAAA;UAAK0E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnD/E,OAAA;UAAG0E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAElE;QAAK;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxC/E,OAAA;UACEgF,OAAO,EAAEtC,YAAa;UACtBgC,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIlE,cAAc,EAAE;IAClB,oBACEb,OAAA;MAAK0E,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtC3E,OAAA;QAAK0E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D3E,OAAA;UAAK0E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB3E,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,KAAK,CAAE;YACxC4D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/E,OAAA;YAAI0E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E/E,OAAA;YAAG0E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eAEN/E,OAAA;UAAK0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD3E,OAAA;YAAMiF,QAAQ,EAAEjC,kBAAmB;YAAA2B,QAAA,gBACjC3E,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3E,OAAA;gBAAOkF,OAAO,EAAC,OAAO;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACEmF,IAAI,EAAC,MAAM;gBACXpB,EAAE,EAAC,OAAO;gBACVqB,KAAK,EAAE/D,SAAS,CAACE,KAAM;gBACvB8D,QAAQ,EAAGpC,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,KAAK,EAAE0B,CAAC,CAACqC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEV,SAAS,EAAC,wFAAwF;gBAClGa,WAAW,EAAC,iCAAiC;gBAC7CC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3E,OAAA;gBAAOkF,OAAO,EAAC,OAAO;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACE+D,EAAE,EAAC,OAAO;gBACVqB,KAAK,EAAE/D,SAAS,CAACI,KAAM;gBACvB4D,QAAQ,EAAGpC,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEI,KAAK,EAAEwB,CAAC,CAACqC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEV,SAAS,EAAC,wFAAwF;gBAClGc,QAAQ;gBAAAb,QAAA,gBAER3E,OAAA;kBAAQoF,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC/E,OAAA;kBAAQoF,KAAK,EAAC,mBAAmB;kBAAAT,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5D/E,OAAA;kBAAQoF,KAAK,EAAC,sBAAsB;kBAAAT,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClE/E,OAAA;kBAAQoF,KAAK,EAAC,iBAAiB;kBAAAT,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxD/E,OAAA;kBAAQoF,KAAK,EAAC,kBAAkB;kBAAAT,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1D/E,OAAA;kBAAQoF,KAAK,EAAC,gBAAgB;kBAAAT,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD/E,OAAA;kBAAQoF,KAAK,EAAC,eAAe;kBAAAT,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/E,OAAA;kBAAQoF,KAAK,EAAC,oBAAoB;kBAAAT,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9D/E,OAAA;kBAAQoF,KAAK,EAAC,kBAAkB;kBAAAT,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/E,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3E,OAAA;gBAAOkF,OAAO,EAAC,aAAa;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACE+D,EAAE,EAAC,aAAa;gBAChBqB,KAAK,EAAE/D,SAAS,CAACG,WAAY;gBAC7B6D,QAAQ,EAAGpC,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,WAAW,EAAEyB,CAAC,CAACqC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC7EV,SAAS,EAAC,wFAAwF;gBAClGe,IAAI,EAAC,GAAG;gBACRF,WAAW,EAAC,sCAAsC;gBAClDC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3E,OAAA;gBAAOkF,OAAO,EAAC,cAAc;gBAACR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACEmF,IAAI,EAAC,gBAAgB;gBACrBpB,EAAE,EAAC,cAAc;gBACjBqB,KAAK,EAAE/D,SAAS,CAACK,YAAa;gBAC9B2D,QAAQ,EAAGpC,CAAC,IAAK3B,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEK,YAAY,EAAEuB,CAAC,CAACqC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC9EV,SAAS,EAAC,wFAAwF;gBAClGc,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAK0E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC3E,OAAA;gBACEmF,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,KAAK,CAAE;gBACxC4D,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/E,OAAA;gBACEmF,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK0E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC3E,OAAA;MAAK0E,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D3E,OAAA;QAAK0E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD3E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAI0E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE/E,OAAA;YAAG0E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLlD,eAAe,IAAIC,UAAU,iBAC5B9B,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,IAAI,CAAE;UACvC4D,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAC1E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3E,OAAA;UAAK0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE/E,OAAA;YAAK0E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBtE,OAAO,CAACqF,MAAM,KAAK,CAAC,gBACnB1F,OAAA;cAAK0E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3E,OAAA;gBAAK0E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtD/E,OAAA;gBAAI0E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChF/E,OAAA;gBAAG0E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFlD,eAAe,IAAIC,UAAU,iBAC5B9B,OAAA;gBACEgF,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,IAAI,CAAE;gBACvC4D,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,GAEN1E,OAAO,CAACsF,GAAG,CAAEhC,MAAM,iBACjB3D,OAAA;cAAqB0E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpE3E,OAAA;gBAAK0E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD3E,OAAA;kBAAI0E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEhB,MAAM,CAACpC;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrE/E,OAAA;kBAAM0E,SAAS,EAAE,8CAA8CH,cAAc,CAACZ,MAAM,CAACa,MAAM,CAAC,EAAG;kBAAAG,QAAA,GAC5FF,aAAa,CAACd,MAAM,CAACa,MAAM,CAAC,EAAC,GAAC,EAACb,MAAM,CAACa,MAAM;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN/E,OAAA;gBAAG0E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEhB,MAAM,CAACnC;cAAW;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D/E,OAAA;gBAAK0E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C3E,OAAA;kBAAA2E,QAAA,gBAAG3E,OAAA;oBAAA2E,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACpB,MAAM,CAAClC,KAAK;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7C/E,OAAA;kBAAA2E,QAAA,gBAAG3E,OAAA;oBAAA2E,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACd,UAAU,CAACN,MAAM,CAACjC,YAAY,CAAC;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE/E,OAAA;kBAAA2E,QAAA,gBAAG3E,OAAA;oBAAA2E,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACpB,MAAM,CAACiC,kBAAkB;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9D/E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAA2E,QAAA,EAAQ;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACpB,MAAM,CAACkC,iBAAiB,EAAC,aACzD,EAACzD,yBAAyB,CAACuB,MAAM,CAACI,EAAE,CAAC,GAAG,CAAC,iBACvC/D,OAAA;oBAAM8F,KAAK,EAAE;sBAAEC,KAAK,EAAE,SAAS;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAArB,QAAA,GAAC,SACrD,EAACvC,yBAAyB,CAACuB,MAAM,CAACI,EAAE,CAAC,EAAC,WAC1C;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN/E,OAAA;gBAAK0E,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GACjDhB,MAAM,CAACa,MAAM,KAAK,MAAM,iBACvBxE,OAAA,CAAAE,SAAA;kBAAAyE,QAAA,gBACE3E,OAAA;oBACEgF,OAAO,EAAEA,CAAA,KAAMtB,qBAAqB,CAACC,MAAM,CAAE;oBAC7Ce,SAAS,EAAC,6FAA6F;oBAAAC,QAAA,EACxG;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/E,OAAA;oBACEgF,OAAO,EAAEA,CAAA,KAAMxB,oBAAoB,CAACG,MAAM,CAACI,EAAE,CAAE;oBAC/CW,SAAS,EAAC,2EAA2E;oBAAAC,QAAA,EACtF;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/E,OAAA;oBACEgF,OAAO,EAAEA,CAAA,KAAMhE,qBAAqB,CAACD,kBAAkB,KAAK4C,MAAM,CAACI,EAAE,GAAG,IAAI,GAAGJ,MAAM,CAACI,EAAE,CAAE;oBAC1FW,SAAS,EAAE,uCACT3D,kBAAkB,KAAK4C,MAAM,CAACI,EAAE,GAC5B,wCAAwC,GACxC,gDAAgD,EACnD;oBAAAY,QAAA,GACJ,oBACS,EAACtC,iBAAiB,CAACsB,MAAM,CAACI,EAAE,CAAC,CAAC2B,MAAM,GAAG,CAAC,IAAI,IAAIrD,iBAAiB,CAACsB,MAAM,CAACI,EAAE,CAAC,CAAC2B,MAAM,GAAG;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC;gBAAA,eACT,CACH,EACApB,MAAM,CAACa,MAAM,KAAK,WAAW,IAAI3C,eAAe,iBAC/C7B,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAACK,MAAM,CAACI,EAAE,CAAE;kBAC3CW,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,EAClF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACD/E,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAAC+C,MAAM,CAAE;kBACzCe,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,EAC/F;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA9DEpB,MAAM,CAACI,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Dd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/E,OAAA;UAAK0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACrE1E,OAAO,CAAC4F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAK,MAAM,CAAC,CAACkB,MAAM,GAAG,CAAC,gBAClD1F,OAAA;YAAK0E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBtE,OAAO,CAAC4F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAK,MAAM,CAAC,CAACmB,GAAG,CAAEhC,MAAM,iBACnD3D,OAAA;cAAqB0E,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/E3E,OAAA;gBAAK0E,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC3E,OAAA;kBAAM0E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD/E,OAAA;kBAAI0E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEhB,MAAM,CAACpC;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrE/E,OAAA;kBAAM0E,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACN/E,OAAA;gBAAG0E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEhB,MAAM,CAACnC;cAAW;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D/E,OAAA;gBAAK0E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C3E,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAMtB,qBAAqB,CAACC,MAAM,CAAE;kBAC7Ce,SAAS,EAAC,2GAA2G;kBAAAC,QAAA,EACtH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/E,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAMxB,oBAAoB,CAACG,MAAM,CAACI,EAAE,CAAE;kBAC/CW,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,EACrF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GApBEpB,MAAM,CAACI,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN/E,OAAA;YAAK0E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3E,OAAA;cAAK0E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrD/E,OAAA;cAAI0E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E/E,OAAA;cAAG0E,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAClD,eAAe,iBACf7B,OAAA;cAAGmG,IAAI,EAAC,QAAQ;cAACzB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE3F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAK0E,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD3E,OAAA;UAAI0E,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF/E,OAAA;UAAK0E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD3E,OAAA;YAAK0E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3E,OAAA;cAAK0E,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F3E,OAAA;gBAAM0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN/E,OAAA;cAAI0E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE/E,OAAA;cAAG0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/E,OAAA;YAAK0E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3E,OAAA;cAAK0E,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F3E,OAAA;gBAAM0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN/E,OAAA;cAAI0E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE/E,OAAA;cAAG0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/E,OAAA;YAAK0E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3E,OAAA;cAAK0E,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7F3E,OAAA;gBAAM0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN/E,OAAA;cAAI0E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjE/E,OAAA;cAAG0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLlD,eAAe,IAAI,CAACC,UAAU,iBAC7B9B,OAAA;QAAK0E,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE3E,OAAA;UAAK0E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3E,OAAA;YAAK0E,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtD/E,OAAA;YAAA2E,QAAA,gBACE3E,OAAA;cAAI0E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnF/E,OAAA;cAAG0E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,qFAE7B,eAAA3E,OAAA;gBAAGmG,IAAI,EAAC,UAAU;gBAACzB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,KAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD/E,OAAA,CAACJ,UAAU;QACT2D,QAAQ,EAAExC,kBAAmB;QAC7BqF,SAAS,EAAE,CAAC,CAACrF,kBAAmB;QAChCsF,OAAO,EAAEA,CAAA,KAAM;UACb,IAAItF,kBAAkB,EAAE;YACtB0C,iBAAiB,CAAC1C,kBAAkB,CAAC;UACvC;QACF;MAAE;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF/E,OAAA,CAACH,WAAW;QACV0D,QAAQ,EAAEtC,mBAAoB;QAC9BmF,SAAS,EAAE,CAAC,CAACnF,mBAAoB;QACjCoF,OAAO,EAAErC,sBAAuB;QAChCL,MAAM,EAAExC;MAAkB;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAtiBID,WAAW;EAAA,QAesCT,OAAO,EASxDC,YAAY;AAAA;AAAA2G,EAAA,GAxBZnG,WAAW;AAwiBjB,eAAeA,WAAW;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}