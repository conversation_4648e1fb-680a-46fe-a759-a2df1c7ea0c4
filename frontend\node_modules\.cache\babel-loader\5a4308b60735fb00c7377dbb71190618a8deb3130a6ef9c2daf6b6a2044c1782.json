{"ast": null, "code": "'use strict';\n\n/**\n * @typedef {{ [key: string]: any }} Extensions\n * @typedef {Error} Err\n * @property {string} message\n */\n\n/**\n *\n * @param {Error} obj\n * @param {Extensions} props\n * @returns {Error & Extensions}\n */\nfunction assign(obj, props) {\n  for (const key in props) {\n    Object.defineProperty(obj, key, {\n      value: props[key],\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return obj;\n}\n\n/**\n *\n * @param {any} err - An Error\n * @param {string|Extensions} code - A string code or props to set on the error\n * @param {Extensions} [props] - Props to set on the error\n * @returns {Error & Extensions}\n */\nfunction createError(err, code, props) {\n  if (!err || typeof err === 'string') {\n    throw new TypeError('Please pass an Error to err-code');\n  }\n  if (!props) {\n    props = {};\n  }\n  if (typeof code === 'object') {\n    props = code;\n    code = '';\n  }\n  if (code) {\n    props.code = code;\n  }\n  try {\n    return assign(err, props);\n  } catch (_) {\n    props.message = err.message;\n    props.stack = err.stack;\n    const ErrClass = function () {};\n    ErrClass.prototype = Object.create(Object.getPrototypeOf(err));\n\n    // @ts-ignore\n    const output = assign(new ErrClass(), props);\n    return output;\n  }\n}\nmodule.exports = createError;", "map": {"version": 3, "names": ["assign", "obj", "props", "key", "Object", "defineProperty", "value", "enumerable", "configurable", "createError", "err", "code", "TypeError", "_", "message", "stack", "ErrClass", "prototype", "create", "getPrototypeOf", "output", "module", "exports"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/err-code/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * @typedef {{ [key: string]: any }} Extensions\n * @typedef {Error} Err\n * @property {string} message\n */\n\n/**\n *\n * @param {Error} obj\n * @param {Extensions} props\n * @returns {Error & Extensions}\n */\nfunction assign(obj, props) {\n    for (const key in props) {\n        Object.defineProperty(obj, key, {\n            value: props[key],\n            enumerable: true,\n            configurable: true,\n        });\n    }\n\n    return obj;\n}\n\n/**\n *\n * @param {any} err - An Error\n * @param {string|Extensions} code - A string code or props to set on the error\n * @param {Extensions} [props] - Props to set on the error\n * @returns {Error & Extensions}\n */\nfunction createError(err, code, props) {\n    if (!err || typeof err === 'string') {\n        throw new TypeError('Please pass an Error to err-code');\n    }\n\n    if (!props) {\n        props = {};\n    }\n\n    if (typeof code === 'object') {\n        props = code;\n        code = '';\n    }\n\n    if (code) {\n        props.code = code;\n    }\n\n    try {\n        return assign(err, props);\n    } catch (_) {\n        props.message = err.message;\n        props.stack = err.stack;\n\n        const ErrClass = function () {};\n\n        ErrClass.prototype = Object.create(Object.getPrototypeOf(err));\n\n        // @ts-ignore\n        const output = assign(new ErrClass(), props);\n\n        return output;\n    }\n}\n\nmodule.exports = createError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACxB,KAAK,MAAMC,GAAG,IAAID,KAAK,EAAE;IACrBE,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEE,GAAG,EAAE;MAC5BG,KAAK,EAAEJ,KAAK,CAACC,GAAG,CAAC;MACjBI,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EAEA,OAAOP,GAAG;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAET,KAAK,EAAE;EACnC,IAAI,CAACQ,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACjC,MAAM,IAAIE,SAAS,CAAC,kCAAkC,CAAC;EAC3D;EAEA,IAAI,CAACV,KAAK,EAAE;IACRA,KAAK,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,OAAOS,IAAI,KAAK,QAAQ,EAAE;IAC1BT,KAAK,GAAGS,IAAI;IACZA,IAAI,GAAG,EAAE;EACb;EAEA,IAAIA,IAAI,EAAE;IACNT,KAAK,CAACS,IAAI,GAAGA,IAAI;EACrB;EAEA,IAAI;IACA,OAAOX,MAAM,CAACU,GAAG,EAAER,KAAK,CAAC;EAC7B,CAAC,CAAC,OAAOW,CAAC,EAAE;IACRX,KAAK,CAACY,OAAO,GAAGJ,GAAG,CAACI,OAAO;IAC3BZ,KAAK,CAACa,KAAK,GAAGL,GAAG,CAACK,KAAK;IAEvB,MAAMC,QAAQ,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;IAE/BA,QAAQ,CAACC,SAAS,GAAGb,MAAM,CAACc,MAAM,CAACd,MAAM,CAACe,cAAc,CAACT,GAAG,CAAC,CAAC;;IAE9D;IACA,MAAMU,MAAM,GAAGpB,MAAM,CAAC,IAAIgB,QAAQ,CAAC,CAAC,EAAEd,KAAK,CAAC;IAE5C,OAAOkB,MAAM;EACjB;AACJ;AAEAC,MAAM,CAACC,OAAO,GAAGb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}