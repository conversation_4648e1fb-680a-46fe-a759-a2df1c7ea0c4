{"ast": null, "code": "'use strict';\n\n// undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n  var _this = this;\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        process.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorNT, this, err);\n      }\n    }\n    return this;\n  }\n\n  // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  }\n\n  // if this is a duplex stream mark the writable part as destroyed as well\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else {\n        process.nextTick(emitCloseNT, _this);\n      }\n    } else if (cb) {\n      process.nextTick(emitCloseNT, _this);\n      cb(err);\n    } else {\n      process.nextTick(emitCloseNT, _this);\n    }\n  });\n  return this;\n}\nfunction emitErrorAndCloseNT(self, err) {\n  emitErrorNT(self, err);\n  emitCloseNT(self);\n}\nfunction emitCloseNT(self) {\n  if (self._writableState && !self._writableState.emitClose) return;\n  if (self._readableState && !self._readableState.emitClose) return;\n  self.emit('close');\n}\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\nfunction errorOrDestroy(stream, err) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n\n  var rState = stream._readableState;\n  var wState = stream._writableState;\n  if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);else stream.emit('error', err);\n}\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy,\n  errorOrDestroy: errorOrDestroy\n};", "map": {"version": 3, "names": ["destroy", "err", "cb", "_this", "readableDestroyed", "_readableState", "destroyed", "writableDestroyed", "_writableState", "process", "nextTick", "emitErrorNT", "errorEmitted", "_destroy", "emitErrorAndCloseNT", "emitCloseNT", "self", "emitClose", "emit", "undestroy", "reading", "ended", "endEmitted", "ending", "finalCalled", "prefinished", "finished", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "rState", "wState", "autoDestroy", "module", "exports"], "sources": ["F:/POLITICA/VS CODE/frontend/node_modules/readable-stream/lib/internal/streams/destroy.js"], "sourcesContent": ["'use strict';\n\n// undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n  var _this = this;\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        process.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorNT, this, err);\n      }\n    }\n    return this;\n  }\n\n  // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  }\n\n  // if this is a duplex stream mark the writable part as destroyed as well\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else {\n        process.nextTick(emitCloseNT, _this);\n      }\n    } else if (cb) {\n      process.nextTick(emitCloseNT, _this);\n      cb(err);\n    } else {\n      process.nextTick(emitCloseNT, _this);\n    }\n  });\n  return this;\n}\nfunction emitErrorAndCloseNT(self, err) {\n  emitErrorNT(self, err);\n  emitCloseNT(self);\n}\nfunction emitCloseNT(self) {\n  if (self._writableState && !self._writableState.emitClose) return;\n  if (self._readableState && !self._readableState.emitClose) return;\n  self.emit('close');\n}\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\nfunction errorOrDestroy(stream, err) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n\n  var rState = stream._readableState;\n  var wState = stream._writableState;\n  if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);else stream.emit('error', err);\n}\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy,\n  errorOrDestroy: errorOrDestroy\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,SAASA,OAAOA,CAACC,GAAG,EAAEC,EAAE,EAAE;EACxB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,iBAAiB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACC,SAAS;EAC5E,IAAIC,iBAAiB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACF,SAAS;EAC5E,IAAIF,iBAAiB,IAAIG,iBAAiB,EAAE;IAC1C,IAAIL,EAAE,EAAE;MACNA,EAAE,CAACD,GAAG,CAAC;IACT,CAAC,MAAM,IAAIA,GAAG,EAAE;MACd,IAAI,CAAC,IAAI,CAACO,cAAc,EAAE;QACxBC,OAAO,CAACC,QAAQ,CAACC,WAAW,EAAE,IAAI,EAAEV,GAAG,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC,IAAI,CAACO,cAAc,CAACI,YAAY,EAAE;QAC5C,IAAI,CAACJ,cAAc,CAACI,YAAY,GAAG,IAAI;QACvCH,OAAO,CAACC,QAAQ,CAACC,WAAW,EAAE,IAAI,EAAEV,GAAG,CAAC;MAC1C;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACA;;EAEA,IAAI,IAAI,CAACI,cAAc,EAAE;IACvB,IAAI,CAACA,cAAc,CAACC,SAAS,GAAG,IAAI;EACtC;;EAEA;EACA,IAAI,IAAI,CAACE,cAAc,EAAE;IACvB,IAAI,CAACA,cAAc,CAACF,SAAS,GAAG,IAAI;EACtC;EACA,IAAI,CAACO,QAAQ,CAACZ,GAAG,IAAI,IAAI,EAAE,UAAUA,GAAG,EAAE;IACxC,IAAI,CAACC,EAAE,IAAID,GAAG,EAAE;MACd,IAAI,CAACE,KAAK,CAACK,cAAc,EAAE;QACzBC,OAAO,CAACC,QAAQ,CAACI,mBAAmB,EAAEX,KAAK,EAAEF,GAAG,CAAC;MACnD,CAAC,MAAM,IAAI,CAACE,KAAK,CAACK,cAAc,CAACI,YAAY,EAAE;QAC7CT,KAAK,CAACK,cAAc,CAACI,YAAY,GAAG,IAAI;QACxCH,OAAO,CAACC,QAAQ,CAACI,mBAAmB,EAAEX,KAAK,EAAEF,GAAG,CAAC;MACnD,CAAC,MAAM;QACLQ,OAAO,CAACC,QAAQ,CAACK,WAAW,EAAEZ,KAAK,CAAC;MACtC;IACF,CAAC,MAAM,IAAID,EAAE,EAAE;MACbO,OAAO,CAACC,QAAQ,CAACK,WAAW,EAAEZ,KAAK,CAAC;MACpCD,EAAE,CAACD,GAAG,CAAC;IACT,CAAC,MAAM;MACLQ,OAAO,CAACC,QAAQ,CAACK,WAAW,EAAEZ,KAAK,CAAC;IACtC;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AACA,SAASW,mBAAmBA,CAACE,IAAI,EAAEf,GAAG,EAAE;EACtCU,WAAW,CAACK,IAAI,EAAEf,GAAG,CAAC;EACtBc,WAAW,CAACC,IAAI,CAAC;AACnB;AACA,SAASD,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIA,IAAI,CAACR,cAAc,IAAI,CAACQ,IAAI,CAACR,cAAc,CAACS,SAAS,EAAE;EAC3D,IAAID,IAAI,CAACX,cAAc,IAAI,CAACW,IAAI,CAACX,cAAc,CAACY,SAAS,EAAE;EAC3DD,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;AACpB;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,IAAI,CAACd,cAAc,EAAE;IACvB,IAAI,CAACA,cAAc,CAACC,SAAS,GAAG,KAAK;IACrC,IAAI,CAACD,cAAc,CAACe,OAAO,GAAG,KAAK;IACnC,IAAI,CAACf,cAAc,CAACgB,KAAK,GAAG,KAAK;IACjC,IAAI,CAAChB,cAAc,CAACiB,UAAU,GAAG,KAAK;EACxC;EACA,IAAI,IAAI,CAACd,cAAc,EAAE;IACvB,IAAI,CAACA,cAAc,CAACF,SAAS,GAAG,KAAK;IACrC,IAAI,CAACE,cAAc,CAACa,KAAK,GAAG,KAAK;IACjC,IAAI,CAACb,cAAc,CAACe,MAAM,GAAG,KAAK;IAClC,IAAI,CAACf,cAAc,CAACgB,WAAW,GAAG,KAAK;IACvC,IAAI,CAAChB,cAAc,CAACiB,WAAW,GAAG,KAAK;IACvC,IAAI,CAACjB,cAAc,CAACkB,QAAQ,GAAG,KAAK;IACpC,IAAI,CAAClB,cAAc,CAACI,YAAY,GAAG,KAAK;EAC1C;AACF;AACA,SAASD,WAAWA,CAACK,IAAI,EAAEf,GAAG,EAAE;EAC9Be,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEjB,GAAG,CAAC;AACzB;AACA,SAAS0B,cAAcA,CAACC,MAAM,EAAE3B,GAAG,EAAE;EACnC;EACA;EACA;EACA;EACA;;EAEA,IAAI4B,MAAM,GAAGD,MAAM,CAACvB,cAAc;EAClC,IAAIyB,MAAM,GAAGF,MAAM,CAACpB,cAAc;EAClC,IAAIqB,MAAM,IAAIA,MAAM,CAACE,WAAW,IAAID,MAAM,IAAIA,MAAM,CAACC,WAAW,EAAEH,MAAM,CAAC5B,OAAO,CAACC,GAAG,CAAC,CAAC,KAAK2B,MAAM,CAACV,IAAI,CAAC,OAAO,EAAEjB,GAAG,CAAC;AACtH;AACA+B,MAAM,CAACC,OAAO,GAAG;EACfjC,OAAO,EAAEA,OAAO;EAChBmB,SAAS,EAAEA,SAAS;EACpBQ,cAAc,EAAEA;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}