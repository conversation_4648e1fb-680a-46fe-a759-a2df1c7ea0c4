{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\ResearchRepository.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResearchRepository = () => {\n  _s();\n  const [papers, setPapers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all');\n  const [selectedPaper, setSelectedPaper] = useState(null);\n  const [showSubmissionForm, setShowSubmissionForm] = useState(false);\n  const [newPaper, setNewPaper] = useState({\n    title: '',\n    abstract: '',\n    category: '',\n    keywords: '',\n    content: ''\n  });\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isVerified\n  } = useAuth();\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n  useEffect(() => {\n    fetchPapers();\n  }, []);\n  const fetchPapers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/research`);\n      setPapers(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch research papers');\n      console.error('Error fetching papers:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmitPaper = async e => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to submit a paper');\n      return;\n    }\n    try {\n      const keywordsArray = newPaper.keywords.split(',').map(k => k.trim()).filter(k => k);\n      const response = await axios.post(`${API_URL}/api/research`, {\n        ...newPaper,\n        keywords: keywordsArray\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setNewPaper({\n          title: '',\n          abstract: '',\n          category: '',\n          keywords: '',\n          content: ''\n        });\n        setShowSubmissionForm(false);\n        fetchPapers(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to submit paper');\n      console.error('Error submitting paper:', err);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'under_review':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const filteredPapers = papers.filter(paper => filter === 'all' || paper.status === filter);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading research papers...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Research Repository\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Access peer-reviewed political research papers and submit your own work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filter,\n                onChange: e => setFilter(e.target.value),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Papers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"published\",\n                  children: \"Published\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"under_review\",\n                  children: \"Under Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"rejected\",\n                  children: \"Rejected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mb-4\",\n              children: \"Submit Paper\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium mb-2\",\n                children: \"Submission Guidelines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Original research only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Peer review required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 APA citation format\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Maximum 10,000 words\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-3/4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-gray-900\",\n                  children: [\"Research Papers (\", filteredPapers.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-gray-500 hover:text-gray-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 160,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-gray-500 hover:text-gray-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-200\",\n              children: filteredPapers.map(paper => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 flex-1 mr-4\",\n                    children: paper.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`,\n                    children: paper.status.replace('_', ' ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-3 line-clamp-3\",\n                  children: paper.abstract\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 text-sm text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Authors:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 29\n                    }, this), \" \", paper.authors.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Category:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 29\n                    }, this), \" \", paper.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this), paper.publishedDate && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Published:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 31\n                    }, this), \" \", new Date(paper.publishedDate).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-2 mb-4\",\n                  children: paper.keywords.map((keyword, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\",\n                    children: keyword\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [paper.downloadCount, \" downloads\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [paper.citationCount, \" citations\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                      children: \"View Abstract\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 25\n                    }, this), paper.status === 'published' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\",\n                      children: \"Download PDF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, paper.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(ResearchRepository, \"n7nXgnhet2OmIspjxuWBL1LBY28=\", false, function () {\n  return [useAuth];\n});\n_c = ResearchRepository;\nexport default ResearchRepository;\nvar _c;\n$RefreshReg$(_c, \"ResearchRepository\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "axios", "jsxDEV", "_jsxDEV", "ResearchRepository", "_s", "papers", "setPapers", "loading", "setLoading", "error", "setError", "filter", "setFilter", "selectedPaper", "setSelectedPaper", "showSubmissionForm", "setShowSubmissionForm", "newPaper", "setNewPaper", "title", "abstract", "category", "keywords", "content", "user", "token", "isAuthenticated", "isVerified", "API_URL", "process", "env", "REACT_APP_API_URL", "fetchPapers", "response", "get", "data", "err", "console", "handleSubmitPaper", "e", "preventDefault", "keywordsArray", "split", "map", "k", "trim", "post", "headers", "success", "getStatusColor", "status", "filteredPapers", "paper", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "length", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "replace", "authors", "join", "publishedDate", "Date", "toLocaleDateString", "keyword", "index", "downloadCount", "citationCount", "id", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/ResearchRepository.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst ResearchRepository = () => {\n  const [papers, setPapers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filter, setFilter] = useState('all');\n  const [selectedPaper, setSelectedPaper] = useState(null);\n  const [showSubmissionForm, setShowSubmissionForm] = useState(false);\n  const [newPaper, setNewPaper] = useState({\n    title: '',\n    abstract: '',\n    category: '',\n    keywords: '',\n    content: ''\n  });\n  const { user, token, isAuthenticated, isVerified } = useAuth();\n\n  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';\n\n  useEffect(() => {\n    fetchPapers();\n  }, []);\n\n  const fetchPapers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_URL}/api/research`);\n      setPapers(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch research papers');\n      console.error('Error fetching papers:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmitPaper = async (e) => {\n    e.preventDefault();\n    if (!isAuthenticated) {\n      setError('Please login to submit a paper');\n      return;\n    }\n\n    try {\n      const keywordsArray = newPaper.keywords.split(',').map(k => k.trim()).filter(k => k);\n\n      const response = await axios.post(\n        `${API_URL}/api/research`,\n        {\n          ...newPaper,\n          keywords: keywordsArray\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setNewPaper({ title: '', abstract: '', category: '', keywords: '', content: '' });\n        setShowSubmissionForm(false);\n        fetchPapers(); // Refresh the list\n      }\n    } catch (err) {\n      setError('Failed to submit paper');\n      console.error('Error submitting paper:', err);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'under_review':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const filteredPapers = papers.filter(paper => \n    filter === 'all' || paper.status === filter\n  );\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading research papers...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Research Repository</h1>\n          <p className=\"text-lg text-gray-600\">\n            Access peer-reviewed political research papers and submit your own work\n          </p>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-1/4\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Filters</h2>\n              \n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Status</label>\n                <select\n                  value={filter}\n                  onChange={(e) => setFilter(e.target.value)}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"all\">All Papers</option>\n                  <option value=\"published\">Published</option>\n                  <option value=\"under_review\">Under Review</option>\n                  <option value=\"rejected\">Rejected</option>\n                </select>\n              </div>\n\n              <button className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mb-4\">\n                Submit Paper\n              </button>\n\n              <div className=\"text-sm text-gray-600\">\n                <h3 className=\"font-medium mb-2\">Submission Guidelines</h3>\n                <ul className=\"space-y-1\">\n                  <li>• Original research only</li>\n                  <li>• Peer review required</li>\n                  <li>• APA citation format</li>\n                  <li>• Maximum 10,000 words</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:w-3/4\">\n            <div className=\"bg-white rounded-lg shadow-md\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Research Papers ({filteredPapers.length})\n                  </h2>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"text-gray-500 hover:text-gray-700\">\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n                      </svg>\n                    </button>\n                    <button className=\"text-gray-500 hover:text-gray-700\">\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"divide-y divide-gray-200\">\n                {filteredPapers.map((paper) => (\n                  <div key={paper.id} className=\"p-6 hover:bg-gray-50\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <h3 className=\"text-lg font-medium text-gray-900 flex-1 mr-4\">\n                        {paper.title}\n                      </h3>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`}>\n                        {paper.status.replace('_', ' ')}\n                      </span>\n                    </div>\n\n                    <p className=\"text-gray-600 mb-3 line-clamp-3\">\n                      {paper.abstract}\n                    </p>\n\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-3\">\n                      <span><strong>Authors: <AUTHORS>\n                      <span><strong>Category:</strong> {paper.category}</span>\n                      {paper.publishedDate && (\n                        <span><strong>Published:</strong> {new Date(paper.publishedDate).toLocaleDateString()}</span>\n                      )}\n                    </div>\n\n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {paper.keywords.map((keyword, index) => (\n                        <span key={index} className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">\n                          {keyword}\n                        </span>\n                      ))}\n                    </div>\n\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                        <span>{paper.downloadCount} downloads</span>\n                        <span>{paper.citationCount} citations</span>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                          View Abstract\n                        </button>\n                        {paper.status === 'published' && (\n                          <button className=\"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\">\n                            Download PDF\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResearchRepository;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAE9D,MAAM6B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAExEjC,SAAS,CAAC,MAAM;IACdkC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGN,OAAO,eAAe,CAAC;MAC3DtB,SAAS,CAAC2B,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ1B,QAAQ,CAAC,iCAAiC,CAAC;MAC3C2B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAE2B,GAAG,CAAC;IAC9C,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACd,eAAe,EAAE;MACpBhB,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IAEA,IAAI;MACF,MAAM+B,aAAa,GAAGxB,QAAQ,CAACK,QAAQ,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAClC,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAAC;MAEpF,MAAMX,QAAQ,GAAG,MAAMjC,KAAK,CAAC8C,IAAI,CAC/B,GAAGlB,OAAO,eAAe,EACzB;QACE,GAAGX,QAAQ;QACXK,QAAQ,EAAEmB;MACZ,CAAC,EACD;QACEM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUtB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIQ,QAAQ,CAACE,IAAI,CAACa,OAAO,EAAE;QACzB9B,WAAW,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QACjFP,qBAAqB,CAAC,KAAK,CAAC;QAC5BgB,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ1B,QAAQ,CAAC,wBAAwB,CAAC;MAClC2B,OAAO,CAAC5B,KAAK,CAAC,yBAAyB,EAAE2B,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,MAAMa,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,cAAc;QACjB,OAAO,+BAA+B;MACxC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,cAAc,GAAG9C,MAAM,CAACM,MAAM,CAACyC,KAAK,IACxCzC,MAAM,KAAK,KAAK,IAAIyC,KAAK,CAACF,MAAM,KAAKvC,MACvC,CAAC;EAED,IAAIJ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKmD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAKmD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FxD,OAAA;UAAGmD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IAAKmD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCpD,OAAA;MAAKmD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DpD,OAAA;QAAKmD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBpD,OAAA;UAAImD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ExD,OAAA;UAAGmD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9CpD,OAAA;UAAKmD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAImD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAErExD,OAAA;cAAKmD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBpD,OAAA;gBAAOmD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ExD,OAAA;gBACEyD,KAAK,EAAEhD,MAAO;gBACdiD,QAAQ,EAAGrB,CAAC,IAAK3B,SAAS,CAAC2B,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;gBAC3CN,SAAS,EAAC,iHAAiH;gBAAAC,QAAA,gBAE3HpD,OAAA;kBAAQyD,KAAK,EAAC,KAAK;kBAAAL,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCxD,OAAA;kBAAQyD,KAAK,EAAC,WAAW;kBAAAL,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxD,OAAA;kBAAQyD,KAAK,EAAC,cAAc;kBAAAL,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDxD,OAAA;kBAAQyD,KAAK,EAAC,UAAU;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxD,OAAA;cAAQmD,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EAAC;YAE9F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETxD,OAAA;cAAKmD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCpD,OAAA;gBAAImD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DxD,OAAA;gBAAImD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvBpD,OAAA;kBAAAoD,QAAA,EAAI;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjCxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxD,OAAA;UAAKmD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBpD,OAAA;YAAKmD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5CpD,OAAA;cAAKmD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CpD,OAAA;gBAAKmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpD,OAAA;kBAAImD,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,GAAC,mBACjC,EAACH,cAAc,CAACW,MAAM,EAAC,GAC1C;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAKmD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BpD,OAAA;oBAAQmD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eACnDpD,OAAA;sBAAKmD,SAAS,EAAC,SAAS;sBAACU,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAX,QAAA,eAC5EpD,OAAA;wBAAMgE,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAiC;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACTxD,OAAA;oBAAQmD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eACnDpD,OAAA;sBAAKmD,SAAS,EAAC,SAAS;sBAACU,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAX,QAAA,eAC5EpD,OAAA;wBAAMgE,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAsQ;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3U;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxD,OAAA;cAAKmD,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCH,cAAc,CAACR,GAAG,CAAES,KAAK,iBACxBlD,OAAA;gBAAoBmD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAClDpD,OAAA;kBAAKmD,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDpD,OAAA;oBAAImD,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAC1DF,KAAK,CAACjC;kBAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACLxD,OAAA;oBAAMmD,SAAS,EAAE,8CAA8CJ,cAAc,CAACG,KAAK,CAACF,MAAM,CAAC,EAAG;oBAAAI,QAAA,EAC3FF,KAAK,CAACF,MAAM,CAACoB,OAAO,CAAC,GAAG,EAAE,GAAG;kBAAC;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENxD,OAAA;kBAAGmD,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC3CF,KAAK,CAAChC;gBAAQ;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAEJxD,OAAA;kBAAKmD,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEpD,OAAA;oBAAAoD,QAAA,gBAAMpD,OAAA;sBAAAoD,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACN,KAAK,CAACmB,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjExD,OAAA;oBAAAoD,QAAA,gBAAMpD,OAAA;sBAAAoD,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACN,KAAK,CAAC/B,QAAQ;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACvDN,KAAK,CAACqB,aAAa,iBAClBvE,OAAA;oBAAAoD,QAAA,gBAAMpD,OAAA;sBAAAoD,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgB,IAAI,CAACtB,KAAK,CAACqB,aAAa,CAAC,CAACE,kBAAkB,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAC7F;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENxD,OAAA;kBAAKmD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvCF,KAAK,CAAC9B,QAAQ,CAACqB,GAAG,CAAC,CAACiC,OAAO,EAAEC,KAAK,kBACjC3E,OAAA;oBAAkBmD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9EsB;kBAAO,GADCC,KAAK;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENxD,OAAA;kBAAKmD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDpD,OAAA;oBAAKmD,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChEpD,OAAA;sBAAAoD,QAAA,GAAOF,KAAK,CAAC0B,aAAa,EAAC,YAAU;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CxD,OAAA;sBAAAoD,QAAA,GAAOF,KAAK,CAAC2B,aAAa,EAAC,YAAU;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACNxD,OAAA;oBAAKmD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpD,OAAA;sBAAQmD,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAAC;oBAE1E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRN,KAAK,CAACF,MAAM,KAAK,WAAW,iBAC3BhD,OAAA;sBAAQmD,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,EAAC;oBAEvF;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GA7CEN,KAAK,CAAC4B,EAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Cb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAhOID,kBAAkB;EAAA,QAc+BJ,OAAO;AAAA;AAAAkF,EAAA,GAdxD9E,kBAAkB;AAkOxB,eAAeA,kBAAkB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}