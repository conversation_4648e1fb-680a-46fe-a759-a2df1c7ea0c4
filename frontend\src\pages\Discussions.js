import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const Discussions = () => {
  const [threads, setThreads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedThread, setSelectedThread] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [showNewThreadForm, setShowNewThreadForm] = useState(false);
  const [newThread, setNewThread] = useState({
    title: '',
    description: '',
    category: ''
  });
  const { user, token, isAuthenticated } = useAuth();

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

  const fetchThreads = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/discussions`);
      setThreads(response.data.data || []);
    } catch (err) {
      setError('Failed to fetch discussions');
      console.error('Error fetching discussions:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    fetchThreads();
  }, [fetchThreads]);

  const fetchComments = async (threadId) => {
    try {
      const response = await axios.get(`${API_URL}/api/discussions/${threadId}`);
      setComments(response.data.data.comments || []);
    } catch (err) {
      console.error('Error fetching comments:', err);
    }
  };

  const handleThreadClick = (thread) => {
    setSelectedThread(thread);
    fetchComments(thread.id);
  };

  const handleCreateThread = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError('Please login to create a discussion');
      return;
    }

    try {
      const response = await axios.post(
        `${API_URL}/api/discussions`,
        newThread,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        setNewThread({ title: '', description: '', category: '' });
        setShowNewThreadForm(false);
        fetchThreads(); // Refresh the list
      }
    } catch (err) {
      setError('Failed to create discussion');
      console.error('Error creating discussion:', err);
    }
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError('Please login to comment');
      return;
    }

    try {
      const response = await axios.post(
        `${API_URL}/api/comments`,
        {
          discussion_id: selectedThread.id,
          content: newComment
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        setNewComment('');
        fetchComments(selectedThread.id); // Refresh comments
      }
    } catch (err) {
      setError('Failed to add comment');
      console.error('Error adding comment:', err);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading discussions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchThreads}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // If viewing a specific thread
  if (selectedThread) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setSelectedThread(null)}
              className="text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Discussions
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{selectedThread.title}</h1>
            <p className="text-gray-600 mb-4">{selectedThread.description}</p>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span className="bg-gray-100 px-2 py-1 rounded">{selectedThread.category}</span>
              <span>by {selectedThread.author_username}</span>
              <span>Created: {formatDate(selectedThread.created_at)}</span>
            </div>
          </div>

          {/* Comments Section */}
          <div className="bg-white rounded-lg shadow-md mb-6">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Comments</h2>
            </div>
            <div className="p-6">
              {comments.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No comments yet. Be the first to comment!</p>
              ) : (
                <div className="space-y-6">
                  {comments.map((comment) => (
                    <div key={comment.id} className="border-l-4 border-blue-200 pl-4">
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium text-gray-900">{comment.author_username}</span>
                        <span className="text-sm text-gray-500">{formatDate(comment.created_at)}</span>
                      </div>
                      <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
                      {comment.upvotes > 0 && (
                        <div className="mt-2 text-sm text-gray-500">
                          👍 {comment.upvotes} upvotes
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Add Comment Form */}
          {isAuthenticated ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add a Comment</h3>
              <form onSubmit={handleAddComment}>
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Share your thoughts..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="4"
                  required
                />
                <div className="mt-4 flex justify-end">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                  >
                    Post Comment
                  </button>
                </div>
              </form>
            </div>
          ) : (
            <div className="bg-gray-100 rounded-lg p-6 text-center">
              <p className="text-gray-600 mb-4">Please login to participate in the discussion</p>
              <a href="/login" className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                Login
              </a>
            </div>
          )}
        </div>
      </div>
    );
  }

  // New Thread Form
  if (showNewThreadForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setShowNewThreadForm(false)}
              className="text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Discussions
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Start New Discussion</h1>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <form onSubmit={handleCreateThread}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Discussion Title
                </label>
                <input
                  type="text"
                  id="title"
                  value={newThread.title}
                  onChange={(e) => setNewThread({ ...newThread, title: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter a clear, descriptive title"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category"
                  value={newThread.category}
                  onChange={(e) => setNewThread({ ...newThread, category: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a category</option>
                  <option value="Elections">Elections</option>
                  <option value="Policy">Policy</option>
                  <option value="Campaign Finance">Campaign Finance</option>
                  <option value="Constitutional Law">Constitutional Law</option>
                  <option value="International Relations">International Relations</option>
                  <option value="Local Government">Local Government</option>
                  <option value="General Discussion">General Discussion</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  value={newThread.description}
                  onChange={(e) => setNewThread({ ...newThread, description: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="6"
                  placeholder="Provide context and details for your discussion topic"
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowNewThreadForm(false)}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Discussion
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // Main discussions list view
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Discussion Forums</h1>
          <p className="text-lg text-gray-600">
            Engage in thoughtful political discussions with the community
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md">
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Recent Discussions</h2>
              {isAuthenticated ? (
                <button
                  onClick={() => setShowNewThreadForm(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Start New Discussion
                </button>
              ) : (
                <a
                  href="/login"
                  className="bg-gray-400 text-white px-4 py-2 rounded-md cursor-not-allowed"
                  title="Login required"
                >
                  Login to Post
                </a>
              )}
            </div>
          </div>

          <div className="divide-y divide-gray-200">
            {threads.length === 0 ? (
              <div className="p-12 text-center">
                <div className="text-gray-400 text-6xl mb-4">💬</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No discussions yet</h3>
                <p className="text-gray-600 mb-4">Be the first to start a political discussion!</p>
                {isAuthenticated && (
                  <button
                    onClick={() => setShowNewThreadForm(true)}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                  >
                    Start First Discussion
                  </button>
                )}
              </div>
            ) : (
              threads.map((thread) => (
                <div
                  key={thread.id}
                  className="p-6 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleThreadClick(thread)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {thread.title}
                      </h3>
                      <p className="text-gray-600 mb-3">{thread.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="bg-gray-100 px-2 py-1 rounded">{thread.category}</span>
                        <span>by {thread.author_username}</span>
                        <span>Created: {formatDate(thread.created_at)}</span>
                        {thread.comment_count > 0 && (
                          <span>{thread.comment_count} comments</span>
                        )}
                      </div>
                    </div>
                    <div className="ml-4">
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Discussions;
