import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const ProfessionalHeader = ({ viewMode, setViewMode, isConnected, user }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { logout } = useAuth();

  const viewModes = [
    { key: 'feed', label: '📱 Feed', icon: '🏠' },
    { key: 'analytics', label: '📊 Analytics', icon: '📈' },
    { key: 'scheduler', label: '⏰ Scheduler', icon: '📅' },
    { key: 'events', label: '🎪 Events', icon: '🗓️' }
  ];

  const notifications = [
    { id: 1, type: 'like', message: '<PERSON> liked your post about climate policy', time: '2m ago', unread: true },
    { id: 2, type: 'comment', message: '<PERSON> commented on your healthcare debate', time: '5m ago', unread: true },
    { id: 3, type: 'follow', message: '<PERSON><PERSON><PERSON><PERSON> started following you', time: '1h ago', unread: false },
    { id: 4, type: 'mention', message: 'You were mentioned in a discussion', time: '2h ago', unread: false }
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className="bg-white border-b border-secondary-200 sticky top-0 z-fixed">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          
          {/* Logo and Brand */}
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">P</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-secondary-900">Politica</h1>
                <p className="text-xs text-secondary-500">Professional Political Platform</p>
              </div>
            </div>

            {/* View Mode Tabs */}
            <nav className="hidden md:flex items-center gap-1 bg-secondary-100 rounded-xl p-1">
              {viewModes.map(mode => (
                <button
                  key={mode.key}
                  onClick={() => setViewMode(mode.key)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    viewMode === mode.key
                      ? 'bg-white text-primary-600 shadow-sm'
                      : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'
                  }`}
                >
                  <span>{mode.icon}</span>
                  <span className="hidden lg:inline">{mode.label.split(' ')[1]}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center gap-4">
            
            {/* Connection Status */}
            <div className="hidden sm:flex items-center gap-2 px-3 py-1 rounded-full bg-secondary-100">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'} animate-pulse`}></div>
              <span className="text-xs font-medium text-secondary-600">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>

            {/* Search */}
            <div className="hidden md:block relative">
              <input
                type="text"
                placeholder="Search politics..."
                className="form-input w-64 pl-10 pr-4 py-2 text-sm"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400">
                🔍
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-2">
              <button className="btn btn-ghost btn-sm">
                💬
              </button>
              <button className="btn btn-ghost btn-sm">
                🔖
              </button>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="btn btn-ghost btn-sm relative"
              >
                🔔
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-error-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </button>

              {showNotifications && (
                <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up">
                  <div className="p-4 border-b border-secondary-100">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-secondary-900">Notifications</h3>
                      <button className="text-xs text-primary-600 hover:text-primary-700">
                        Mark all read
                      </button>
                    </div>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.map(notification => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-secondary-50 hover:bg-secondary-25 cursor-pointer ${
                          notification.unread ? 'bg-primary-25' : ''
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm">
                            {notification.type === 'like' && '👍'}
                            {notification.type === 'comment' && '💬'}
                            {notification.type === 'follow' && '👥'}
                            {notification.type === 'mention' && '📢'}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm text-secondary-900">{notification.message}</p>
                            <p className="text-xs text-secondary-500 mt-1">{notification.time}</p>
                          </div>
                          {notification.unread && (
                            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="p-4 border-t border-secondary-100">
                    <button className="w-full text-center text-sm text-primary-600 hover:text-primary-700">
                      View all notifications
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-3 p-2 rounded-xl hover:bg-secondary-100 transition-colors"
              >
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-secondary-900">{user?.username || 'User'}</p>
                  <p className="text-xs text-secondary-500">
                    {user?.role === 'admin' && '👑 Admin'}
                    {user?.role === 'moderator' && '🛡️ Moderator'}
                    {user?.verified_status === 'verified' && '✅ Verified'}
                    {!user?.role && !user?.verified_status && '👤 Member'}
                  </p>
                </div>
                <span className="text-secondary-400">▼</span>
              </button>

              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-xl border border-secondary-200 z-dropdown animate-slide-up">
                  <div className="p-4 border-b border-secondary-100">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold">
                        {user?.username?.charAt(0).toUpperCase() || 'U'}
                      </div>
                      <div>
                        <p className="font-semibold text-secondary-900">{user?.username || 'User'}</p>
                        <p className="text-sm text-secondary-500">{user?.email}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-2">
                    <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left">
                      <span>👤</span>
                      <span className="text-sm">Profile</span>
                    </button>
                    <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left">
                      <span>⚙️</span>
                      <span className="text-sm">Settings</span>
                    </button>
                    <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left">
                      <span>📊</span>
                      <span className="text-sm">Analytics</span>
                    </button>
                    <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left">
                      <span>🎨</span>
                      <span className="text-sm">Appearance</span>
                    </button>
                    <hr className="my-2 border-secondary-100" />
                    <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-secondary-100 text-left">
                      <span>❓</span>
                      <span className="text-sm">Help & Support</span>
                    </button>
                    <button 
                      onClick={logout}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-error-50 hover:text-error-600 text-left"
                    >
                      <span>🚪</span>
                      <span className="text-sm">Sign Out</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile View Mode Selector */}
      <div className="md:hidden border-t border-secondary-200 bg-secondary-50">
        <div className="flex items-center justify-around py-2">
          {viewModes.map(mode => (
            <button
              key={mode.key}
              onClick={() => setViewMode(mode.key)}
              className={`flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all ${
                viewMode === mode.key
                  ? 'text-primary-600 bg-primary-50'
                  : 'text-secondary-600'
              }`}
            >
              <span className="text-lg">{mode.icon}</span>
              <span>{mode.label.split(' ')[1]}</span>
            </button>
          ))}
        </div>
      </div>
    </header>
  );
};

export default ProfessionalHeader;
